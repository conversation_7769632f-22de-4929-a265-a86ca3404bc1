/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  FlagAnimator
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/8/14
 * * Author      : Tian<PERSON>un
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.wavemark.wave.view

import android.animation.ValueAnimator
import android.graphics.Canvas
import android.graphics.Paint
import androidx.core.animation.doOnEnd
import androidx.core.content.ContextCompat
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.wavemark.R
import com.soundrecorder.wavemark.wave.WaveViewUtil
import kotlin.math.roundToInt

class BookmarkAnimator(var itemView: WaveItemView) {
    companion object {
        const val TAG = "BookmarkAnimator"
        const val ANIM_DURATION = 180L //ms
        const val ANIM_REMOVE_LINE_DELAY = 50L //ms
        const val ALPHA = 255
        const val PERCENT_30 = 0.3F
        const val FLOAT_0 = 0F
        const val FLOAT_1 = 1F
        const val FLOAT_2 = 2F
        const val INT_2 = 2
    }

    private val mAddFlagPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val mRemoveFlagPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val mRemoveLinePaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private var mAnimAddFlag: ValueAnimator? = null
    private var mAnimRemoveFlag: ValueAnimator? = null

    init {
        mRemoveLinePaint.strokeWidth = itemView.context.resources.getDimension(R.dimen.wave_mark_line_stroke_width)
        mRemoveLinePaint.color = ContextCompat.getColor(itemView.context, com.support.appcompat.R.color.coui_color_label_theme_blue)
        mRemoveLinePaint.alpha = (PERCENT_30 * ALPHA).roundToInt()
    }

    fun getFlagPaint(mark: MarkDataBean): Paint {
        val waveRecyclerView = itemView.parent as? WaveRecyclerView<*>
        val addMarkData = waveRecyclerView?.addMarkData ?: return itemView.bookmarksPaint
        return if (addMarkData.correctTime == mark.correctTime) {
            if (mAnimAddFlag != null) {
                return mAddFlagPaint
            }
            doAddFlagAnim()
            waveRecyclerView.removeMarkData = null
            mAddFlagPaint
        } else {
            itemView.bookmarksPaint
        }
    }

    private fun doAddFlagAnim(): ValueAnimator? {
        if (mAnimAddFlag != null) {
            DebugUtil.i(TAG, "doAddFlagAnim exist.")
            return mAnimAddFlag
        }
        DebugUtil.i(TAG, "doAddFlagAnim")
        mAnimAddFlag = ValueAnimator.ofFloat(FLOAT_0, FLOAT_1).apply {
            duration = ANIM_DURATION
            interpolator = COUIEaseInterpolator()
            addUpdateListener { anim: ValueAnimator ->
                val alpha = (anim.animatedValue as Float * ALPHA).toInt()
                mAddFlagPaint.alpha = alpha
                itemView.postInvalidateOnAnimation()
            }
            doOnEnd {
                val waveRecyclerView = itemView.parent as? WaveRecyclerView<*>
                waveRecyclerView?.addMarkData = null
                mAnimAddFlag = null
            }
            start()
        }
        return mAnimAddFlag
    }

    fun drawRemoveAnimIfNeed(
        canvas: Canvas,
        startTime: Long,
        isReverseLayout: Boolean,
        markViewHeight: Int,
        viewHeight: Float
    ): Boolean {
        val waveRecyclerView = itemView.parent as? WaveRecyclerView<*>
        val mark = waveRecyclerView?.removeMarkData ?: return false
        val markTime: Long = mark.correctTime
        val pxPerMs = itemView.pxPerMs
        //删除标记的item绘制删除动效
        if ((startTime >= 0) && (startTime < markTime) && (markTime - startTime <= WaveViewUtil.ONE_WAVE_VIEW_DURATION)) {
            val startX: Int = ((markTime - startTime) * pxPerMs).toInt()
            var newStartX = startX.toFloat()
            if (isReverseLayout) {
                newStartX = itemView.width - newStartX
            }
            //draw line
            val lineMarkX = newStartX
            canvas.drawLine(lineMarkX, markViewHeight.toFloat(), lineMarkX, viewHeight, mRemoveLinePaint)
            createRemoveAnim()
            return true
        }
        return false
    }

    private fun createRemoveAnim() {
        if (mAnimRemoveFlag != null) {
            return
        }
        DebugUtil.i(TAG, "doRemoveFlagAnim")
        mAnimRemoveFlag = ValueAnimator.ofFloat(FLOAT_1, FLOAT_0).apply {
            duration = ANIM_DURATION
            interpolator = COUIEaseInterpolator()
            addUpdateListener { anim: ValueAnimator ->
                val alpha = anim.animatedValue as Float
                val alphaFlag = (alpha * ALPHA).roundToInt()
                mRemoveFlagPaint.alpha = alphaFlag

                val alphaLine = (alpha * PERCENT_30).roundToInt()
                mRemoveLinePaint.alpha = alphaLine
                itemView.postInvalidateOnAnimation()
            }
            doOnEnd {
                DebugUtil.i(TAG, "doRemoveFlagAnim doOnEnd")
                val waveRecyclerView = itemView.parent as? WaveRecyclerView<*>
                waveRecyclerView?.removeMarkData = null
                mAnimRemoveFlag = null
            }
            start()
        }
    }

    fun release() {
        mAnimAddFlag?.cancel()
        mAnimRemoveFlag?.cancel()
    }
}