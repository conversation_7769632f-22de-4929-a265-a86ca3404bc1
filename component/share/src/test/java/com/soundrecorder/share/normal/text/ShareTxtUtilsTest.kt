/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.share.normal.text

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import androidx.lifecycle.lifecycleScope
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.permission.PermissionProxyActivity
import kotlinx.coroutines.launch
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNotNull
import org.junit.Assert.assertNull
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowApplication
import org.robolectric.shadows.ShadowLog

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S])
class ShareTxtUtilsTest {

    private var context: Context? = null
    private var controller: ActivityController<PermissionProxyActivity>? = null

    @Before
    fun setUp() {
        ShadowLog.stream = System.out
        context = BaseApplication.getAppContext()
        controller = Robolectric.buildActivity(PermissionProxyActivity::class.java)
    }

    @After
    fun tearDown() {
        controller = null
        context = null
    }

    @Test
    fun executeAsyncShareTxt() {
        val activity = controller?.get()
        activity?.lifecycleScope?.launch {
            val textFile = ShareTxtUtils.getShareTextFile(activity, "", "")
            ShareTxtUtils.doExport(activity, null, textFile)
        }
        assertNull(Whitebox.getInternalState(ShareTxtUtils, "shareTxtCallback"))
        activity?.lifecycleScope?.launch {
            val textFile = ShareTxtUtils.getShareTextFile(activity, "test", "")
            ShareTxtUtils.doExport(activity, null, textFile)
        }
        assertEquals(activity, Whitebox.getInternalState(ShareTxtUtils, "shareTxtCallback"))
    }

    @Test
    fun doExport() {
        val activity = controller?.create()?.get()
        val uri = Mockito.mock(Uri::class.java)
        ShareTxtUtils.doExport(activity, null, null)
        var actual = ShadowApplication.getInstance().nextStartedActivity
        assertNull(actual)
        ShareTxtUtils.doExport(activity, null, uri)
        actual = ShadowApplication.getInstance().nextStartedActivity
        assertNotNull(actual)
        assertEquals(Intent.ACTION_CHOOSER, actual.action)
    }
}