/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - ShareLinkUtils.kt
 ** Description: ShareLinkUtils.
 ** Version: 1.0
 ** Date : 2025/3/20
 ** Author: zhangmeng
 **
 ** ---------------------Revision History: ---------------------
 ** <author> <data> <version > <desc>
 ** zhangmeng    2025/3/20    1.0    create
 ****************************************************************/
package com.soundrecorder.share.normal.link.utils

import android.app.Activity
import android.content.Intent
import android.view.View
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.flexible.FollowHandDialogUtils

object ShareLinkUtils {
    private const val TAG = "ShareLinkUtils"
    private const val TXT_MIMETYPE = "text/plain"

    @JvmStatic
    fun showShareLinkPanel(activity: Activity, link: String, anchor: View?) {
        DebugUtil.d(TAG, "showShareLinkPanel")
        val intent = Intent(Intent.ACTION_SEND)
        intent.type = TXT_MIMETYPE
        intent.putExtra(Intent.EXTRA_TEXT, link)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        FollowHandDialogUtils.addShareDialogAnchor(anchor, intent)
        val chooserIntent = Intent.createChooser(intent, activity.getString(com.soundrecorder.common.R.string.share))
        activity.startActivity(chooserIntent)
    }
}