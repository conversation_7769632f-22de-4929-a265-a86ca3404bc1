/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: ConvertRecordXmlParser
 ** Description:for parse convert record xml file.
 ** Version:1.0
 ** Date :2019-10-11
 ** Author: tianjun
 **
 ** v1.0, 2019-10-11, tianjun, create
 ****************************************************************/
package com.recorder.movepure;

import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;
import org.xmlpull.v1.XmlPullParserFactory;

import java.io.IOException;
import java.io.StringReader;
import java.util.HashSet;

import com.soundrecorder.common.constant.DatabaseConstant;
import com.soundrecorder.common.databean.ConvertRecord;
import com.soundrecorder.base.utils.DebugUtil;

public class ConvertRecordXmlParser {
    private final static String TAG = ConvertRecordXmlParser.class.getSimpleName();

    public static HashSet<ConvertRecord> parse(String recordString) {
        ConvertRecord record = null;
        HashSet<ConvertRecord> list = new HashSet<>();
        try {
            XmlPullParserFactory factory = XmlPullParserFactory.newInstance();
            XmlPullParser parser = factory.newPullParser();
            parser.setInput(new StringReader(recordString));

            int eventType = parser.getEventType();
            String tagName = "";
            while (eventType != XmlPullParser.END_DOCUMENT) {
                switch (eventType) {
                    case XmlPullParser.START_DOCUMENT:
                        break;
                    case XmlPullParser.START_TAG:
                        record = new ConvertRecord();
                        tagName = parser.getName();
                        if (tagName.equals(DatabaseConstant.ROOT)) {
                            int attrNum = parser.getAttributeCount();
                            for (int i = 0; i < attrNum; ++i) {
                                String name = parser.getAttributeName(i);
                                String value = parser.getAttributeValue(i);
                                switch (name) {
                                    case DatabaseConstant.ConvertColumn.MEDIA_PATH:
                                        record.setMediaPath(value);
                                        break;
                                    case DatabaseConstant.ConvertColumn.CONVERT_TEXTFILE_PATH:
                                        record.setConvertTextfilePath(value);
                                        break;
                                    case DatabaseConstant.ConvertColumn.CHUNK_NAME:
                                        record.setChunkName(value);
                                        break;
                                    case DatabaseConstant.ConvertColumn.COMPLETE_STATUS:
                                        record.setCompleteStatus(Integer.parseInt(value));
                                        break;
                                    case DatabaseConstant.ConvertColumn.ONLY_ID:
                                        record.setOnlyId(value);
                                        break;
                                    case DatabaseConstant.ConvertColumn.VERSION:
                                        record.setVersion(Integer.parseInt(value));
                                        break;
                                    case DatabaseConstant.ConvertColumn.TASKID:
                                        record.setTaskId(value);
                                        break;
                                    case DatabaseConstant.ConvertColumn.UPLOAD_REQUEST_ID:
                                        record.setUploadRequestId(value);
                                        break;
                                    case DatabaseConstant.ConvertColumn.UPLOAD_KEY:
                                        record.setUploadKey(value);
                                        break;
                                    case DatabaseConstant.ConvertColumn.PART_COUNT:
                                        record.setPartCount(Integer.parseInt(value));
                                        break;
                                    case DatabaseConstant.ConvertColumn.UPLOAD_STATUS:
                                        record.setUploadStatus(Integer.parseInt(value));
                                        break;
                                    case DatabaseConstant.ConvertColumn.CONVERT_STATUS:
                                        record.setConvertStatus(Integer.parseInt(value));
                                        break;
                                    case DatabaseConstant.ConvertColumn.UPLOAD_ALL_URL:
                                        record.setUploadAllUrl(value);
                                        break;

                                    case DatabaseConstant.ConvertColumn.HISTORY_ROLENAME:
                                        record.setHistoryRoleName(value);
                                        break;
                                    case DatabaseConstant.ConvertColumn.SERVER_PLAN_CODE:
                                        record.setServerPlanCode(Integer.parseInt(value));
                                        break;
                                    case DatabaseConstant.ConvertColumn.CAN_SHOW_SPEAKER_ROLE:
                                        record.setCanShowSpeakerRole(Integer.parseInt(value));
                                        break;
                                    case DatabaseConstant.ConvertColumn.SPEAKER_ROLE_ISSHOWING:
                                        record.setSpeakerRoleIsShowing(Integer.parseInt(value));
                                        break;
                                    case DatabaseConstant.ConvertColumn.SPEAKER_ROLE_ORIGINAL_NUMBER:
                                        record.setSpeakerRoleOriginalNumber(Integer.parseInt(value));
                                        break;
                                    case DatabaseConstant.ConvertColumn.SPEAKER_ROLE_HAS_FIRSTSHOW:
                                        record.setSpeakerRoleHasFirstshow(Integer.parseInt(value));
                                        break;
                                    case DatabaseConstant.ConvertColumn.IS_DIRECT_ON:
                                        record.setIsDirectOn(Boolean.parseBoolean(value));
                                        break;
                                    case DatabaseConstant.ConvertColumn.DIRECT_TIME:
                                        record.setDirectTime(value);
                                        break;
                                    default:
                                        break;
                                }
                            }
                        }
                        break;

                    case XmlPullParser.END_TAG:
                        if (parser.getName().equals(DatabaseConstant.ROOT) && (record != null)) {
                            list.add(record);
                            DebugUtil.i(TAG, "parse ConvertRecord: " + record);
                        }
                        break;
                    default:
                        break;
                }

                eventType = parser.next();
            }
        } catch (XmlPullParserException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }

        return list;
    }
}