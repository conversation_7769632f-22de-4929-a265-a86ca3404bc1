/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AudioModeChangeManagerTest
 Description:
 Version: 1.0
 Date: 2022/10/9
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/10/9 1.0 create
 */

package com.soundrecorder.recorderservice.manager

import android.content.Context
import android.media.AudioManager
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.recorderservice.manager.listener.AudioModeChangeListener
import com.soundrecorder.recorderservice.shadows.ShadowFeatureOption
import com.soundrecorder.recorderservice.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class AudioModeChangeManagerTest {

    private var mContext: Context? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
    }

    @Test
    fun should_notNull_when_audioManagerModeChange() {
        AudioModeChangeManager.audioManagerModeChange()
        val mAudioManager = Whitebox.getInternalState<AudioManager>(AudioModeChangeManager::class.java, "mAudioManager")
        Assert.assertNotNull(mAudioManager)
    }

    @Test
    fun should_notNull_when_dealMode() {
        val mAudioModeChangeManager = Mockito.mock(AudioModeChangeManager::class.java)
        Whitebox.invokeMethod<Any>(mAudioModeChangeManager, "dealMode", 0)
        Whitebox.invokeMethod<Any>(mAudioModeChangeManager, "dealMode", 1)
        Whitebox.invokeMethod<Any>(mAudioModeChangeManager, "dealMode", 2)
        Whitebox.invokeMethod<Any>(mAudioModeChangeManager, "dealMode", 3)
    }

    @Test
    fun should_notNull_when_checkModeCanRecord() {
        val boo = AudioModeChangeManager.checkModeCanRecord(true)
        Assert.assertTrue(boo)
    }

    @Test
    fun should_notNull_when_registerChangeListener() {
        val audioModeChangeListener = object : AudioModeChangeListener {
            override fun onAudioModeChanged(audioMode: Int) {
            }
        }
        AudioModeChangeManager.registerChangeListener(audioModeChangeListener)
        val mAudioModeChangeListener = Whitebox.getInternalState<AudioModeChangeListener>(
            AudioModeChangeManager::class.java,
            "mAudioModeChangeListener"
        )
        Assert.assertNotNull(mAudioModeChangeListener)
    }

    @Test
    fun should_notNull_when_changeAudioPause() {
        AudioModeChangeManager.changeAudioPause(true)
        val isAudioChangePause = Whitebox.getInternalState<Boolean>(
            AudioModeChangeManager::class.java,
            "mIsAudioChangePause"
        )
        Assert.assertTrue(isAudioChangePause)

        AudioModeChangeManager.changeAudioPause(false)
        val isAudioChangePause1 = Whitebox.getInternalState<Boolean>(
            AudioModeChangeManager::class.java,
            "mIsAudioChangePause"
        )
        Assert.assertTrue(!isAudioChangePause1)
    }

    @Test
    fun should_notNull_when_changeNeedResume() {
        AudioModeChangeManager.changeNeedResume(true)
        val isNeedResume = Whitebox.getInternalState<Boolean>(
            AudioModeChangeManager::class.java,
            "mIsNeedResume"
        )
        Assert.assertTrue(isNeedResume)

        AudioModeChangeManager.changeNeedResume(false)
        val isNeedResume1 = Whitebox.getInternalState<Boolean>(
            AudioModeChangeManager::class.java,
            "mIsNeedResume"
        )
        Assert.assertTrue(!isNeedResume1)
    }

    @Test
    fun should_notNull_when_changeLastBtnDisabled() {
        AudioModeChangeManager.changeLastBtnDisabled(true)
        val lastBtnDisabled = Whitebox.getInternalState<Boolean>(
            AudioModeChangeManager::class.java,
            "mLastBtnDisabled"
        )
        Assert.assertTrue(lastBtnDisabled)

        AudioModeChangeManager.changeLastBtnDisabled(false)
        val lastBtnDisabled1 = Whitebox.getInternalState<Boolean>(
            AudioModeChangeManager::class.java,
            "mLastBtnDisabled"
        )
        Assert.assertTrue(!lastBtnDisabled1)
    }

    @Test
    fun should_notNull_when_release() {
        AudioModeChangeManager.release()
    }

    @Test
    fun should_notNull_when_getLastBtnDisabled() {
        Assert.assertFalse(AudioModeChangeManager.getLastBtnDisabled())
    }

    @After
    fun tearDown() {
        mContext = null
    }
}