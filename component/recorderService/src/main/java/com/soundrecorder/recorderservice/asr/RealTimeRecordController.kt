/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RealTimeRecordController
 * Description:
 * Version: 1.0
 * Date: 2025/3/4
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2025/3/4 1.0 create
 */

package com.soundrecorder.recorderservice.asr

import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.HandlerThread
import android.os.Message
import androidx.core.os.bundleOf
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.LanguageUtil
import com.soundrecorder.base.utils.NetworkUtils
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.common.constant.RecorderConstant
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.databean.ConvertRecord
import com.soundrecorder.common.db.CollectionInfoDbUtils
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.realtimeasr.IRealtimeSubtitleCache
import com.soundrecorder.common.realtimeasr.OnRealtimeListener
import com.soundrecorder.common.realtimeasr.RealTimeAsrStatus
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.modulerouter.convertService.ConvertServiceInterface
import com.soundrecorder.modulerouter.smartname.SmartNameAction
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.recorderservice.api.RecorderViewModelApi
import com.soundrecorder.recorderservice.manager.RecorderViewModel
import com.soundrecorder.recorderservice.recorder.IRealTimeRecordListener
import com.soundrecorder.recorderservice.recorder.RealTimeAudioRecorder
import com.soundrecorder.recorderservice.recorder.RealTimeAudioRecorder.Companion.SAMPLE_RATE
import com.soundrecorder.translate.AIAsrManager
import com.soundrecorder.translate.asr.RealtimeAsrCallback
import com.soundrecorder.translate.asr.RealtimeAsrManager
import com.soundrecorder.translate.asr.RealtimeOffsetManager
import com.soundrecorder.translate.asr.realtime.AIRealTimeAsrClient.Companion.EXTRA_CHANNEL_ID
import com.soundrecorder.translate.asr.realtime.AIRealTimeAsrClient.Companion.EXTRA_FILE_RECORD_ID
import com.soundrecorder.translate.asr.realtime.AIRealTimeAsrClient.Companion.EXTRA_FORMAT
import com.soundrecorder.translate.asr.realtime.AIRealTimeAsrClient.Companion.EXTRA_SAMPLE_BYTES
import com.soundrecorder.translate.asr.realtime.AIRealTimeAsrClient.Companion.EXTRA_SAMPLE_CHANNEL
import com.soundrecorder.translate.asr.realtime.AIRealTimeAsrClient.Companion.EXTRA_SAMPLE_RATE
import com.soundrecorder.translate.asr.realtime.AIRealTimeAsrClient.Companion.EXTRA_SOURCE_LANGUAGE
import com.soundrecorder.translate.asr.realtime.RealTimeAsrParser.Companion.ERROR_NET_DISCONNECT
import com.soundrecorder.translate.asr.realtime.RealTimeAsrParser.Companion.STATUS_INIT_ERROR
import java.util.UUID
import java.util.concurrent.CopyOnWriteArrayList
import kotlin.math.roundToLong

class RealTimeRecordController() : IRealTimeAsrController, IRealTimeRecordListener {
    companion object {
        private const val TAG = "RealTimeRecordController"
        private const val MSG_START_RECORDER = 0X1
        private const val MSG_RESUME_RECORDER = 0X6
        private const val MSG_UPLOAD_DATA = 0X2
        private const val MSG_CANCEL_RECORD = 0X3
        private const val MSG_SAVE_RECORD = 0X4
        private const val MSG_PAUSE_RECORD = 0X7
        private const val MSG_RELEASE = 0X8
        private const val MSG_STOP_ASR = 0X9
        private const val MSG_START_TRANSLATION_CONFIG = 0X10
        private const val MSG_EXTERNAL_INIT_ASR = 0X11
        private const val MSG_EXTERNAL_STOP_ASR = 0X12

        // 空包超时时间
        private const val SEND_EMPTY_TIMEOUT = 10 * 60 * 1000L

        // 空包任务循环间隔时间
        private const val SEND_EMPTY_TASK_INTERVAL = 2000L

        // ASR识别的音频流格式
        private const val ASR_EXTRA_FORMAT = "pcm"

        // ASR的位深，每个采样的字节数
        private const val ASR_EXTRA_SAMPLE_BYTES = 2
    }

    private var mHandlerThread: HandlerThread? = null
    private var mWorkHandler: Handler? = null
    private var recorder: RealTimeAudioRecorder? = null
    private var convertManager: RealtimeAsrManager? = null
    private var asrResultCallback: IRealTimeAsrResultCallback? = null
    private var offsetManager: RealtimeOffsetManager? = null
    private var saveName: String? = null
    private var saveUri: Uri? = null

    /**startAsr 成功之前，记录录制数据*/
    private val waitingByteList = CopyOnWriteArrayList<ByteArray>()

    /*记录需要释放的channelId*/
    private val needReleaseChannelIdList = CopyOnWriteArrayList<String>()

    /*服务端返回原始错误码*/
    private var originErrorCode = 0

    /*转成ui层使用的2个错误码,网络异常or服务异常*/
    private var formatErrorCode = 0

    @Volatile
    private var channelId = ""

    /*是否取消：true：取消 false：未取消*/
    @Volatile
    private var cancelFlag = false

    /*记录是否第一次初始化*/
    private var isFirstInitFlag = true

    // 空包累计大小
    private var emptyPacketSize = 0L

    // 记录本次发送空包的时间，用于超时判断
    private var sendEmptyTime = 0L

    /*记录实时转写过程中，是否出现中断*/
    private var isASRInterrupted = false

    /*ASR支持的语种*/
    private val asrSupportLanguage = mutableListOf<String>()

    private val smartNameAction by lazy {
        Injector.injectFactory<SmartNameAction>()
    }

    // 发送空包的任务
    private val emptyTask = object : Runnable {
        override fun run() {
            if (convertManager?.isStopAsr(channelId) == true) {
                DebugUtil.i(TAG, "emptyTask stop asr")
                return
            }
            val emptyPacket = recorder?.getBufferSize()?.let { ByteArray(it) } ?: return
            sendAudioDataMessage(channelId, emptyPacket)
            // 计算空包发送时间
            sendEmptyTime += SEND_EMPTY_TASK_INTERVAL
            // 计算空包音频数据总字节数
            emptyPacketSize += emptyPacket.size
            DebugUtil.i(TAG, "emptyTask ing: sendEmptyTime=$sendEmptyTime, emptyPacketSize=$emptyPacketSize")
            if (sendEmptyTime >= SEND_EMPTY_TIMEOUT) {
                clearAsrOffsetVar()
                // 超时,停止ASR
                convertManager?.stopAsr(channelId)
                waitingByteList.clear()
            } else {
                mWorkHandler?.postDelayed(this, SEND_EMPTY_TASK_INTERVAL)
            }
        }
    }

    init {
        checkManagerInit()
    }

    private val convertServiceApi by lazy {
        Injector.injectFactory<ConvertServiceInterface>()
    }

    override fun initHandlerThread() {
        DebugUtil.i(TAG, "initHandlerThread")
        mHandlerThread = HandlerThread("RealTimeAsr")
        mHandlerThread?.start()
        mHandlerThread?.looper?.let {
            mWorkHandler = object : Handler(it) {
                // 消息处理的操作
                override fun handleMessage(msg: Message) {
                    when (msg.what) {
                        MSG_START_RECORDER -> {
                            DebugUtil.i(TAG, "handleMessage START ASR")
                            val context = BaseApplication.getAppContext()
                            if (NetworkUtils.isNetworkInvalid(context) && !AIAsrManager.canRunRealTimeAsr(context)) {
                                DebugUtil.i(TAG, "not support or no internet")
                                return
                            }
                            resetChannelId()
                            if (convertManager == null || offsetManager == null) {
                                checkManagerInit()
                            }
                            if (!cancelFlag) {
                                /*启动recorder，获取PCM数据*/
                                startRecorder(false, channelId)
                                offsetManager?.mainRecordingTime = getAdjustedMainRecordingTime()
                                convertManager?.initAsr(bundleOf(EXTRA_CHANNEL_ID to channelId))
                            }
                        }

                        MSG_RESUME_RECORDER -> {
                            doResumeRecorder()
                        }

                        MSG_UPLOAD_DATA -> {
                            if (!cancelFlag) {
                                (msg.obj as? Bundle)?.run {
                                    val channelId = getString(EXTRA_CHANNEL_ID) ?: return
                                    val data = getByteArray("data")
                                    convertManager?.processAudioData(channelId, data)
                                }
                            }
                        }

                        MSG_CANCEL_RECORD -> doCancel()

                        MSG_SAVE_RECORD -> doSaveFile()

                        MSG_RELEASE -> doRelease()
                        MSG_PAUSE_RECORD -> {
                            (msg.obj as? String)?.let {
                                doPauseRecord(it)
                            }
                        }

                        MSG_STOP_ASR -> {
                            (msg.obj as? String)?.let {
                                convertManager?.stopAsr(it)
                            }
                        }

                        MSG_START_TRANSLATION_CONFIG -> {
                            (msg.obj as? String)?.let {
                                convertManager?.getTranslationConfig(it)
                            }
                        }

                        MSG_EXTERNAL_INIT_ASR -> doExternalInitAsr()

                        MSG_EXTERNAL_STOP_ASR -> {
                            (msg.obj as? String)?.let {
                                doExternalStopAsr(it)
                            }
                        }
                    }
                }
            }
        }
    }

    private fun startRecorder(isResume: Boolean, channelId: String) {
        checkPcmRecorderInit()
        if (isResume) {
            if (recorder?.isRecording() == false) {
                recorder?.resumeRecord(channelId)
            }
        } else {
            recorder?.startRecord(channelId)
        }
    }

    private fun checkPcmRecorderInit() {
        if (recorder == null) {
            recorder = RealTimeAudioRecorder(this)
            recorder?.initRecorder()
        }
    }

    override fun setAsrResultCallback(callback: IRealTimeAsrResultCallback) {
        this.asrResultCallback = callback
    }

    private fun genStartAsrParam(channelId: String): Bundle {
        return bundleOf().apply {
            putString(EXTRA_CHANNEL_ID, channelId)
            putString(EXTRA_FILE_RECORD_ID, channelId)
            putInt(EXTRA_SAMPLE_RATE, RecorderConstant.SAMPLE_RATE_16000)
            putInt(EXTRA_SAMPLE_BYTES, ASR_EXTRA_SAMPLE_BYTES)
            putInt(EXTRA_SAMPLE_CHANNEL, RealTimeAudioRecorder.CHANNEL_SINGLE)
            putString(EXTRA_FORMAT, ASR_EXTRA_FORMAT)
            val spLanguage = PrefUtil.getString(BaseApplication.getAppContext(), PrefUtil.OPLUS_AI_TEXT_ASR_LANGUAGE, "")
            val currentLanguage = if (asrSupportLanguage.contains(spLanguage)) {
                spLanguage
            } else {
                LanguageUtil.getAsrDefaultSupportLanguageWithLocal(asrSupportLanguage)
            }
            putString(EXTRA_SOURCE_LANGUAGE, currentLanguage)
        }
    }

    /**
     * 开始录音
     * @param recordType 音频模式，如：标准模式 @see RecordModeConstant
     * @param recordFormat 音频格式，如mp3、aac @see RecorderConstant
     */
    override fun startRecord() {
        DebugUtil.i(TAG, "startRecord")
        mWorkHandler?.sendEmptyMessage(MSG_START_RECORDER)
    }

    override fun pauseRecord() {
        if (channelId == "" || recorder == null) {
            DebugUtil.i(TAG, "pauseRecord Uninitialized ASR and PCM recorder")
            return
        }
        DebugUtil.i(TAG, "pauseRecord")
        val runChannelId = channelId
        mWorkHandler?.obtainMessage(MSG_PAUSE_RECORD)?.run {
            obj = runChannelId
            sendToTarget()
        }
    }

    private fun doPauseRecord(channelId: String) {
        if (recorder?.isPaused() == true) {
            DebugUtil.d(TAG, "doPauseRecord return paused")
            return
        }
        DebugUtil.i(TAG, "doPauseRecordInner $channelId")
        if (convertManager?.getStartAsrResult(channelId) == true) {
            waitingByteList.clear()
        }
        recorder?.pauseRecord()
        // 启动发送ASR空包任务
        mWorkHandler?.postDelayed(emptyTask, SEND_EMPTY_TASK_INTERVAL)
        sendEmptyTime = 0L
        emptyPacketSize = 0L
    }

    /**
     * 恢复录音
     */
    override fun resumeRecord() {
        if (recorder == null) {
            DebugUtil.i(TAG, "resumeRecord Uninitialized PCM recorder")
            return
        }
        DebugUtil.i(TAG, "resumeRecord")
        mWorkHandler?.sendEmptyMessage(MSG_RESUME_RECORDER)
    }

    private fun doResumeRecorder() {
        DebugUtil.i(TAG, "doResumeRecorder")
        mWorkHandler?.removeCallbacks(emptyTask)
        val isSmartNameSwitchOpen = smartNameAction?.isSmartNameSwitchOpen(BaseApplication.getAppContext()) == true
        val isRealtimeSwitchOpen = RecorderViewModel.getInstance().isRealTimeSwitch()
        if (isSmartNameSwitchOpen.not() && isRealtimeSwitchOpen.not()) {
            return
        }
        val isRecorderInitStatus = recorder?.isInitializing() == true
        if (convertManager?.isStopAsr(channelId) == true || isRecorderInitStatus) {
            // ASR通道已经关闭
            val channelId = resetChannelId()
            if (isRecorderInitStatus) {
                startRecorder(false, channelId)
            } else if (recorder?.isPaused() == true) {
                startRecorder(true, channelId)
            }
            offsetManager?.mainRecordingTime = getAdjustedMainRecordingTime()
            convertManager?.initAsr(bundleOf(EXTRA_CHANNEL_ID to channelId))
        } else {
            // ASR 通道未关闭
            if (recorder?.isPaused() == true) {
                startRecorder(true, channelId)
            }
            // 计算发送空包造成的时间偏移量
            offsetManager?.let {
                it.emptyOffsetTime += getEmptyOffsetTime(emptyPacketSize)
                DebugUtil.i(TAG, "emptyOffsetTime = ${it.emptyOffsetTime}")
            }
        }
    }

    private fun resetChannelId(): String {
        channelId = UUID.randomUUID().toString()
        needReleaseChannelIdList.add(channelId)
        return channelId
    }

    /**
     * 停止录音
     */
    override fun doStopRecord() {
        DebugUtil.i(TAG, "doStopRecord")
        recorder?.stopRecord()
    }

    /**
     * 取消录音
     */
    override fun cancelRecord() {
        DebugUtil.i(TAG, "cancelRecord")
        cancelFlag = true
        mWorkHandler?.removeCallbacksAndMessages(null)
        mWorkHandler?.sendEmptyMessage(MSG_CANCEL_RECORD)
    }

    private fun doCancel() {
        DebugUtil.i(TAG, "doCancel")
        recorder?.release()
        clearAsrOffsetVar()
        convertManager?.stopAsr(channelId)
        convertManager?.releaseChannel(channelId)
        recorder = null
        convertManager = null
    }

    override fun saveFile(displayName: String, uri: Uri?) {
        DebugUtil.i(TAG, "saveFile")
        this.saveName = displayName
        this.saveUri = uri
        mWorkHandler?.sendEmptyMessage(MSG_SAVE_RECORD)
    }

    private fun doSaveFile() {
        if (saveName == null || saveUri == null) {
            DebugUtil.w(TAG, "doSaveFile save file is null")
            return
        }
        if (convertManager?.isError == true) {
            asrResultCallback?.onError(originErrorCode, formatErrorCode, convertManager?.getAsrContent())
            return
        }
        val displayName = saveName ?: return
        if (RecorderViewModelApi.getDirectRecordOn()) {
            //开启定向录音，重命名文件后可能存在media uri发生变化，重新查询一次真实uri
            val relativePath = RecorderViewModelApi.getRelativePath()
            saveUri = MediaDBUtils.getMediaUriForRelativePathAndDisplayName(relativePath, displayName)
        }
        val fileUri = saveUri ?: return
        DebugUtil.d(TAG, "doSaveFile, displayName:$displayName, fileUri:$fileUri")
        val record = MediaDBUtils.getRecordFromMediaByUriId(fileUri)
        if (record == null) {
            DebugUtil.w(TAG, "doSaveFile query record is null")
            /*异常处理，避免流程卡住*/
            asrResultCallback?.onError(originErrorCode, IRealTimeAsrController.ERROR_CODE_SERVER_ERROR, null)
            return
        }
        DebugUtil.i(TAG, "doSaveFile ${record.id}")
        runCatching { Thread.sleep(NumberConstant.NUMBER_L300) }
        runCatching {
            val dataList = convertManager?.getAsrContent() ?: return
            val fileName = convertServiceApi?.getConvertFileNameWithPostFix(record.id, displayName) ?: displayName
            val convertPath = convertServiceApi?.getConvertFilePath(BaseApplication.getAppContext(), fileName)

            val isEnableRealtimeSubtitles = FeatureOption.isEnableRealtimeSubtitles()
            if (isEnableRealtimeSubtitles) {
                convertServiceApi?.writeConvertContent(convertPath, dataList)
                CollectionInfoDbUtils.addConvertCollectionInfo(content = record.id.toString())
            }

            val convert = ConvertRecord().apply {
                this.recordId = record.id
                mediaPath = record.relativePath + displayName
                convertTextfilePath = convertPath
                completeStatus = ConvertDbUtil.CONVERT_COMP_STATUS_COMPLETE
                version = ConvertDbUtil.VERSION_2_SPEAKER
                serverPlanCode = ConvertDbUtil.SERVER_PLAN_ASR_REAL_TIME
                canShowSpeakerRole = if (dataList.any { it.roleId == -1 }) {
                    ConvertDbUtil.SHOW_SWITH_FALSE
                } else {
                    ConvertDbUtil.SHOW_SWITH_TRUE
                }
                speakerRoleOriginalNumber = ConvertDbUtil.getSpeakerRoleNumber(dataList)
            }
            if (isEnableRealtimeSubtitles) {
                ConvertDbUtil.insert(convert)
            }
            asrResultCallback?.onResult(convert, dataList)
        }.onFailure {
            DebugUtil.e(TAG, "doSaveFile error $it")
        }
    }

    /**
     * 释放录音
     */
    override fun releaseAll() {
        DebugUtil.i(TAG, "releaseAll")
        mWorkHandler?.sendEmptyMessage(MSG_RELEASE)
    }

    override fun isError(): Boolean {
        if (convertManager?.getAsrContent().isNullOrEmpty()) {
            return true
        }
        if (formatErrorCode != 0) {
            return true
        }
        return false
    }

    private fun doRelease() {
        DebugUtil.i(TAG, "doRelease")
        releaseRecorderAndAsr()
        releaseHandlerThread()
    }

    private fun releaseRecorderAndAsr() {
        DebugUtil.i(TAG, "releaseRecorderAndAsr")
        recorder?.release()
        // 释放当前通道
        convertManager?.let {
            it.asrCallback = null
            it.stopAsr(channelId)
            it.releaseChannel(channelId)
        }
        // 确保所有通道完全释放
        needReleaseChannelIdList.filterNot { it.equals(channelId) }.forEach { id ->
            convertManager?.let {
                it.stopAsr(id)
                it.releaseChannel(id)
            }
        }
        needReleaseChannelIdList.clear()
        recorder = null
        convertManager?.release()
        convertManager = null
        asrResultCallback = null
        offsetManager?.clear()
        offsetManager = null
    }

    override fun onAudioData(channelId: String, data: ByteArray) {
        if (cancelFlag) {
            return
        }
        val startAsrSuccess = convertManager?.getStartAsrResult(channelId) == true
        if (startAsrSuccess && convertManager?.isError == false) {
            if (waitingByteList.isNotEmpty()) {
                /*使用foreach，发送，合并一起发送没有前面数据asr结果，若要修改，需要跟算法侧确认清除*/
                waitingByteList.iterator().forEach {
                    sendAudioDataMessage(channelId, it)
                }
                // 发送完成清空
                waitingByteList.clear()
            }
            sendAudioDataMessage(channelId, data)
        } else if (convertManager?.isError != true && !startAsrSuccess && convertManager?.isStopAsr(channelId) != true) {
            // startAsr 结果还没回来，不能发送数据，存起来，等startAsr成功后发送
            DebugUtil.i(TAG, "onAudioData not start asr $channelId")
            waitingByteList.add(data)
        } else {
            DebugUtil.w(TAG, "onAudioData abdon....$channelId")
        }
    }

    private fun sendAudioDataMessage(channelId: String, data: ByteArray) {
        mWorkHandler?.obtainMessage(MSG_UPLOAD_DATA)?.run {
            val bundle = bundleOf(EXTRA_CHANNEL_ID to channelId)
            bundle.putByteArray("data", data)
            obj = bundle
            sendToTarget()
        }
    }

    private fun releaseHandlerThread() {
        runCatching {
            mWorkHandler?.removeCallbacksAndMessages(null)
            mHandlerThread?.quitSafely()
        }.onFailure {
            DebugUtil.e(TAG, "releaseHandlerThread error $it")
        }
        mWorkHandler = null
        mHandlerThread = null
    }

    /**
     * 检查RealtimeAsrManager和 offsetManager 是否初始化；
     * 如果没有初始化，则初始化,并初始化回调
     * 如果已经初始化，则什么都不做
     */
    private fun checkManagerInit() {
        DebugUtil.i(TAG, "checkManagerInit")
        if (offsetManager == null) {
            offsetManager = RealtimeOffsetManager()
        }
        if (convertManager == null) {
            convertManager = RealtimeAsrManager().apply {
                offsetManager = <EMAIL>
                asrCallback = RealTimeAsrCallbackImpl()
            }
        }
    }

    /**
     * ASR接口回调
     */
    inner class RealTimeAsrCallbackImpl : RealtimeAsrCallback {
        override fun onInitResult(channelId: String?, success: Boolean) {
            channelId ?: return
            if (cancelFlag) {
                DebugUtil.d(TAG, "onInitResult cancel")
                return
            }
            mWorkHandler?.post {
                DebugUtil.i(
                    TAG, "onInitResult $success,channelId=$channelId," +
                            "stop ${convertManager?.isStopAsr(channelId)}"
                )
                if (success) { // init成功才能调用start、发送数据
                    val isDisable = asrSupportLanguage.isEmpty()
                    if (convertManager?.isStopAsr(channelId) == false && isDisable) {
                        convertManager?.getTranslationConfig(channelId, true)
                        isFirstInitFlag = false
                        // 后续流程在获取支持语种的回调中执行， onTranslationCfgError 和 onTranslationCfgSuccess
                    } else if (convertManager?.isStopAsr(channelId) == false && isDisable.not()) {
                        convertManager?.startAsr(channelId, genStartAsrParam(channelId))
                    } else {
                        DebugUtil.i(TAG, "onInitResult no start asr")
                    }
                } else {
                    originErrorCode = STATUS_INIT_ERROR
                    formatErrorCode = IRealTimeAsrController.ERROR_CODE_INIT_ERROR
                    interruptASR(channelId)
                }
            }
        }

        override fun onError(channelId: String?, code: Int?, msg: String?) {
            DebugUtil.i(TAG, "on error $code")
            originErrorCode = code ?: -1
            formatErrorCode = if (code == ERROR_NET_DISCONNECT
                || NetworkUtils.isNetworkInvalid(BaseApplication.getAppContext())
            ) {
                IRealTimeAsrController.ERROR_CODE_NET_ERROR
            } else {
                IRealTimeAsrController.ERROR_CODE_SERVER_ERROR
            }
            channelId?.let { interruptASR(it) }
        }

        override fun onStopAsrResult(result: Boolean, channelId: String?) {
            /*不管stop是否成功，都要释放该通道*/
            channelId?.let {
                convertManager?.releaseChannel(it)
                needReleaseChannelIdList.remove(channelId)
            }
        }

        override fun onTranslationCfgError(channelId: String?, errorCode: Int, errorMsg: String?) {
            DebugUtil.d(TAG, "onTranslationCfgError channelId=$channelId, errorCode=$errorCode, errorMsg=$errorMsg")
            // 接口返回错误，则默认语言为内销中文，外销英文
            channelId?.let { convertManager?.startAsr(it, genStartAsrParam(it)) }
        }

        override fun onTranslationCfgSuccess(channelId: String?, data: Map<String, String>) {
            DebugUtil.d(TAG, "onTranslationCfgSuccess channelId=$channelId, data=$data")
            asrSupportLanguage.clear()
            asrSupportLanguage.addAll(data.keys.toMutableList())
            channelId?.let { convertManager?.startAsr(it, genStartAsrParam(it)) }
        }
    }

    /**
     * ASR异常停止处理，如服务器主动断开，无网络连接,初始化失败
     * ASR主动断开处理，如用户点击取消转写
     */
    private fun interruptASR(channelId: String) {
        DebugUtil.d(TAG, "interruptASR channelId=$channelId")
        recorder?.release()
        recorder = null
        clearAsrOffsetVar()
        convertManager?.stopAsr(channelId)
        convertManager?.releaseChannel(channelId)
        needReleaseChannelIdList.remove(channelId)
        isASRInterrupted = true
    }

    override fun changeAsrLanguage(language: String) {
        RecorderViewModelApi.setCurSelectedLanguage(language)
        recorder?.stopProcessData = true
        // 后续处理流程由onStopProcessData()方法接手，以确保当前数据处理完成之后，才开始切换语言
    }

    override fun onStopProcessData(channelId: String, stopProcessFlag: Boolean) {
        DebugUtil.d(TAG, "onStopProcessData channelId=$channelId, stopProcessFlag=$stopProcessFlag")
        if (stopProcessFlag) {
            // 处理切换ASR语言
            clearAsrOffsetVar()
            // 停止asr通道
            convertManager?.let {
                it.stopAsr(channelId)
                it.releaseChannel(channelId)
                needReleaseChannelIdList.remove(channelId)
            }
            // 立刻开启一个新的通道
            val newChannelId = resetChannelId()
            offsetManager?.mainRecordingTime = getAdjustedMainRecordingTime()
            convertManager?.initAsr(bundleOf(EXTRA_CHANNEL_ID to newChannelId))
            recorder?.let {
                it.stopProcessData = false
                it.processReadData(newChannelId)
            }
        }
    }

    override fun registerRtAsrListener(listener: OnRealtimeListener) {
        DebugUtil.d(TAG, "registerRtAsrListener listener=$listener")
        if (convertManager == null || offsetManager == null) {
            checkManagerInit()
        }
        convertManager?.registerRtAsrListener(listener)
    }

    override fun unregisterRtAsrListener(listener: OnRealtimeListener) {
        DebugUtil.d(TAG, "unregisterRtAsrListener listener=$listener")
        convertManager?.unregisterRtAsrListener(listener)
    }

    override fun startTranslationConfig() {
        DebugUtil.d(TAG, "startTranslationConfig")
        mWorkHandler?.obtainMessage(MSG_START_TRANSLATION_CONFIG)?.run {
            obj = channelId
            sendToTarget()
        }
    }

    override fun getAllAsrContent(): List<ConvertContentItem>? {
        DebugUtil.d(TAG, "getAllAsrContent")
        return convertManager?.getAsrContent()
    }

    override fun getRealtimeAsrStatus(): RealTimeAsrStatus {
        DebugUtil.d(TAG, "getRealtimeAsrStatus")
        return convertManager?.getRealtimeAsrStatus() ?: RealTimeAsrStatus.ASR_DEFAULT
    }

    override fun externalInitAsr() {
        DebugUtil.d(TAG, "externalInitAsr")
        mWorkHandler?.sendEmptyMessage(MSG_EXTERNAL_INIT_ASR)
    }

    private fun doExternalInitAsr() {
        if (RecorderViewModel.getInstance().isMainRecording().not()) {
            // 需要初始化PCM Recorder
            checkPcmRecorderInit()
            return
        }
        val asrStatus = convertManager?.getRealtimeAsrStatus() ?: RealTimeAsrStatus.ASR_DEFAULT
        if (asrStatus.isStartingUp().not()) {
            val channelId = resetChannelId()
            startRecorder(false, channelId)
            if (!cancelFlag) {
                offsetManager?.mainRecordingTime = getAdjustedMainRecordingTime()
                convertManager?.let {
                    // 重新初始化的时候，需要将isError标志位置恢复默认值
                    it.isError = false
                    it.initAsr(bundleOf(EXTRA_CHANNEL_ID to channelId))
                }
            }
        } else {
            DebugUtil.d(TAG, "externalInitAsr asr is starting up")
        }
    }

    override fun externalStopAsr() {
        if (channelId == "" || recorder == null) {
            DebugUtil.i(TAG, "externalStopAsr Uninitialized ASR and PCM recorder")
            return
        }
        DebugUtil.d(TAG, "externalStopAsr")
        mWorkHandler?.obtainMessage(MSG_EXTERNAL_STOP_ASR)?.run {
            obj = channelId
            sendToTarget()
        }
    }

    private fun doExternalStopAsr(channelId: String) {
        interruptASR(channelId)
    }

    /**
     * 获取ASR是否中断过
     * @return true: ASR中断过 false: ASR未中断过
     */
    override fun isASRInterrupted(): Boolean {
        return isASRInterrupted
    }

    /**
     * 修改讲话人名字
     */
    override fun updateSpeakerName(roleId: Int, newName: String): Boolean {
        return convertManager?.updateSpeakerName(roleId, newName) == true
    }

    /**
     * 获取ASR转写缓存对象
     */
    override fun getRealtimeSubtitleCache(): IRealtimeSubtitleCache? {
        return convertManager?.getRealtimeSubtitleCache()
    }

    /**
     * 根据空包大小计算空包造成的时间偏移量
     * 计算公式
     *                  空包音频数据总字节数
     * 偏移量毫秒数 = ———————————————————————————————— * 1000
     *              (PCM编码位数/8) * 声道数 * 采样率
     */
    private fun getEmptyOffsetTime(packetSize: Long): Long {
        return recorder?.let { recorder ->
            val pcmBit = recorder.getAudioFormat()
            val channelNum = recorder.getChannelNum()
            // 确保所有参数有效
            if (pcmBit <= 0 || channelNum <= 0 || SAMPLE_RATE <= 0) {
                DebugUtil.w(TAG, "Invalid parameters for offset calculation: pcmBit=$pcmBit, channelNum=$channelNum, sampleRate=$SAMPLE_RATE")
                return@let 0L
            }
            // 使用Double进行计算，避免整数除法导致的精度丢失
            val bytesPerSample = pcmBit / NumberConstant.NUM_8.toDouble()
            val samplesPerSecond = SAMPLE_RATE * channelNum.toDouble()
            val offsetTimeMs = (packetSize * NumberConstant.NUM_1000.toDouble()) / (bytesPerSample * samplesPerSecond)
            // 四舍五入后转为Long
            offsetTimeMs.roundToLong()
        } ?: 0L
    }

    /**
     * 获取主录音的当前时间，单位毫秒
     */
    private fun getAdjustedMainRecordingTime(): Long {
        return RecorderViewModel.getInstance().getMainRecorderCurrentTime().also {
            DebugUtil.w(TAG, "getAdjustedMainRecordingTime: $it")
        }
    }

    /**
     * ASR停止时，重置计算偏移量的相关变量
     */
    private fun clearAsrOffsetVar() {
        emptyPacketSize = 0
        sendEmptyTime = 0
        offsetManager?.let {
            it.emptyOffsetTime = 0
        }
    }
}