/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  OldStatusBar
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/6/29
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.recorderservice.manager.statusbar

import android.content.Context
import android.content.Intent
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.OplusCompactUtil
import com.soundrecorder.common.constant.OplusCompactConstant
import com.soundrecorder.recorderservice.RecorderService
import com.soundrecorder.recorderservice.manager.RecorderViewModel

object OldStatusBar {
    private const val TAG = "OldStatusBar"
    private const val RECORDER_STATUS_FOR_STATUS_BAR = "recorder_status_for_statebar"
    private const val RECORDER_TIME_FOR_STATUS_BAR = "recorder_time_for_statebar"
    private const val STATUS_HIDE = 0
    private const val STATUS_SHOW = 1

    @Volatile
    var isShowing = false
        private set

    @JvmStatic
    fun show(ctx: Context, from: String) {
        DebugUtil.d(TAG, "old statusBar show, from = $from, isShowing = $isShowing", true)
        if (!isShowing) {
            isShowing = true
            Intent().run {
                OplusCompactUtil.getActionForIntent(this,
                    OplusCompactConstant.STATUS_BAR_UPDATE_ACTION_BEFOR,
                    OplusCompactConstant.STATUS_BAR_UPDATE_ACTION_AFTER
                )
                putExtra(RECORDER_STATUS_FOR_STATUS_BAR, STATUS_SHOW)
                putExtra(RECORDER_TIME_FOR_STATUS_BAR, RecorderViewModel.getInstance().getAmplitudeCurrentTime())
                ctx.sendBroadcast(this, RecorderService.COMPONENT_SAFE_PERMISSION)
            }
        }
    }

    @JvmStatic
    fun dismiss(ctx: Context, forceDismiss: Boolean = false, from: String) {
        DebugUtil.d(TAG, "dismiss, forceDismiss = $forceDismiss, from = $from, isShowing = $isShowing", true)
        if (forceDismiss || isShowing) {
            isShowing = false
            Intent().run {
                OplusCompactUtil.getActionForIntent(this,
                    OplusCompactConstant.STATUS_BAR_UPDATE_ACTION_BEFOR,
                    OplusCompactConstant.STATUS_BAR_UPDATE_ACTION_AFTER
                )
                putExtra(RECORDER_STATUS_FOR_STATUS_BAR, STATUS_HIDE)
                ctx.sendBroadcast(this, RecorderService.COMPONENT_SAFE_PERMISSION)
            }
        }
    }

    @JvmStatic
    fun release() {
        isShowing = false
    }
}