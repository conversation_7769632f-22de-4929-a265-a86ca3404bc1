/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  FluidStatusBarCloseTimer
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/11/20
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.recorderservice.manager.statusbar

import android.os.CountDownTimer

class FluidStatusBarCloseTimer(var timerCallback: (form: String) -> Unit) {

    companion object {
        private const val TIMER_DURATION = 5900L //保存中的流体卡片，要求至少显示5s再显示保存结果卡片+900L保存loading加载的延迟。
        private const val TIMER_INTERVAL = 1000L
    }

    private var timer: CountDownTimer? = null

    fun startTimer(form: String) {
        cancelTimer()

        timer = object : CountDownTimer(TIMER_DURATION, TIMER_INTERVAL) {
            override fun onTick(p0: Long) {
                //do nothing
            }

            override fun onFinish() {
                timerCallback.invoke(form)
            }
        }
        timer?.start()
    }

    fun cancelTimer() {
        timer?.cancel()
        timer = null
    }
}