/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecordStatus
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.recorderservice

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

//currentRecordState 取值定义
//没有处于录音状态
const val RECORD_STATE_NOTRECORD = 0

//录制过程中
const val RECORD_STATE_RECORDING = 1

//录制暂停中
const val RECORD_STATE_PAUSED = 2

//录制停止保存过程中
const val RECORD_STATE_STOPING_SAVING = 3


//otherState 取值定义
//默认值，无意义
const val OTHER_STATE_DEFAULT = 0

//录音App正在详情播放页面中，无法录音
const val OTHER_STATE_PLAYBACK = 1

//录音App正在裁切页面中，无法录音
const val OTHER_STATE_EDITRECORD = 2

//录音App其他可能存在的不能录音的界面，无法录音（后续如果录音有其他新增界面，可能会用到）
const val OTHER_STATE_OTHERACTVITY = 3

//errorCode 取值定义
//无错误，默认值
const val STATUS_ERROR_CODE_DEFAULT = 0

//录音App存储空间未授权，无法录音
const val ERROR_CODE_NOSTORAGEPERMISSION = 1

//录音App录音权限未授权，无法录音
const val ERROR_CODE_NORECORDPERMISSION = 2

//录音App存储空间，录音权限都未授权，无法录音
const val ERROR_CODE_NOBOTHPERMISSION = 3

//当前通话过程中，无法录音
const val ERROR_CODE_INCALLSTATE = 4

//当前Rom存储空间不足，无法录音
const val ERROR_CODE_NO_SPACE = 5


//extField 取值定义
//默认值，无意义
const val EXT_DEFAULT = 0


@Parcelize
data class RecordStatus(
    var currentRecordState: Int = RECORD_STATE_NOTRECORD,
    var otherState: Int = OTHER_STATE_DEFAULT,
    var errorCode: Int = STATUS_ERROR_CODE_DEFAULT,
    var extField: Int = EXT_DEFAULT
) : Parcelable