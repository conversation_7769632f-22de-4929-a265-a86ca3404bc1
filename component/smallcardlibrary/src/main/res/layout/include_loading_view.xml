<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/loadingView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:visibility="gone"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginBottom="@dimen/small_card_save_succes_spacing_file_name"
        android:visibility="visible"
        app:layout_constraintBottom_toTopOf="@id/tvSavingFileName"
        app:layout_constraintEnd_toEndOf="@+id/tvSavingFileName"
        app:layout_constraintStart_toStartOf="@+id/tvSavingFileName"
        app:layout_constraintVertical_chainStyle="packed"
        >

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lottieView"
            android:layout_width="26dp"
            android:layout_height="26dp"
            app:flow_horizontalAlign="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:lottie_loop="true" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tvSavingFileName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/breeno_saving_text_color"
        android:textFontWeight="400"
        android:textSize="@dimen/small_card_loading_text_size"
        app:layout_constraintBottom_toTopOf="@id/tvLoading"
        app:layout_constraintEnd_toEndOf="@+id/tvLoading"
        app:layout_constraintStart_toStartOf="@+id/tvLoading"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="标准录音 1" />

    <TextView
        android:id="@+id/tvLoading"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:gravity="center"
        android:paddingHorizontal="@dimen/small_card_content_margin_horizontal"
        android:textColor="@color/breeno_saving_text_color"
        android:textFontWeight="400"
        android:textSize="@dimen/small_card_loading_text_size"
        app:layout_constraintBottom_toTopOf="@id/guideline_bottom"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="loading" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.81" />

</androidx.constraintlayout.widget.ConstraintLayout>