<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.oplus.soundrecorder.breenocardlibrary.views.WaveView
        android:id="@+id/wv_record"
        android:layout_width="match_parent"
        android:layout_height="@dimen/record_wave_height"
        android:layout_marginHorizontal="12dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/recordTime"
        app:layout_constraintBottom_toTopOf="@id/ivStartOrPause"
        app:wave_view_amp_color="@color/default_amp_color" />

    <ImageView
        android:id="@+id/iv_wave_mark_flag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:importantForAccessibility="no"
        android:padding="6dp"
        android:src="@drawable/brenno_card_do_mark"
        android:background="@color/breeno_card_background_color"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/wv_record"
        app:layout_constraintEnd_toEndOf="@id/wv_record"
        app:layout_constraintStart_toStartOf="@id/wv_record"
        app:layout_constraintTop_toTopOf="@id/wv_record" />

    <View
        android:id="@+id/view_wave_start_bg"
        android:layout_width="49dp"
        android:layout_height="0dp"
        android:background="@drawable/shape_wave_gradient_left"
        android:layoutDirection="ltr"
        app:layout_constraintBottom_toBottomOf="@id/wv_record"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/wv_record" />

    <View
        android:id="@+id/view_wave_end_bg"
        android:layout_width="49dp"
        android:layout_height="0dp"
        android:background="@drawable/shape_wave_gradient_right"
        android:layoutDirection="ltr"
        app:layout_constraintBottom_toBottomOf="@id/wv_record"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/wv_record" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_old_wave_views"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="wv_record,view_wave_start_bg,view_wave_end_bg" />
</merge>