/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: SummaryCacheDBHelper
 * Description:
 * Version: 1.0
 * Date: 2025/5/12
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/12      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.summary.request.database

import android.os.SystemClock
import androidx.annotation.VisibleForTesting
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil

object SummaryCacheDBHelper {
    private const val TAG = "SummaryCacheDBHelper"

    private val _summaryCacheDao by lazy {
        SummaryCacheDatabase.getInstance(BaseApplication.getAppContext()).summaryCacheDao()
    }

    @VisibleForTesting
    private val summaryCacheDao: SummaryCacheDao
        get() = _summaryCacheDao


    fun getChooseSummaryByMediaId(mediaId: Long): SummaryCacheEntity? {
        val summary = summaryCacheDao.queryCurrentSummary(mediaId)
        if (summary.isEmpty()) {
            DebugUtil.e(TAG, "getChooseSummaryByMediaId is null, error")
            return null
        }
        if (summary.size > 1) {
            DebugUtil.e(TAG, "getChooseSummaryByMediaId size over 1, error")
            return null
        }
        return summary[0]
    }

    fun saveNoteDataToDatabase(
        mediaId: Long,
        summaryContent: String,
        title: String,
        time: Long
    ): Boolean {
        val summaryCacheRowContent = SummaryCacheEntity(
            chooseState = 0,
            summaryTitle = title,
            timeStamp = time,
            mediaId = mediaId,
            summaryContent = summaryContent
        )
        return summaryCacheDao.insert(summaryCacheRowContent) != -1L
    }

    /**
     * 添加单个摘要缓存
     * @param entity 摘要缓存行内容
     * @return Boolean 添加是否成功
     */
    fun addSummaryCache(entity: SummaryCacheEntity): Long {
        return kotlin.runCatching {
            DebugUtil.d(
                TAG,
                "addSummaryCache ${entity.timeStamp}, ${entity.chooseState}, " + "${entity.summaryStyle}, ${entity.summaryTheme}"
            )
            summaryCacheDao.insertByCheck(entity)
        }.onFailure {
            DebugUtil.e(TAG, "addSummaryCache: ERROR! $it")
        }.getOrDefault(-1L)
    }

    fun switchNextSummary(mediaId: Long, currentTimestamp: Long): SummaryCacheEntity? {
        return summaryCacheDao.switchNextSummary(mediaId, currentTimestamp)
    }

    fun switchPreviousSummary(mediaId: Long, currentTimestamp: Long): SummaryCacheEntity? {
        return summaryCacheDao.switchPreviousSummary(mediaId, currentTimestamp)
    }

    fun getAllSummaryByMediaId(mediaId: Long): List<SummaryCacheEntity> {
        return summaryCacheDao.query(mediaId)
    }

    /**
     * 溯源更新摘要缓存
     */
    fun updateSummaryCacheWithTrace(mediaId: Long, trace: String): Boolean = kotlin.runCatching {
        val result = summaryCacheDao.updateByTrace(mediaId, trace)
        result > 0
    }.onFailure {
        DebugUtil.e(TAG, "updateSummaryCache: ERROR! $it")
    }.getOrDefault(false)


    /**
     * 待办更新摘要缓存
     */
    fun updateSummaryCacheWithAgent(id: Long, mediaId: Long, agent: String): Boolean =
        kotlin.runCatching {
            val result = summaryCacheDao.updateByAgent(id, mediaId, agent)
            result > 0
        }.onFailure {
            DebugUtil.e(TAG, "updateSummaryCache: ERROR! $it")
        }.getOrDefault(false)

    /**
     * querySummaryListByKeyword
     */
    fun querySummaryListByKeyword(keyWord: String?): List<SummaryCacheEntity> = kotlin.runCatching {
        DebugUtil.e(TAG, "querySummaryListByKeyword keyword:$keyWord")
        if (keyWord.isNullOrEmpty()) {
            emptyList()
        } else {
            DebugUtil.e(TAG, "SQL Para:$keyWord")
            val dbList = summaryCacheDao.getSummaryItemsByKeyWord(keyWord)
            DebugUtil.e(TAG, "DB list:${dbList.size}")
            dbList
        }
    }.onFailure {
        DebugUtil.e(TAG, "querySummaryListByKeyword: ERROR! Keyword: $keyWord")
        emptyList<SummaryCacheEntity>()
    }.getOrDefault(emptyList())
}
