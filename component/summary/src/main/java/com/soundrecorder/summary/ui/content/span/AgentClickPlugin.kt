/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  AgentClickPlugin
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui.content.span

import android.content.Context
import android.graphics.Color
import android.text.TextPaint
import android.text.style.ClickableSpan
import android.view.View
import androidx.annotation.Keep
import com.support.appcompat.R
import io.noties.markwon.AbstractMarkwonPlugin
import io.noties.markwon.MarkwonConfiguration
import io.noties.markwon.MarkwonSpansFactory
import io.noties.markwon.RenderProps
import io.noties.markwon.ext.tasklist.TaskListItem
import io.noties.markwon.ext.tasklist.TaskListSpan

@Keep
class AgentClickPlugin(
    context: Context,
    private val clickTask: ((isDone: Boolean, task: String) -> Unit)? = null
) : AbstractMarkwonPlugin() {

    companion object {
        private const val TAG = "AgentClickPlugin"
    }

    private var disableColor = context.getColor(R.color.coui_btn_check_color_off_normal)
    private var normalColor = context.getColor(R.color.coui_color_label_primary)

    override fun configureSpansFactory(builder: MarkwonSpansFactory.Builder) {
        val origin = builder.getFactory(TaskListItem::class.java) ?: return
        builder.setFactory(
            TaskListItem::class.java
        ) { configuration: MarkwonConfiguration, props: RenderProps ->
            val span =
                origin.getSpans(configuration, props) as? TaskListSpan ?: return@setFactory null
            arrayOf(span, object : ClickableSpan() {
                override fun onClick(widget: View) {
                    span.isDone = !span.isDone
                    clickTask?.invoke(span.isDone, span.task)
                    widget.invalidate()
                }

                override fun updateDrawState(ds: TextPaint) {
                    super.updateDrawState(ds)
                    ds.isUnderlineText = false
                    if (span.isDone) {
                        ds.color = disableColor
                    } else {
                        ds.color = normalColor
                    }
                    ds.bgColor = Color.TRANSPARENT
                    ds.linkColor = Color.TRANSPARENT
                }
            }
            )
        }.build()
    }
}