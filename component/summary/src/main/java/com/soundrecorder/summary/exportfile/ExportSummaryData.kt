/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ExportSummaryData
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/04/25
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.exportfile

import android.content.Context
import android.content.res.Configuration
import android.graphics.Bitmap
import android.media.MediaScannerConnection
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.toBitmap
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.summary.R
import java.io.File
import java.io.FileOutputStream

data class ExportSummaryData(
    val title: String,
    val summary: SummaryContent,
    val bitmap: List<String>
)

data class SummaryContent(val content: String, val htmlContent: List<String>)

private const val QUALITY = 100
private const val DEFAULT_FILE_NAME = "/file_not_exist.png"
private const val DEFAULT_FILE_NAME_NIGHT = "/file_not_exist_night.png"


fun getDefaultFileIfAttachmentLoss(context: Context): File {
    val isNightMode = context.resources.configuration.uiMode and
            Configuration.UI_MODE_NIGHT_MASK == Configuration.UI_MODE_NIGHT_YES
    val destPath = context.filesDir.absolutePath + if (isNightMode) {
        DEFAULT_FILE_NAME_NIGHT
    } else {
        DEFAULT_FILE_NAME
    }
    return File(destPath).also {
        if (it.exists()) return@also
        val drawable = ContextCompat.getDrawable(context, R.drawable.file_not_exist)
        val bitmap = drawable?.toBitmap()
        runCatching {
            val fileOutputStream = FileOutputStream(File(destPath))
            fileOutputStream.use { fos ->
                bitmap?.compress(Bitmap.CompressFormat.PNG, QUALITY, fos)
                fos.flush()
            }
        }.onFailure { tr ->
            DebugUtil.w("ExportSummaryData", "internalMediaScanner :${tr.message}")
        }
        bitmap?.recycle()
    }
}

fun scanFile(context: Context, path: String) {
    runCatching {
        val paths = arrayOf(path)
        MediaScannerConnection.scanFile(context, paths, null, null)
    }.onFailure {
        DebugUtil.w("ExportSummaryData", "internalMediaScanner :$path, error: $it")
    }
}