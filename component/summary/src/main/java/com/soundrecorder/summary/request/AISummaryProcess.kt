/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: ProcessAISummary
 * Description:
 * Version: 1.0
 * Date: 2025/5/8
 * Author: ********
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * ********                         2025/5/8      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.summary.request

import android.os.Handler
import android.os.HandlerThread
import android.os.Message
import com.oplus.recorderlog.util.GsonUtil
import com.oplus.unified.summary.sdk.UnifiedSummaryKit
import com.oplus.unified.summary.sdk.callback.ISummaryInitCallback
import com.oplus.unified.summary.sdk.common.SummaryInitParam
import com.oplus.unified.summary.sdk.common.SummaryResultType
import com.oplus.unified.summary.sdk.speech.SpeechSummaryRequest
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.base.utils.PrefUtil.SUMMARY_SUPPORT_THEME
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.addRecordSummaryScene
import com.soundrecorder.summary.data.SummaryDataParser
import com.soundrecorder.summary.model.CONTENT_LESS_ERROR
import com.soundrecorder.summary.model.LOAD_ERROR
import com.soundrecorder.summary.model.PLUGIN_INIT_ERROR
import com.soundrecorder.summary.model.SummaryRequestModel
import com.soundrecorder.summary.model.SummaryStop
import com.soundrecorder.summary.model.SummaryTheme
import com.soundrecorder.summary.request.SummaryRecordParamGetter.SUMMARY_MIN_SIZE
import com.soundrecorder.summary.request.database.CHOOSE
import com.soundrecorder.summary.request.database.SummaryCacheDBHelper
import com.soundrecorder.summary.request.database.SummaryCacheEntity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class AISummaryProcess(
    private val requestModel: SummaryRequestModel,
    private var callback: IAISummaryCallback
) {

    companion object {
        const val IS_OVER_SIZE = "is_over_size"
        const val SUMMARY_TIME = "summary_time"
        const val DATABASE_ID = "database_id"
        const val TITLE = "title"
        const val TIME = "time"
        const val LANGUAGE = "language"
        const val BIZ_TAG_AI_RECORDING = "ai_recording"
        const val STOP_REASON = "stop_reason"
        const val THEME_CODE = "theme_code"

        private const val TAG = "AISummaryProcess"
        private const val MSG_START_AI_SUMMARY = 1
        private const val MSG_RELEASE = 2
        private const val MAX_RETRY_COUNT = 3
    }

    private var mHandlerThread: HandlerThread? = null
    private var workHandler: Handler? = null
    private var isThreadQuit = false

    private var mSummaryKit: UnifiedSummaryKit? = null
    private var mErrorRetryCount = 0  // 错误重试计数器

    private val mediaId = requestModel.mediaId
    private var sessionId: String = ""

    private var stream: String = ""
    private var isOverSize = false
    private var isStop = false
    private var language: String = ""
    private var stopReason = -1

    private var currentStyleCode: Int = -1
    private var currentTitle: String? = null
    private var currentTime: Long = 0L

    fun initHandlerThread() {
        DebugUtil.d(TAG, "initHandlerThread, mediaId:$mediaId")
        isThreadQuit = false
        mHandlerThread = HandlerThread("AISummary-$mediaId")
        mHandlerThread?.start()
        mHandlerThread?.looper?.let {
            workHandler = object : Handler(it) {
                override fun handleMessage(msg: Message) {
                    when (msg.what) {
                        MSG_START_AI_SUMMARY -> processAISummary()
                        MSG_RELEASE -> release()
                    }
                }
            }
        }
    }

    fun release() {
        DebugUtil.d(TAG, "release")
        workHandler?.post {
            DebugUtil.d(TAG, "release run")
            releaseSummaryKit()
            workHandler?.removeCallbacksAndMessages(null)
            mHandlerThread?.quitSafely()
            mHandlerThread = null
            workHandler = null
            isThreadQuit = true
            isStop = false
            stopReason = -1
        }
    }

    private fun releaseSummaryKit() {
        mSummaryKit?.release(getAppTag())
        mSummaryKit = null
    }

    fun doStartAISummary() {
        DebugUtil.i(TAG, "doStartAISummary: start")
        workHandler?.sendEmptyMessage(MSG_START_AI_SUMMARY)
    }

    fun getCurrentSteam(): String {
        return stream
    }

    fun getStreamExtra(): Map<String, Any>? {
        return hashMapOf<String, Any>().apply {
            put(IS_OVER_SIZE, isOverSize)
            currentTitle?.let { put(TITLE, it) }
            put(TIME, currentTime)
            put(LANGUAGE, language)
        }
    }

    private fun processAISummary() {
        //初始化分词工具
        SummaryDataParser.initSemanticTool()
        val params = SummaryRecordParamGetter.getAISummaryRecordParam(
            requestModel.mediaId,
            requestModel.recordType,
            requestModel.recordTime
        )
        handleSummaryRequest(params)
    }

    private fun handleSummaryRequest(params: AISummaryRecordParam) {
        if (params.sessionId.isEmpty()) {
            DebugUtil.e(TAG, "handleSummaryRequest sessionId is null")
            callback.onAISummaryError(mediaId, LOAD_ERROR, "")
            callback.onAISummaryStop(mediaId, null)
            return
        }
        if (params.asrContent.isEmpty() && params.dialogContent.isEmpty()) {
            DebugUtil.e(TAG, "content is null")
            callback.onAISummaryError(mediaId, CONTENT_LESS_ERROR, "")
            callback.onAISummaryStop(mediaId, null)
            return
        }
        if (params.length <= SUMMARY_MIN_SIZE) {
            DebugUtil.e(TAG, "size low")
            callback.onAISummaryError(mediaId, CONTENT_LESS_ERROR, "")
            callback.onAISummaryStop(mediaId, null)
            return
        }
        isOverSize = params.isOverSize
        language = params.outputLanguage
        val summaryType = if (requestModel.theme?.style == SummaryTheme.NORMAL_SIMPLE) {
            SummaryResultType.DEFAULT
        } else {
            SummaryResultType.DETAIL
        }
        val themeCode = if (requestModel.theme?.style == SummaryTheme.NORMAL_SIMPLE) {
            SummaryTheme.NORMAL
        } else {
            requestModel.theme?.style
        }
        val retryCount = AISummaryRetryCacheTask.getCurrentRetryCount(mediaId)
        val isPhoneTheme = if (themeCode == null) {
            params.isPhoneRecord
        } else {
            null
        }
        DebugUtil.i(
            TAG,
            "themeCode = ${requestModel.theme?.style}, " +
                    "retryCount = $retryCount, " +
                    "isOverSize = $isOverSize, " +
                    "language = $language, " +
                    "isPhoneRecord = ${params.isPhoneRecord}"
        )
        val request = if (params.asrContent.isNotEmpty()) {
            SpeechSummaryRequest.createRecordSummaryRequest(
                sessionId = params.sessionId,
                content = params.asrContent,
                timeout = params.timeout,
                inputLanguage = params.inputLanguage,
                outputLanguage = params.outputLanguage,
                summaryType = summaryType,
                outputType = 0,
                enableEntityCombination = true,
                retryCount = retryCount,
                isRetry = (retryCount > 0),
                enableTheme = true,
                themeCode = themeCode,
                isPhoneTheme = isPhoneTheme
            )
        } else {
            SpeechSummaryRequest.createPhoneSummaryRequest(
                sessionId = params.sessionId,
                otherName = "",
                dialogs = params.dialogContent,
                timeout = params.timeout,
                inputLanguage = params.inputLanguage,
                outputLanguage = params.outputLanguage,
                summaryType = summaryType,
                outputType = 0,
                enableEntityCombination = true,
                retryCount = retryCount,
                isRetry = (retryCount > 0),
                enableTheme = true,
                themeCode = themeCode,
                isPhoneTheme = isPhoneTheme
            )
        }
        AISummaryRetryCacheTask.addRetryTaskToCache(params.mediaID, retryCount)
        createSummaryKit(request)
    }


    /**
     * 取消 生成摘要
     */
    fun cancel() {
        workHandler?.post {
            isStop = true
            stopReason = SummaryStop.REASON_STOP
            stopSummary()
            mErrorRetryCount = 0
        }
    }

    private fun stopSummary() {
        DebugUtil.d(TAG, "stopSummaryTask, sessionId:$sessionId")
        if (sessionId.isNotEmpty()) {
            mSummaryKit?.stopTask(sessionId)
        }
    }


    private fun createSummaryKit(request: SpeechSummaryRequest) {
        sessionId = request.sessionId
        mSummaryKit = UnifiedSummaryKit(BaseApplication.getAppContext())
        workHandler?.post { callback.onAISummaryStart(mediaId, null) }
        mSummaryKit?.initKit(
            SummaryInitParam(
                appTag = getAppTag(),
                isSupportDataTraining = true,
                appId = BIZ_TAG_AI_RECORDING
            ), callback = object : ISummaryInitCallback {

                override fun onSuccess(bizTag: String?, extras: Map<String, Any>?) {
                    DebugUtil.d(TAG, "initUnifiedSummary, onSuccess:$bizTag, extras:$extras sessionId:$sessionId")
                    startSummary(request)
                }

                override fun onError(sessionId: String, errorCode: Int, errorMsg: String?) {
                    DebugUtil.d(TAG, "initUnifiedSummary, onError:$sessionId, errorCode:$errorCode, errorMsg:$errorMsg")
                    stopReason = SummaryStop.REASON_ERROR
                    workHandler?.post {
                        callback.onAISummaryError(mediaId, errorCode, errorMsg)
                        val map = hashMapOf<String, Any>().apply {
                            this[STOP_REASON] = stopReason
                        }
                        callback.onAISummaryStop(mediaId, map)
                    }
                }
            }
        )
    }

    private fun getAppTag(): String {
        return "${BaseApplication.getAppContext().packageName}-summary-$mediaId"
    }

    /**
     * 开始生成摘要,实现摘要kit相关接口
     */
    private fun startSummary(request: SpeechSummaryRequest) {
        mSummaryKit?.getSummary(request, object : IAISummarySDKCallback {

            override fun onStart(sessionId: String?, extras: Map<String, Any>?) {
                DebugUtil.d(TAG, "onStart, sessionId:$sessionId, extras:$extras")
            }

            override fun onError(sessionId: String, errorCode: Int, errorMsg: String?) {
                DebugUtil.d(TAG, "onError, sessionId:$sessionId, errorCode:$errorCode, errorMsg:$errorMsg")
                if (<EMAIL> == sessionId) {
                    stopReason = SummaryStop.REASON_ERROR
                    workHandler?.post {
                        if (errorCode == PLUGIN_INIT_ERROR && mErrorRetryCount <= MAX_RETRY_COUNT) {
                            mErrorRetryCount += 1
                            DebugUtil.d(TAG, "startRetryAISummary, errorRetryCount:$mErrorRetryCount")
                            startRetryAISummary(request)
                        } else {
                            callback.onAISummaryError(mediaId, errorCode, errorMsg)
                            val map = hashMapOf<String, Any>().apply {
                                this[STOP_REASON] = stopReason
                            }
                            callback.onAISummaryStop(mediaId, map)
                        }
                    }
                }
            }

            override fun onDataAvailable(
                sessionId: String,
                jsonResult: String,
                extras: Map<String, Any>?
            ) {
                DebugUtil.d(
                    TAG,
                    "onDataAvailable sessionId:$sessionId, jsonResult:$jsonResult, currentStyleCode = $currentStyleCode, extras:$extras"
                )
                if (<EMAIL> == sessionId && isStop.not()) {
                    workHandler?.post {
                        val stream = SummaryDataParser.parseContentInStream(jsonResult)
                        <EMAIL> += stream
                        if (currentTitle == null) {
                            currentTitle = SummaryDataParser.parseTitle(jsonResult)
                        }
                        if (currentTime == 0L) {
                            currentTime = System.currentTimeMillis()
                        }
                        DebugUtil.d(
                            TAG,
                            "onDataAvailable currentTitle = $currentTitle, currentTime = $currentTime, language = $language"
                        )
                        val wrapExtras = hashMapOf<String, Any>().apply {
                            put(IS_OVER_SIZE, isOverSize)
                            currentTitle?.let { put(TITLE, it) }
                            put(TIME, currentTime)
                            put(LANGUAGE, language)
                        }
                        extras?.let { wrapExtras.plus(it) }
                        callback.onAISummaryDataAvailable(mediaId, <EMAIL>, wrapExtras)
                        if (currentStyleCode == -1) {
                            val abstractStyle = SummaryDataParser.parseAbstractStyle(jsonResult)
                            val themeCode = SummaryDataParser.parseThemeCode(jsonResult)
                            <EMAIL> = if (abstractStyle == "detail" || abstractStyle.isEmpty()) {
                                themeCode
                            } else {
                                SummaryTheme.NORMAL_SIMPLE
                            }
                            storeThemeList(SummaryDataParser.parseThemeInfo(jsonResult))
                            DebugUtil.d(TAG, "onDataAvailable abstractStyle = $abstractStyle, themeCode = $themeCode")
                        }
                    }
                }
            }

            /**
             * 流式数据结束回调
             * @param sessionId 当前请求ID
             * @param jsonResult 流式数据对象的json字符串
             * @param extras 扩展保留参数
             */
            override fun onFinished(
                sessionId: String,
                jsonResult: String,
                extras: Map<String, Any>?
            ) {
                DebugUtil.d(
                    TAG,
                    "onFinished, jsonResult:$jsonResult, extras:$extras, " +
                            "isStop:$isStop, " +
                            "currentStyleCode = $currentStyleCode, " +
                            "title = $currentTitle"
                )
                stopReason = SummaryStop.REASON_FINISH
                if (<EMAIL> == sessionId && isStop.not()) {
                    workHandler?.post {
                        //成功了就写入数据库
                        val summaryTheme = SummaryTheme(currentStyleCode)
                        val id = saveSummaryToDataBase(jsonResult, summaryTheme, currentTitle, language)
                        val time = SummaryDataParser.parseTime(jsonResult)
                        val map = hashMapOf<String, Any>().apply {
                            this[IS_OVER_SIZE] = isOverSize
                            this[SUMMARY_TIME] = time
                            this[DATABASE_ID] = id
                            this[THEME_CODE] = summaryTheme
                            this[LANGUAGE] = language
                            currentTitle?.let { put(TITLE, it) }
                        }
                        extras?.let { map.plus(it) }
                        callback.onAISummaryFinished(mediaId, jsonResult, map)
                        addRecordSummaryEvent(currentStyleCode)
                    }
                }
            }

            override fun onStop(sessionId: String, extras: Map<String, Any>?) {
                DebugUtil.d(TAG, "onStop, sessionId:$sessionId,extras:$extras,isStop:$isStop,stopReason:$stopReason")
                if (<EMAIL> == sessionId) {
                    workHandler?.post {
                        val map = hashMapOf<String, Any>().apply {
                            this[STOP_REASON] = stopReason
                        }
                        extras?.let { map.plus(it) }
                        callback.onAISummaryStop(mediaId, map)
                    }
                }
            }
        })
    }

    /**
     * 重试AI摘要
     */
    private fun startRetryAISummary(request: SpeechSummaryRequest) {
        releaseSummaryKit()
        createSummaryKit(request)
    }

    private fun saveSummaryToDataBase(
        summaryContent: String,
        theme: SummaryTheme,
        title: String?,
        language: String
    ): Long {
        DebugUtil.d(TAG, "saveSummaryToDataBase theme: = $theme")
        val content = SummaryDataParser.parseContentInFinish(summaryContent)
        val entities =
            GsonUtil.getGson().toJson(SummaryDataParser.parseSummaryEntity(summaryContent))
        val agent = GsonUtil.getGson()
            .toJson(SummaryDataParser.parseAgent(BaseApplication.getAppContext(), content))
        val time = SummaryDataParser.parseTime(summaryContent)
        val summaryCache = SummaryCacheEntity(
            mediaId = mediaId,
            summaryStyle = theme.style,
            chooseState = CHOOSE,
            summaryContent = content,
            summaryEntity = entities,
            summaryAgent = agent,
            timeStamp = time,
            summaryTitle = title,
            summaryLanguage = language
        )
        return SummaryCacheDBHelper.addSummaryCache(summaryCache)
    }

    private fun storeThemeList(themeInfo: String) {
        if (themeInfo.isEmpty()) {
            return
        }
        PrefUtil.putString(BaseApplication.getAppContext(), SUMMARY_SUPPORT_THEME, themeInfo)
    }

    private fun addRecordSummaryEvent(theme: Int) {
        //执行录音摘要场景埋点上报
        CoroutineScope(Dispatchers.IO).launch {
            val summarySize = SummaryCacheDBHelper.getAllSummaryByMediaId(mediaId).size
            val ifFirst = if (summarySize > 1) {
                AISummaryBuryingUtil.VALUE_IS_NOT_FIRST
            } else {
                AISummaryBuryingUtil.VALUE_IS_FIRST
            }
            val type = when (theme) {
                SummaryTheme.MEETING -> AISummaryBuryingUtil.VALUE_TYPE_MEETING
                SummaryTheme.CLASSROOM -> AISummaryBuryingUtil.VALUE_TYPE_NOTE
                SummaryTheme.INTERVIEW -> AISummaryBuryingUtil.VALUE_TYPE_INTERVIEW
                SummaryTheme.PHONE -> AISummaryBuryingUtil.VALUE_TYPE_CALL
                SummaryTheme.NORMAL_SIMPLE -> AISummaryBuryingUtil.VALUE_TYPE_SIMPLE
                SummaryTheme.NORMAL -> AISummaryBuryingUtil.VALUE_TYPE_DETAIL
                else -> AISummaryBuryingUtil.VALUE_TYPE_DETAIL
            }
            addRecordSummaryScene(mediaId.toString(), ifFirst, type)
        }
    }
}