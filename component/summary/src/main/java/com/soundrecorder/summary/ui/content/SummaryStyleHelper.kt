/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryStyleHelper
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui.content

import android.content.Context
import android.net.Uri
import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.ext.durationInMsFormatTimeExclusive
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.summary.R
import com.soundrecorder.summary.data.SummaryDataParser
import com.soundrecorder.summary.model.SummaryAgentEvent
import com.soundrecorder.summary.model.SummaryEntity
import com.soundrecorder.summary.model.SummaryTrace
import com.soundrecorder.summary.util.SummaryConditionChecker
import java.util.regex.Pattern

@Suppress("StringTemplate")
object SummaryStyleHelper {
    private const val TAG = "SummaryStyleHelper"
    private const val LIST_SYMBOL = "\n   - "
    private const val LIST_SYMBOL_REPLACE = "\n * "
    private const val TASK_SYMBOL = "- [ ] "
    private const val TASK_DONE_SYMBOL = "- [x] "
    private const val LINE_BREAKS = "\n"

    private val taskListPattern = Pattern.compile("^- \\[([xX\\s])]\\s+.*")

    @JvmStatic
    fun formatSummaryContent(
        context: Context,
        originText: String,
        trace: List<SummaryTrace>,
        entities: List<SummaryEntity>,
        agents: List<SummaryAgentEvent>,
    ): String {
        val newTextReplaceListSymbol = formatSummaryContentByList(originText)
        val newTextWithTaskList =
            formatSummaryContentByTask(context, newTextReplaceListSymbol, agents)
        val newTextWithTrace = formatSummaryContentByTrace(newTextWithTaskList, trace)
        val newTextWithSummaryEntity =
            formatSummaryContentBySummaryEntity(newTextWithTrace, entities)
        return newTextWithSummaryEntity
    }

    @JvmStatic
    private fun formatSummaryContentByTrace(
        originText: String,
        traces: List<SummaryTrace>
    ): String {
        if (traces.isEmpty()) {
            return originText
        }
        val replacements = traces.map {
            it.traceText to traceFormat(it)
        }
        var result = originText
        for ((target, append) in replacements) {
            result = result.replace(target, target + append)
        }
        return result
    }

    @JvmStatic
    private fun traceFormat(trace: SummaryTrace): String {
        val time = trace.startTime.durationInMsFormatTimeExclusive()
        val entityObject = GsonUtil.getGson().toJson(trace)
        return "[`$time`](#$entityObject)"
    }

    @JvmStatic
    private fun formatSummaryContentBySummaryEntity(
        originText: String,
        originEntities: List<SummaryEntity>
    ): String {
        val entities = originEntities
        if (entities.isEmpty()) {
            return originText
        }
        val taskListItemPattern = taskListPattern
        val pattern = "(?:" + entities.joinToString("|") { "\\Q${it.name}\\E" } + ")"
        val linkMap = entities.associate { it.name to linkFormat(it) }
        val lines = originText.split(LINE_BREAKS)
        val transformedLines = lines.map { line ->
            val matcher = taskListItemPattern.matcher(line)
            if (matcher.find()) {
                line
            } else {
                Regex(pattern).replace(line) { matchResult ->
                    val name = matchResult.value
                    linkMap.getOrDefault(name, line)
                }
            }
        }
        return transformedLines.joinToString(LINE_BREAKS)
    }

    @JvmStatic
    private fun linkFormat(entity: SummaryEntity): String {
        val entityObject = GsonUtil.getGson().toJson(entity)
        return "[${entity.name}]($entityObject)"
    }

    @JvmStatic
    private fun formatSummaryContentByList(originText: String): String {
        return originText.replace(LIST_SYMBOL, LIST_SYMBOL_REPLACE)
    }

    @JvmStatic
    private fun formatSummaryContentByTask(
        context: Context,
        originText: String,
        agents: List<SummaryAgentEvent>
    ): String {
        var agentStart: String
        val agentEnd: String
        if (BaseUtil.isEXP()) {
            agentStart = context.getString(R.string.summary_agent_export)
            agentEnd = context.getString(R.string.summary_agent_export_end)
        } else {
            agentStart = context.getString(R.string.summary_agent)
            val index = originText.indexOf(agentStart)
            if (index == -1) {
                agentStart = context.getString(R.string.summary_agent_action)
            }
            if (index == -1) {
                agentStart = context.getString(R.string.summary_agent_1)
            }
            agentEnd = context.getString(R.string.summary_agent_end)
        }
        val startIndex = originText.indexOf(agentStart)
        if (startIndex == -1) {
            return originText
        }
        val endIndex = originText.indexOf(agentEnd, startIndex + agentStart.length)
        val subStringB = if (endIndex == -1) {
            originText.substring(startIndex + agentStart.length)
        } else {
            originText.substring(startIndex + agentStart.length, endIndex)
        }

        val lines = subStringB.split(LINE_BREAKS)
        val processedLines = lines.mapNotNull { line ->
            val cleanedLine = line.trimStart { it !in 'a'..'z' && it !in 'A'..'Z' && it !in '\u4e00'..'\u9fa5' }
            if (cleanedLine.isEmpty()) {
                null
            } else {
                val agent = agents.singleOrNull {
                    val formatAgent = SummaryDataParser.replaceEntity(cleanedLine)
                    it.agent == formatAgent
                }
                agent?.let { formatTask(agent.isDone, cleanedLine) } ?: run { null }
            }
        }
        val result =
            originText.substring(
                0,
                startIndex
            ) + agentStart + LINE_BREAKS + processedLines.joinToString(LINE_BREAKS)
        return result
    }

    @JvmStatic
    private fun formatTask(isDone: Boolean, agent: String): String {
        return if (isDone) {
            "$TASK_DONE_SYMBOL$agent"
        } else {
            "$TASK_SYMBOL$agent"
        }
    }

    @JvmStatic
    fun updateStyleAfterTaskStateChange(originText: String, isDone: Boolean, task: String): String {
        val checkboxUnchecked = "$TASK_SYMBOL$task"
        val checkboxChecked = "$TASK_DONE_SYMBOL$task"
        val updated = if (isDone && originText.contains(checkboxUnchecked)) {
            checkboxChecked
        } else if (isDone.not() && originText.contains(checkboxChecked)) {
            checkboxUnchecked
        } else {
            task
        }
        return originText.replace(checkboxUnchecked, updated).replace(checkboxChecked, updated)
    }

    @JvmStatic
    suspend fun prepareHtmlNoteContent(
        summaryOriginText: String,
        agent: List<SummaryAgentEvent>?,
        summaryEntity: List<SummaryEntity>?,
        uri: Uri,
        recordTitle: String,
        context: Context
    ): String {
        DebugUtil.d(TAG, "prepareHtmlNoteContent: summaryOriginText length=${summaryOriginText.length}, " +
                    "agent size=${agent?.size}, entity size=${summaryEntity?.size}, uri=$uri")

        // 处理待办事项剔除
        val processedText = if (!agent.isNullOrEmpty()) {
            removeTaskContentFromSummary(context, summaryOriginText)
        } else {
            summaryOriginText
        }

        // 转换为HTML格式
        val htmlContent = formatSummaryContentAsHtml(processedText)

        // 添加待办事项HTML
        val htmlWithTasks = if (!agent.isNullOrEmpty()) {
            addTasksAsHtml(htmlContent, agent)
        } else {
            htmlContent
        }

        return if (SummaryConditionChecker.isNoteSupportAudio(context)) {
            // 添加录音文件附件
            buildFileAttachmentHtml(htmlWithTasks, uri, recordTitle)
        } else {
            htmlWithTasks
        }
    }

    /**
     * 从摘要原文中剔除待办事项内容，保留"待办事项"标题
     */
    @JvmStatic
    private fun removeTaskContentFromSummary(context: Context, originText: String): String {
        var agentStart: String
        val agentEnd: String

        // 根据内外销确定待办事项标识符
        if (BaseUtil.isEXP()) {
            agentStart = context.getString(R.string.summary_agent_export)
            agentEnd = context.getString(R.string.summary_agent_export_end)
        } else {
            agentStart = context.getString(R.string.summary_agent)
            val index = originText.indexOf(agentStart)
            if (index == -1) {
                agentStart = context.getString(R.string.summary_agent_action)
            }
            agentEnd = context.getString(R.string.summary_agent_end)
        }

        val startIndex = originText.indexOf(agentStart)
        if (startIndex == -1) {
            return originText
        }

        val endIndex = originText.indexOf(agentEnd, startIndex + agentStart.length)

        return if (endIndex == -1) {
            originText.substring(0, startIndex + agentStart.length)
        } else {
            originText.substring(0, startIndex + agentStart.length) + originText.substring(endIndex)
        }
    }

    /**
     * 将摘要内容转换为HTML格式
     * 处理标题、数字开头的文本、短横线开头的文本
     */
    @JvmStatic
    private fun formatSummaryContentAsHtml(originText: String): String {
        val cleanText = originText.replace("*", "")
        val lines = cleanText.split("\n").map { it.trim() }.filter { it.isNotEmpty() }
        val htmlBuilder = StringBuilder()
        htmlBuilder.append("<html><body>")

        // 使用函数式编程方法处理行，避免while循环
        processLinesAsHtml(lines, htmlBuilder)

        // 关闭HTML标签
        htmlBuilder.append("</body></html>")
        return htmlBuilder.toString()
    }

    /**
     * 处理文本行并转换为HTML格式
     * 使用递归和函数式方法避免while循环
     */
    @JvmStatic
    private fun processLinesAsHtml(lines: List<String>, htmlBuilder: StringBuilder) {
        if (lines.isEmpty()) return

        val groupedLines = groupConsecutiveLines(lines)

        groupedLines.forEach { group ->
            when (group.type) {
                LineType.DASH_LIST -> {
                    // 处理连续的短横线列表项
                    htmlBuilder.append("<ul>\n")
                    group.lines.forEach { item ->
                        val content = item.substring(1).trim() // 移除开头的"-"
                        htmlBuilder.append("  <li>").append(content).append("</li>\n")
                    }
                    htmlBuilder.append("</ul>\n")
                }
                LineType.NUMBERED_TITLE -> {
                    // 处理编号标题
                    group.lines.forEach { line ->
                        htmlBuilder.append("<ul>").append(line).append("</ul>\n")
                    }
                }
                LineType.REGULAR_TITLE -> {
                    // 处理普通标题
                    group.lines.forEach { line ->
                        htmlBuilder.append("<ul>").append(line).append("</ul>\n")
                    }
                }
                LineType.DEFAULT -> {
                    // 处理默认段落
                    group.lines.forEach { line ->
                        htmlBuilder.append("<ul>").append(line).append("</ul>\n")
                    }
                }
            }
        }
    }

    /**
     * 行类型枚举
     */
    private enum class LineType {
        DASH_LIST,      // 短横线列表项
        NUMBERED_TITLE, // 编号标题
        REGULAR_TITLE,  // 普通标题
        DEFAULT         // 默认段落
    }

    /**
     * 行分组数据类
     */
    private data class LineGroup(
        val type: LineType,
        val lines: List<String>
    )

    /**
     * 将连续的相同类型行分组
     * 使用函数式编程方法，避免复杂的循环逻辑
     */
    @JvmStatic
    private fun groupConsecutiveLines(lines: List<String>): List<LineGroup> {
        if (lines.isEmpty()) return emptyList()

        val groups = mutableListOf<LineGroup>()
        var currentGroup = mutableListOf<String>()
        var currentType: LineType? = null

        lines.forEach { line ->
            val lineType = determineLineType(line)

            if (currentType == null) {
                // 第一行，初始化
                currentType = lineType
                currentGroup.add(line)
            } else if (currentType == lineType && lineType == LineType.DASH_LIST) {
                // 连续的短横线列表项，继续添加到当前组
                currentGroup.add(line)
            } else {
                // 类型变化或非短横线列表项，结束当前组并开始新组
                if (currentGroup.isNotEmpty()) {
                    groups.add(LineGroup(currentType!!, currentGroup.toList()))
                }
                currentGroup = mutableListOf(line)
                currentType = lineType
            }
        }

        // 添加最后一组
        if (currentGroup.isNotEmpty() && currentType != null) {
            groups.add(LineGroup(currentType!!, currentGroup.toList()))
        }

        return groups
    }

    /**
     * 确定行的类型
     * 使用纯函数方法，避免复杂的条件判断
     */
    @JvmStatic
    private fun determineLineType(line: String): LineType {
        return when {
            line.startsWith("-") -> LineType.DASH_LIST
            line.matches(Regex("^\\d+\\.\\s*.*")) -> LineType.NUMBERED_TITLE
            !line.matches(Regex("^\\d+\\..*")) && !line.startsWith("-") -> LineType.REGULAR_TITLE
            else -> LineType.DEFAULT
        }
    }

    /**
     * 在HTML内容中添加待办事项的checkbox格式
     */
    @JvmStatic
    private fun addTasksAsHtml(htmlContent: String, agents: List<SummaryAgentEvent>): String {
        if (agents.isEmpty()) {
            DebugUtil.d(TAG, "addTasksAsHtml: agents is empty, returning original content")
            return htmlContent
        }

        val taskHtml = StringBuilder()
        taskHtml.append("<ul>\n")
        agents.forEachIndexed { index, agent ->
            DebugUtil.d(TAG, "addTasksAsHtml: processing agent[$index]: ${agent.agent}")
            val checked = if (agent.isDone) { "checked" } else { "unchecked" }
            taskHtml.append("  <li class=\"$checked\">").append(agent.agent).append("</li>\n")
        }
        taskHtml.append("</ul>\n")

        val result = htmlContent.replace("</body></html>", "$taskHtml </body></html>")
        DebugUtil.d(TAG, "addTasksAsHtml: result length=${result.length}, original length=${htmlContent.length}")
        return result
    }

    /**
     * 构建录音文件附件的HTML
     */
    @JvmStatic
    private fun buildFileAttachmentHtml(htmlContent: String, uri: Uri, recordTitle: String): String {
        val attachmentHtml = "<div><input type='audio' src='$uri' alt='$recordTitle' /></div>"
        return htmlContent.replace("</body></html>", "$attachmentHtml</body></html>").also {
            DebugUtil.d(TAG, "buildFileAttachmentHtml: result length=${it.length}, original length=${htmlContent.length}")
        }
    }
}