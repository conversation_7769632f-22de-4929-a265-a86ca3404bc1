/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        : INotification
 * * Description : notification接口
 * * Version     : 1.0
 * * Date        : 2022/8/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.notification.base

import android.app.Service
import android.content.Intent
import android.widget.RemoteViews

interface INotification {

    fun showNotification(service: Service?)

    fun setRemoteViewData(remoteViews: RemoteViews)

    fun observeData()

    fun sendNotification(inThread: Boolean)

    fun onRelease()

    fun cancelNotification()

    fun getLayoutId(): Int

    fun getFoldLayoutId(): Int?

    fun getJumpIntent(): Intent?

    fun getChannelName(): String?

    fun getChannelId(): String?

    fun getOldChannelId(): String

    fun getNotificationId(): Int

    fun getGroupId(): Int

    /**
     * @return first：显示文案 second：talkback描述
     */
    fun getContentTitle(): Pair<String, String> {
        return Pair("", "")
    }

    /**
     * @return first：显示文案 second：talkback描述
     */
    fun getContentText(): Pair<String, String> {
        return Pair("", "")
    }
}