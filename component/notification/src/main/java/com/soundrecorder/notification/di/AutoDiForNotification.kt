/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  AutoDiForNotification.kt
 * * Description : AutoDiForNotification
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.notification.di

import com.soundrecorder.modulerouter.notification.NotificationInterface
import com.soundrecorder.notification.NotificationApi
import org.koin.dsl.module

object AutoDiForNotification {
    val notificationModule = module {
        single<NotificationInterface>(createdAtStart = true) {
            NotificationApi
        }
    }
}