/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SellData
 * Description:
 * Version: 1.0
 * Date: 2024/4/19
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/4/19 1.0 create
 */

package com.soundrecorder.sellmode

import androidx.annotation.Keep
import com.soundrecorder.common.databean.NoteData

@Keep
data class SellData(val convertFileAbsPath: String?, val noteData: NoteData?)