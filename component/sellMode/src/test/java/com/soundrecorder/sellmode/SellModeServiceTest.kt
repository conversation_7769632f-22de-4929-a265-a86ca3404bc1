/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: SellModeServiceTest
 Description:
 Version: 1.0
 Date: 2022/12/13
 Author: W9013333(v-z<PERSON><PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/12/13 1.0 create
 */

package com.soundrecorder.sellmode

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.db.CursorHelper
import com.soundrecorder.sellmode.SellModeService.Companion.checkAndStartSellModeService
import com.soundrecorder.sellmode.shadow.ShadowFeatureOption
import com.soundrecorder.sellmode.shadow.ShadowOplusCompactUtil
import io.mockk.spyk
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config
import java.io.File

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class, ShadowOplusCompactUtil::class])
class SellModeServiceTest {
    @Test
    fun mimeTypeTest() {
        val sellModeService = spyk(SellModeService())
        val newFile = File("mp3.mp3")
        if (!newFile.exists()) {
            newFile.createNewFile()
        }
        val mimeType = Whitebox.invokeMethod<String>(sellModeService, "mimeType", newFile)
        Assert.assertTrue(mimeType !in CursorHelper.getsAcceptableAudioTypes())
        newFile.delete()
    }

    @Test
    fun clearDataBySellModeTest() {
        val sellModeService = spyk(SellModeService())
        Whitebox.invokeMethod<Unit>(sellModeService, "clearDataBySellMode")
    }

    @Test
    fun copeAudioBySellModeTest() {
        val sellModeService = spyk(SellModeService())
        val newFile = File("mp3.mp3")
        if (!newFile.exists()) {
            newFile.createNewFile()
        }
        Whitebox.invokeMethod<Unit>(sellModeService, "copeAudioBySellMode", newFile)
        newFile.delete()
    }

    @Test
    fun copeConvertBySellModeTest() {
        val sellModeService = spyk(SellModeService())
        val newFile = File("mp31.mp3")
        if (!newFile.exists()) {
            newFile.createNewFile()
        }
        Whitebox.invokeMethod<Unit>(sellModeService, "copeConvertBySellMode", newFile)
        newFile.delete()
    }

    @Test
    fun onCreateTest() {
        val sellModeService = spyk(SellModeService())
        Whitebox.setInternalState(sellModeService, "audioToConvertMap", mutableMapOf("mp3.mp3" to "mp3.txt"))
        Whitebox.invokeMethod<Unit>(sellModeService, "onCreate")
    }

    @Test
    fun checkAndStartSellModeServiceTest() {
        val context: Context = ApplicationProvider.getApplicationContext()
        context.checkAndStartSellModeService()
    }
}