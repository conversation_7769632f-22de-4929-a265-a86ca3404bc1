/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: IAiAsrClient
 * Description:
 * Version: 1.0
 * Date: 2025/3/18
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2025/3/18 1.0 create
 */

package com.soundrecorder.translate.asr.file

import android.os.Bundle
import com.soundrecorder.translate.asr.listener.IFileAsrListener

interface IFileAsrKit {
    companion object {
        /*发送音频间隔时间*/
        const val DEFAULT_VALUE_REQUEST_DURATION = 60000L
        /*查询文本间隔时间*/
        const val DEFAULT_VALUE_QUERY_DURATION = 10000L
        /*发送超时时间*/
        const val DEFAULT_VALUE_END_TIMEOUT = 15000L

        const val ASR_PARAM_CALL_ID = "asr_param_call_id"
    }

    fun registerAsrListener(mediaId: Long, listener: IFileAsrListener?)
    fun unRegisterAsrListener(mediaId: Long, listener: IFileAsrListener?)

    /**
     * 初始化asr
     */
    fun initAsr(params: Bundle?, initResult: ((result: Boolean) -> Unit))

    /**
     * 开始asr通道
     */
    fun startAsr(extra: Bundle?)

    /**
     * 停止asr通道
     */
    fun stopAsr(recordId: Long? = null)

    /**
     * 重试asr
     */
    fun retryAsr(recordId: Long)

    /**
     * 释放asr
     */
    fun release(id: String? = null)
}