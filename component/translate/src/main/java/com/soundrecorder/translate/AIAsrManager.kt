/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: AIAsrManager
 * Description:
 * Version: 1.0
 * Date: 2025/3/25
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/3/25 1.0 create
 */

package com.soundrecorder.translate

import android.content.Context
import android.util.Log
import androidx.lifecycle.MutableLiveData
import com.oplus.aiunit.toolkits.AISettings
import com.oplus.aiunit.toolkits.callback.RequestCallback
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.OS12FeatureUtil
import com.soundrecorder.common.task.AppTaskUtil
import com.soundrecorder.modulerouter.smartname.SmartNameAction
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.translate.asr.realtime.AIRealTimeAsrClient
import java.util.Locale

/**
 * asr 接口文档：
 * https://odocs.myoas.com/docs/wV3VVxNB4MsDKv3y
 */
object AIAsrManager {
    private const val TAG = "AIAsrManager"
    const val DETECT_NAME_ASR = "ai_asr_detector"
    const val UNIT_NAME_ASR = "ai_asr_cloud"
    const val SCENE_NAME_ASR = "ai_asr_scene"
    /*算法场景asr+智能标题合集*/
    const val SCENE_NAME_ASR_AND_SMART_TITLE = "unified_asr_summary"
    const val UNIT_ID_ASR = 186654726
    /*智能标题 detectorName、sceneName*/
    const val DETECT_UNIFIED_SUMMARY = "unified_summary"
    const val SCENE_UNIFIED_SUMMARY = "unified_summary"
    /*需要下载 ai_asr_dector 和 unified_summary 需要填 sceneName：unified_asr_summary*/
    const val SCENENAME_UNIFIED_ASR_SUMMARY = "unified_asr_summary"
    var supportAIAsr = MutableLiveData<Boolean>()
        private set
    private var country: String? = Locale.getDefault().country

    private var supportSmartName: Boolean? = null

    @JvmStatic
    fun loadSupportAIAsrByCountry(newCountry: String?) {
        if (newCountry != country) {
            isSupportAIAsr(BaseApplication.getAppContext(), true)
            country = newCountry
        }
    }

    @JvmStatic
    fun isSupportAIAsr(context: Context, forceUpdate: Boolean = false): Boolean {
        if (supportAIAsr.value != null && !forceUpdate) {
            return supportAIAsr.value ?: false
        }
        if (!OS12FeatureUtil.isSupportASRFeature()) {
            Log.d(TAG, "isSupportAIAsr false")
            if (supportAIAsr.value != false) {
                supportAIAsr.postValueSafe(false)
            }
            return false
        }
        kotlin.runCatching {
            val detectInfo = AISettings.getDetectData(context, DETECT_NAME_ASR)
            val supportAsr = detectInfo.isSupport
            if (supportAIAsr.value != supportAsr) {
                supportAIAsr.postValueSafe(supportAsr)
            }
            Log.i(TAG, "isSupportAIAsr, state: ${detectInfo.state}, isSupport: ${detectInfo.isSupport},available=${detectInfo.isAvailable}")
        }.onFailure {
            Log.e(TAG, "isSupportAIAsr, error: ${it.message}")
        }
        return supportAIAsr.value ?: false
    }


    @JvmStatic
    fun canRunRealTimeAsr(context: Context): Boolean {
        return Injector.injectFactory<SmartNameAction>()?.checkSupportSmartName(context, null) == true &&
                isSupportAIAsr(context, true) &&
                AIDownloadManager.isAiModeDownloadSuccess(DETECT_NAME_ASR, SCENE_NAME_ASR_AND_SMART_TITLE)
    }

    /**
     * 是否支持智能标题
     */
    fun isSupportSmartName(context: Context, forceUpdate: Boolean = false): Boolean {
        if (supportSmartName != null && !forceUpdate) {
            return supportSmartName ?: false
        }
        kotlin.runCatching {
            val detectInfo = AISettings.getDetectData(context, DETECT_UNIFIED_SUMMARY)
            DebugUtil.i(TAG, "isSupportSmartName, state: ${detectInfo.state}, isSupport: ${detectInfo.isSupport},available=${detectInfo.isAvailable}")
            supportSmartName = detectInfo.isSupport
            AppTaskUtil.isSupportSmartName = supportSmartName ?: false
        }.onFailure {
            DebugUtil.e(TAG, "isSupportSmartName, error: ${it.message}")
            return false
        }
        return supportSmartName ?: false
    }

    /**
     * 算法是否运行在端侧
     * runType： 0是端，1是云
     *
     */
    @JvmStatic
    fun isPluginRunLocal(context: Context, detectName: String): Boolean {
        runCatching {
            return AISettings.getDetectData(context, detectName).runType == 0
        }
        return false
    }

    /**
     * 查询能力是否支持
     */
    @JvmStatic
    fun isDetectSupported(context: Context, detectName: String = DETECT_UNIFIED_SUMMARY): Boolean {
        return AISettings.isDetectSupported(context, detectName)
    }

    @JvmStatic
    fun checkUpdateAIUnitConfigBackground(context: Context) {
        if (!OS12FeatureUtil.isColorOS16OrLater() || isSupportAIAsr(context, true)) {
            DebugUtil.d(TAG, "check update return <os16 or support true")
            return
        }
        DebugUtil.d(TAG, "checkUpdateAIUnitConfigBackground")
        if (BaseUtil.isEXP()) {
            AISettings.requestConfigurationUpdate(context, object :
                    RequestCallback {
                override fun onResult(resultCode: Int) {
                    val state = isSupportAIAsr(context, true)
                    DebugUtil.d(TAG, "checkUpdateAIUnitConfigBackground: onResult $resultCode , $state")
                }
            })
        }
    }

    /**
     * 获取ASR支持的语种，禁止在录制界面调用。
     */
    @JvmStatic
    fun getSupportLanguage(context: Context,  callback: (Map<String, String>?) -> Unit) {
        if (!isSupportAIAsr(context, true)) {
            callback.invoke(null)
            return
        }
        AIRealTimeAsrClient(context).getSupportLanguage(callback)
    }
}