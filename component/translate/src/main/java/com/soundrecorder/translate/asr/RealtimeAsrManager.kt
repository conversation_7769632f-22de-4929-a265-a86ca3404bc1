/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RealtimeConvertManager
 * Description:
 * Version: 1.0
 * Date: 2025/3/6
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2025/3/6 1.0 create
 */

package com.soundrecorder.translate.asr

import android.os.Bundle
import android.os.SystemClock
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.realtimeasr.IRealtimeSubtitleCache
import com.soundrecorder.common.realtimeasr.OnRealtimeListener
import com.soundrecorder.common.realtimeasr.RealTimeAsrStatus
import com.soundrecorder.common.realtimeasr.RealTimeAsrStatusConst
import com.soundrecorder.translate.asr.bean.AsrResult
import com.soundrecorder.translate.asr.listener.IRealTimeAsrListener
import com.soundrecorder.translate.asr.realtime.AIRealTimeAsrClient
import com.soundrecorder.translate.asr.realtime.AIRealTimeAsrClient.Companion.EXTRA_SOURCE_LANGUAGE
import com.soundrecorder.translate.asr.realtime.MergeContentHelper
import com.soundrecorder.translate.asr.realtime.RealTimeAsrParser
import com.soundrecorder.translate.asr.realtime.RealTimeAsrParser.Companion.STATUS_NET_CONNECTED
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.Locale
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentMap

class RealtimeAsrManager {

    companion object {
        private const val TAG = "RealtimeAsrManager"

        private const val DEFAULT_SPEAKER_ID = 1
        private const val UNSUPPORTED_SPEAKER_ID = -1
        private const val PROCESS_SPEAKER_ID = 0
        private const val DELAY_UPDATE_TIME = 800L
    }

    private var asrClient: AIRealTimeAsrClient? = null
    private var listener: IRealTimeAsrListener? = null
    private val asrResultMap = LinkedHashMap<String, ConvertContentItem>()

    var asrCallback: RealtimeAsrCallback? = null
    var offsetManager: RealtimeOffsetManager? = null

    /*监听ASR结果*/
    private var rtAsrListener: OnRealtimeListener? = null

    /*ASR转写缓存*/
    private var rtAsrCache: RealtimeSubtitleCacheImpl? = null

    /*记录startAsr的成功结果、调用stop后，该值修改为false*/
    private val startAsrResultMap: ConcurrentMap<String, Boolean> = ConcurrentHashMap()

    /*记录initAsr结果，true：成功 false：失败*/
    private val initAsrResultMap: ConcurrentMap<String, Boolean> = ConcurrentHashMap()

    /*记录调用stopAsr，true：已经调用过了，false：还未调用*/
    private val stopAsrFlagMap: ConcurrentMap<String, Boolean> = ConcurrentHashMap()

    private var isDestroy = false

    @Volatile
    var isError = false

    /*ASR的当前状态*/
    private var asrStatus: RealTimeAsrStatus = RealTimeAsrStatus.ASR_DEFAULT

    /*讲话人ID到名字的映射表*/
    private val speakerIdToNameMap = ConcurrentHashMap<Int, String>()

    /*合并ASR转写数据帮助类*/
    private val mergeContentHelper: MergeContentHelper by lazy { MergeContentHelper() }

    /*记录最后一个转写数据的文本最长长度，用于处理字数骤降的场景*/
    private var lastContentMaxLength = -1

    /*延迟刷新的任务*/
    private var refreshJob: Job? = null

    init {
        runCatching {
            val startTime = SystemClock.elapsedRealtime()
            asrClient = AIRealTimeAsrClient(BaseApplication.getAppContext())
            DebugUtil.d(TAG, "new client spend ${SystemClock.elapsedRealtime() - startTime}")
        }.onFailure {
            DebugUtil.e(TAG, "init error $it")
        }
        listener = object : IRealTimeAsrListener {
            override fun onStatus(channelId: String?, bizType: String?, code: Int?, msg: String?) {
                /**流程结束，忽略状态回调*/
                if (isDestroy) {
                    DebugUtil.i(TAG, "on status return destory")
                    return
                }

                when (bizType) {
                    RealTimeAsrParser.BIZ_TYPE_START_ASK -> {
                        if (code == 0) { // startAsr 成功回调
                            channelId?.let {
                                startAsrResultMap[it] = true
                                asrStatus = RealTimeAsrStatus.ASR_RUNNING
                            }
                            return
                        }
                    }

                    RealTimeAsrParser.BIZ_TYPE_END_ASK -> {
                        asrCallback?.onStopAsrResult(code == 0, channelId)
                        if (code == 0) { // stopAsr 成功回调
                            return
                        }
                    }

                    RealTimeAsrParser.BIZ_TYPE_AUDIO -> { // 发送音频数据错误
                        if (code == RealTimeAsrParser.ERROR_NOT_FOUND_CHANNEL_ID && stopAsrFlagMap[channelId] == true) {
                            /*忽略发送数据，通道被关闭的场景*/
                            return
                        }
                    }

                    RealTimeAsrParser.BIZ_TYPE_SERVER_END_ASK -> { // 服务端主动断开链路标志
                        DebugUtil.w(TAG, "on status server end")
                        channelId?.let {
                            if (startAsrResultMap[it] == true) {
                                startAsrResultMap[it] = false
                            }
                        }
                        asrStatus = RealTimeAsrStatus.ASR_DEFAULT
                        isError = true
                        asrCallback?.onError(channelId, code, msg)
                        rtAsrListener?.onAsrStatus(RealTimeAsrStatusConst.STATUS_SERVER_DISCONNECT)
                        return
                    }
                }

                when (code) {
                    RealTimeAsrParser.STATUS_INIT_SUCCESS -> {
                        initAsrResultMap[channelId] = true
                        asrStatus = RealTimeAsrStatus.ASR_INITIALIZING
                        asrCallback?.onInitResult(channelId, true)
                        rtAsrListener?.onAsrStatus(code)
                        return
                    }

                    RealTimeAsrParser.STATUS_INIT_ERROR -> {
                        initAsrResultMap[channelId] = false
                        asrStatus = RealTimeAsrStatus.ASR_DEFAULT
                        asrCallback?.onInitResult(channelId, false)
                        rtAsrListener?.onAsrStatus(code)
                        return
                    }
                    /**网络已连接*/
                    STATUS_NET_CONNECTED -> return
                    else -> {
                        if (channelId?.isNotBlank() == true && stopAsrFlagMap[channelId] == true) {
                            DebugUtil.d(TAG, "ignore error by stop")
                            return
                        }
                        isError = true
                        asrStatus = RealTimeAsrStatus.ASR_DEFAULT
                        asrCallback?.onError(channelId, code, msg)
                        rtAsrListener?.onAsrStatus(code ?: -1)
                    }
                }
            }

            override fun onAsrResult(channelId: String?, asrResult: AsrResult) {
                asrResult.msgId?.let { msgID ->
                    val emptyOffset = offsetManager?.emptyOffsetTime ?: 0
                    val mainRecordingTime = offsetManager?.mainRecordingTime ?: 0
                    val realOffset = mainRecordingTime - emptyOffset
                    if (asrResultMap[msgID] == null) {
                        lastContentMaxLength = -1
                        // 当下一句出现的时候，无论因为什么原因导致上一句仍为中间句，都需要把上一个结果设置为最终句。
                        asrResultMap.values.lastOrNull()?.apply {
                            textType = RealTimeAsrParser.ASR_RESULT_TYPE_VAD_FINAL
                        }
                        asrResultMap[msgID] = ConvertContentItem().apply {
                            startTime = asrResult.startOffset.toLong() + realOffset
                            endTime = asrResult.endOffset.toLong() + realOffset
                            textContent = asrResult.text
                            textType = asrResult.type
                            roleId = generateSpeakerIdWithConditions(asrResult.speakId)
                            roleName = getRoleNameByRoleId(roleId)
                        }
                    } else {
                        asrResultMap[msgID]?.apply {
                            if (lastContentMaxLength < textContent.length) {
                                lastContentMaxLength = textContent.length
                            }
                            startTime = asrResult.startOffset.toLong() + realOffset
                            endTime = asrResult.endOffset.toLong() + realOffset
                            textContent = asrResult.text
                            textType = asrResult.type
                            roleId = generateSpeakerIdWithConditions(asrResult.speakId)
                            roleName = getRoleNameByRoleId(roleId)
                        }
                    }
                    // 将数据进行合并处理
                    asrResultMap[msgID]?.let { mergeContentHelper.mergeConvertItem(it) }
                    // 处理数据刷新
                    handleUpdate(asrResult.text.length)
                }
            }

            override fun onTranslationCfgError(channelId: String?, errorCode: Int, errorMsg: String?, isInnerInInvoke: Boolean) {
                rtAsrListener?.onTranslationCfgError(errorCode, errorMsg)
                DebugUtil.d(TAG, "onTranslationCfgError rtAsrListener=$rtAsrListener")
                if (isInnerInInvoke) {
                    asrCallback?.onTranslationCfgError(channelId, errorCode, errorMsg)
                }
            }

            override fun onTranslationCfgSuccess(channelId: String?, parseData: Map<String, String>, isInnerInInvoke: Boolean) {
                rtAsrListener?.onTranslationCfgSuccess(parseData)
                DebugUtil.d(TAG, "onTranslationCfgSuccess rtAsrListener=$rtAsrListener")
                if (isInnerInInvoke) {
                    asrCallback?.onTranslationCfgSuccess(channelId, parseData)
                }
            }
        }
        listener?.let {
            asrClient?.registerAsrListener(it)
        }
        rtAsrCache = RealtimeSubtitleCacheImpl()
    }

    private fun handleUpdate(currentTextSize: Int) {
        val fivePercentOfExpected = mergeContentHelper.mExpectedWord * MergeContentHelper.PERCENTAGE_5
        if (lastContentMaxLength == -1) {
            // 每句话的开始，直接通知刷新
            notifySubtitleUpdate(true)
        } else if (lastContentMaxLength > currentTextSize && lastContentMaxLength - currentTextSize >= fivePercentOfExpected) {
            // 当前的字数比上一次的字数减少了，且减少字数达到预期字数的5%，延时800毫秒通知界面刷新， 如果已经有一个延时刷新任务了，则无需再开启一个
            if (hasPendingDelayTask().not()) {
                refreshJob = CoroutineScope(Dispatchers.Default).launch {
                    delay(DELAY_UPDATE_TIME)
                    notifySubtitleUpdate()
                    refreshJob = null
                }
            }
        } else if (lastContentMaxLength > currentTextSize && lastContentMaxLength - currentTextSize < fivePercentOfExpected) {
            // 当前字数比上一次的字数减少了，减少字数未达到预期字数的5%，通知界面刷新
            notifySubtitleUpdate(true)
        } else if (lastContentMaxLength <= currentTextSize) {
            // 恢复到原字数，立刻通知刷新，如果有一个延时刷新任务了，则直接取消
            notifySubtitleUpdate(true)
        } else {
            // 未知场景，什么都不做
            DebugUtil.i(TAG, "handleUpdate unknown. do nothing")
        }
    }

    private fun notifySubtitleUpdate(isCancelDelay: Boolean = false) {
        if (isCancelDelay && hasPendingDelayTask()) {
            refreshJob?.cancel()
            refreshJob = null
        }
        rtAsrCache?.let { rtAsrListener?.onSubtitleUpdated(it) }
    }

    private fun hasPendingDelayTask(): Boolean = refreshJob?.isActive == true

    fun initAsr(param: Bundle?) {
        DebugUtil.i(TAG, "init asr")
        runCatching {
            asrStatus = RealTimeAsrStatus.ASR_INITIALIZING
            asrClient?.initAsr(param)
        }.onFailure {
            DebugUtil.e(TAG, "initAsr error $it")
            asrStatus = RealTimeAsrStatus.ASR_DEFAULT
        }
    }

    fun startAsr(channelId: String, param: Bundle?) {
        /*已经调用过stopAsr了,避免init回来之前用户点击了暂停*/
        if (stopAsrFlagMap[channelId] == true) {
            DebugUtil.w(TAG, "startAsr return by stop")
            return
        }
        runCatching {
            asrStatus = RealTimeAsrStatus.ASR_STARTING
            asrClient?.startAsr(channelId, param)
            // 将当前的转写的语种给到数据合并类
            val sourceLanguage = param?.getString(EXTRA_SOURCE_LANGUAGE) ?: Locale.CHINESE.language
            mergeContentHelper.refreshExpectedWordByLanguage(sourceLanguage)
        }.onFailure {
            DebugUtil.e(TAG, "startAsr error $it")
            asrStatus = RealTimeAsrStatus.ASR_DEFAULT
        }
    }

    fun getStartAsrResult(channelId: String): Boolean {
        return startAsrResultMap[channelId] ?: false
    }

    fun stopAsr(channelId: String) {
        /*已经调用过stopAsr了*/
        if (stopAsrFlagMap[channelId] == true) {
            return
        }
        runCatching {
            asrStatus = RealTimeAsrStatus.ASR_STOPPING
            asrClient?.stopAsr(channelId)
            stopAsrFlagMap[channelId] = true
            startAsrResultMap[channelId] = false
        }.onFailure {
            DebugUtil.e(TAG, "stopAsr error $it")
            asrStatus = RealTimeAsrStatus.ASR_DEFAULT
        }
    }

    fun processAudioData(channelId: String, data: ByteArray?) {
        runCatching {
            asrClient?.processRealTimeData(channelId, data)
        }.onFailure {
            DebugUtil.e(TAG, "processAudioData error $it")
        }
    }

    fun releaseChannel(channelId: String) {
        runCatching {
            asrStatus = RealTimeAsrStatus.ASR_RELEASING
            asrClient?.releaseChannel(channelId)
        }.onFailure {
            DebugUtil.e(TAG, "release error $it")
            asrStatus = RealTimeAsrStatus.ASR_DEFAULT
        }
    }

    fun release() {
        isDestroy = true
        runCatching {
            asrStatus = RealTimeAsrStatus.ASR_RELEASING
            asrClient?.release()
        }.onFailure {
            DebugUtil.e(TAG, "release error $it")
            asrStatus = RealTimeAsrStatus.ASR_DEFAULT
        }
        listener = null
        asrClient = null
        rtAsrListener = null
        rtAsrCache = null
        offsetManager = null
        speakerIdToNameMap.clear()
        refreshJob?.cancel()
        refreshJob = null
    }

    fun isStopAsr(channelId: String): Boolean {
        return stopAsrFlagMap[channelId] ?: false
    }

    fun getAsrContent(isMergedData: Boolean = true): List<ConvertContentItem> {
        return if (isMergedData) {
            mergeContentHelper.mMergeItemList
        } else {
            asrResultMap.values.toList()
        }
    }

    fun registerRtAsrListener(listener: OnRealtimeListener) {
        rtAsrListener = listener
        DebugUtil.d(TAG, "registerRtAsrListener rtAsrListener=$rtAsrListener")
    }

    fun unregisterRtAsrListener(listener: OnRealtimeListener) {
        if (rtAsrListener == listener) {
            rtAsrListener = null
            DebugUtil.d(TAG, "unregisterRtAsrListener rtAsrListener cleared")
        }
    }

    /**
     * 获取ASR支持的语种
     */
    fun getTranslationConfig(channelId: String, isInnerInInvoke: Boolean = false) {
        asrClient?.getTranslationConfig(channelId, isInnerInInvoke)
    }

    /**
     * 获取ASR当前的状态
     */
    fun getRealtimeAsrStatus(): RealTimeAsrStatus {
        return asrStatus
    }

    /**
     * 修改讲话人名字
     * @param roleId 讲话人ID
     * @param newName 新的讲话人名字
     * @return 是否修改成功
     */
    fun updateSpeakerName(roleId: Int, newName: String): Boolean {
        if (newName.isBlank()) return false

        speakerIdToNameMap[roleId] = newName

        // 更新合并后新数据的讲话人
        mergeContentHelper.mMergeItemList.forEach { item ->
            if (item.roleId == roleId) {
                item.roleName = newName
            }
        }
        DebugUtil.i(TAG, "Updated speaker name: roleId=$roleId, newName=$newName")
        // 通知界面刷新
        notifySubtitleUpdate()
        return true
    }

    /**
     * 获取ASR缓存对象
     */
    fun getRealtimeSubtitleCache(): IRealtimeSubtitleCache? {
        return rtAsrCache
    }

    /**
     * 根据实时ASR识别状态生成讲话人id
     *
     * 1. 如果originalRoleId为空，则返回-1,表示不支持讲话人功能
     * 2. 如果originalRoleId等于PROCESS_SPEAKER_ID，则返回前一句话的讲话人ID,表示正在处理，后续识别完成后会修正
     * 3. 其他情况，表示识别完成的讲话人id，不做处理，直接返回即可
     *
     * @param originalRoleId 原始的讲话人ID
     * @return 生成的讲话人ID
     */
    private fun generateSpeakerIdWithConditions(originalRoleId: Int?): Int = when (originalRoleId) {
        null -> UNSUPPORTED_SPEAKER_ID
        PROCESS_SPEAKER_ID -> {
            getAsrContent(false).lastOrNull { it.textType == RealTimeAsrParser.ASR_RESULT_TYPE_VAD_FINAL }?.roleId
                ?: DEFAULT_SPEAKER_ID
        }

        else -> originalRoleId
    }

    /**
     * 根据讲话人ID获取讲话人名字
     *
     * 1. 如果id为-1，则表示不支持讲话人功能，则返回Null
     * 2. 如果id为0，则尝试从rtAsrCache中获取最后一个生成的讲话人名字，如果不存在，则使用DEFAULT_SPEAKER_ID生成默认名字
     * 3. 对于其他id，查询是否存在讲话人名字，如果有则返回，如果没有则使用讲话人ID生成默认名字
     *
     * @param roleId 讲话人ID
     * @return 讲话人名字，如果不支持则返回null
     */
    private fun getRoleNameByRoleId(roleId: Int): String? = when (roleId) {
        UNSUPPORTED_SPEAKER_ID -> null
        PROCESS_SPEAKER_ID -> {
            getAsrContent(false).lastOrNull { it.textType == RealTimeAsrParser.ASR_RESULT_TYPE_VAD_FINAL }?.roleName
                ?: genRoleNameByRoleId(DEFAULT_SPEAKER_ID)
        }

        else -> speakerIdToNameMap[roleId] ?: genRoleNameByRoleId(roleId)
    }

    /**
     * 根据id生成默认的名字，格式如 讲话人 %d
     */
    private fun genRoleNameByRoleId(roleId: Int): String {
        val text = BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.convert_speaker, roleId)
        return text
    }

    inner class RealtimeSubtitleCacheImpl : IRealtimeSubtitleCache {
        override fun getGeneratedSubtitles(): List<ConvertContentItem> {
            return getAsrContent().filter { it.textType == RealTimeAsrParser.ASR_RESULT_TYPE_VAD_FINAL }
        }

        override fun getTemporySubtitles(): List<ConvertContentItem> {
            return getAsrContent().filter { it.textType == RealTimeAsrParser.ASR_RESULT_TYPE_INTERMEDIATE }
        }
    }
}

interface RealtimeAsrCallback {

    fun onInitResult(channelId: String?, success: Boolean)
    fun onError(channelId: String?, code: Int?, msg: String?)

    fun onStopAsrResult(result: Boolean, channelId: String?)

    fun onTranslationCfgError(channelId: String?, errorCode: Int, errorMsg: String?)

    fun onTranslationCfgSuccess(channelId: String?, data: Map<String, String>)
}

