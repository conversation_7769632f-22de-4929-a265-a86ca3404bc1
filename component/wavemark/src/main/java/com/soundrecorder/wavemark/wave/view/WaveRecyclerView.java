/********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  WaveRecyclerView
 * * Description :
 * * Version     : 1.0
 * * Date        : 2018/11/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.wavemark.wave.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.animation.Interpolator;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.NightModeUtil;
import com.soundrecorder.base.utils.NumberConstant;
import com.soundrecorder.common.utils.CoroutineUtils;
import com.soundrecorder.wavemark.R;
import com.coui.appcompat.contextutil.COUIContextUtil;
import com.soundrecorder.base.utils.ActivityRunnable;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.ScreenUtil;
import com.soundrecorder.common.databean.markdata.MarkDataBean;
import com.soundrecorder.common.utils.ViewUtils;
import com.soundrecorder.wavemark.wave.WaveViewUtil;
import com.soundrecorder.wavemark.wave.WaveViewUtil.WaveType;

import java.lang.ref.WeakReference;
import java.util.List;

public abstract class WaveRecyclerView<T extends WaveItemView> extends RecyclerView implements IWaveItemViewDelegate<T> {

    protected static final int MSG_FRESH_RECYCLER_VIEW = 1;
    protected static final int DURATION = 16;
    protected static final int BOOK_MARK_FRESH_NUMBER = 6;
    protected static final int DEFAULT_SCROLL_LENGTH = -1;
    private static final int DEFAULT_MAX_RECYCLER_COUNT = 5;
    private static final int DEFAULT_NUM_560 = 560;
    private static final String TAG = "WaveRecyclerView";
    private static final float MARK_EXPAND = ViewUtils.dp2px(10, true);

    protected final Interpolator mQuinticInterpolator = new Interpolator() {
        @Override
        public float getInterpolation(float t) {
            return t;
        }
    };

    /**
     * 顶部标记区域的高度
     */
    private final int mMarkViewHeight;
    private final Handler mHandler;

    /**
     * duration from center to left
     */
    protected volatile int mCenterPointDuration;
    /**
     * pixels per millisecond
     */
    protected volatile float mPxPerMs = 0.0F;
    /**
     * duration per pixel
     */
    protected float mMsPerPx = 0.0F;

    protected long mTotalTime = 0;
    protected int mScreenWidth = 0;
    protected long mPreTimeMillis = -1;

    protected volatile int mCenterLineStrokeWidth;
    protected volatile int mWaveViewWidth;
    protected volatile int mFirstWaveViewWidth = 0;
    protected volatile int mTimeLineGap;
    protected Paint mCenterLinePaint = null;
    protected volatile int mCenterLineColor;
    protected int mItemHeight;

    protected MaxAmplitudeSource mMaxAmplitudeSource = null;
    protected List<Integer> mAmplitudeValue;
    protected List<Integer> mDecodedAmplitudeList;
    protected WaveLinearLayoutManager mLayoutManager = null;
    protected WaveAdapter<T> mWaveAdapter = null;
    protected DragListener mDragListener = null;

    protected boolean mCanScrollHorizontally = true;
    protected int mSlop = 0;
    protected float mTouchDownX = 0;
    protected boolean mNightModel;

    protected WaveType mWaveType = WaveType.LARGE;
    //是否显示时间刻度
    private volatile boolean mNeedShowTimeLine = true;

    private MarkDataBean mAddMarkData = null;
    private MarkDataBean mRemoveMarkData = null;

    private boolean mIsDirectOn;
    private String mDirectTime;
    //最近一次开启定向录音的时间，在录制页面使用
    private volatile long mLastDirectOnTime;

    private long mDuration;

    private Runnable mSetSelectTimeRunnable = new ActivityRunnable<View>("", this) {
        @Override
        public void run(View view) {
            if (mMaxAmplitudeSource != null) {
                setSelectTime(mMaxAmplitudeSource.getTime());
            }
        }
    };
    private Runnable mOnLayoutChangeUiRunnable = () -> {
        if (mMaxAmplitudeSource != null) {
            setSelectTime(mMaxAmplitudeSource.getTime());
        }
        if (mWaveAdapter != null) {
            mWaveAdapter.updateWidth(mScreenWidth);
        }
    };
    /**
     * 是否正在触摸标记区域
     */
    private boolean mIsTouchingMarkView = false;

    public WaveRecyclerView(Context context, AttributeSet attrs) {
        super(context, attrs);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.WaveRecyclerView);
        try {
            initTypeAttr(typedArray);
        } catch (Exception e) {
            DebugUtil.e(TAG, "onCreate error", e);
        } finally {
            typedArray.recycle();
        }
        mCenterLineStrokeWidth =
                context.getResources().getDimensionPixelOffset(R.dimen.wave_center_line_stroke_width);
        mTimeLineGap = (int) WaveViewUtil.getTimeLineGapByWaveType(context, mWaveType);
        mWaveViewWidth = mTimeLineGap * WaveViewUtil.TIME_SCALE_COUNT;
        mCenterLineColor = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimary, 0);
        mMarkViewHeight =
                context.getResources().getDimensionPixelOffset(R.dimen.record_wave_view_mark_area_height);

        mHandler = new WaveHandler(this);

        init(context);
        initAdapter(context);
        initSlop(context);
        setOverScrollMode(OVER_SCROLL_ALWAYS);
        DebugUtil.i(TAG, "constructor init end");
    }

    /**
     * 切换为指定的波形图
     *
     * @param waveType 指定的波形图： LARGE / MIDDLE / SMALL
     */
    public void changeWaveType(WaveType waveType) {
        if (waveType == mWaveType) {
            return;
        }
        mWaveType = waveType;
        switch (waveType) {
            case LARGE -> {
                mNeedShowTimeLine = true;
                mFirstWaveViewWidth = mScreenWidth / WaveViewUtil.NUM_TWO;
            }
            case MIDDLE, SMALL -> {
                mNeedShowTimeLine = false;
                mFirstWaveViewWidth = getWidth() / WaveViewUtil.NUM_TWO;
            }
        }

        mCenterLineStrokeWidth =
                getContext().getResources().getDimensionPixelOffset(R.dimen.wave_center_line_stroke_width);
        mTimeLineGap = (int) WaveViewUtil.getTimeLineGapByWaveType(getContext(), mWaveType);
        mPxPerMs = (float) mTimeLineGap / WaveViewUtil.DURATION_INTERVAL;
        mWaveViewWidth = mTimeLineGap * WaveViewUtil.TIME_SCALE_COUNT;
        mCenterLineColor = COUIContextUtil.getAttrColor(getContext(), com.support.appcompat.R.attr.couiColorPrimary, 0);

        mCenterPointDuration =
                (int) (((float) mFirstWaveViewWidth / mTimeLineGap) * WaveViewUtil.DURATION_INTERVAL);

        init(getContext());
        initAdapter(getContext());
        initSlop(getContext());
        setOverScrollMode(OVER_SCROLL_ALWAYS);
    }

    private void initTypeAttr(@NonNull TypedArray array) {
        mNeedShowTimeLine = array.getBoolean(R.styleable.WaveRecyclerView_needShowTimeLine, true);
        int waveType = array.getInt(R.styleable.WaveRecyclerView_waveType, NumberConstant.NUM_1);
        switch (waveType) {
            case NumberConstant.NUM_2:
            case NumberConstant.NUM_3:
                mWaveType = WaveViewUtil.WaveType.SMALL;
                break;
            default:
                mWaveType = WaveViewUtil.WaveType.LARGE;
        }
    }

    public WaveViewUtil.WaveType getWaveType() {
        return mWaveType;
    }

    private void initSlop(Context context) {
        ViewConfiguration vc = ViewConfiguration.get(context);
        mSlop = vc.getScaledTouchSlop();
    }

    private void init(final Context context) {
        initPaint();
        if (BaseUtil.isAndroidQOrLater()) {
            setForceDarkAllowed(false);
        }
        //duration from center to left
        mPxPerMs = (float) mTimeLineGap / WaveViewUtil.DURATION_INTERVAL;
        mMsPerPx = WaveViewUtil.DURATION_INTERVAL / (float) mTimeLineGap;
        mNightModel = NightModeUtil.isNightMode(context);
    }

    private void initAdapter(Context context) {
        mLayoutManager = new WaveLinearLayoutManager(context);
        mLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
        if (BaseApplication.sIsRTLanguage) {
            mLayoutManager.setReverseLayout(true);
        }
        mWaveAdapter = new WaveAdapter<>(context, this);
        mWaveAdapter.setNeedShowTimeLine(mNeedShowTimeLine);
        mWaveAdapter.setWaveType(mWaveType);
        setLayoutManager(mLayoutManager);
        setAdapter(mWaveAdapter);
        if (mScreenWidth != 0) {
            mWaveAdapter.updateWidth(mScreenWidth);
        }
    }

    public void resetWaveData() {
        mScreenWidth = ScreenUtil.INSTANCE.getRealScreenWidth();
        if (mScreenWidth != 0) {
            mWaveAdapter.updateWidth(mScreenWidth);
        }
        DebugUtil.i(TAG, "resetWaveData getRealScreenWidth mWidth: " + mScreenWidth);
        mTimeLineGap = (int) WaveViewUtil.getTimeLineGapByWaveType(getContext(), mWaveType);
        mPxPerMs = (float) mTimeLineGap / WaveViewUtil.DURATION_INTERVAL;
        mNightModel = NightModeUtil.isNightMode(getContext());

        //屏幕宽度发生变化时需要更新这两项
        mFirstWaveViewWidth = mScreenWidth / WaveViewUtil.NUM_TWO;
        mCenterPointDuration =
                (int) (((float) mFirstWaveViewWidth / mTimeLineGap) * WaveViewUtil.DURATION_INTERVAL);
    }

    /**
     * 定向录音中心线变化
     * @param isEnhance
     */
    public void setCenterEnhanceRecording(boolean isEnhance) {
        mCenterLineStrokeWidth = getContext().getResources().getDimensionPixelOffset(isEnhance
                        ? R.dimen.bold_wave_center_line_stroke_width : R.dimen.wave_center_line_stroke_width);
    }

    /**
     * 设置RecycledViewPool的最大回收个数
     * <p>
     * 现象： 在这折叠屏手机录制界面会重复执行onCreateViewHolder()
     * <p>
     * 新插入的View复用过程：
     * 如果在缓存的CacheViews中找到，则直接复用，如果在缓存池RecyclerViewPool中找到，则需要bindView；
     * 如果没有找到可用的ViewHolder，则需要onCreateViewHolder()新建一个VH。
     * 而复用CacheViews时必须是position相同的ViewHolder才能复用，因此新插入的VH只能复用缓存池中的VH。
     * <p>
     * RecycledViewPool默认最大的回收个数是5，而在大屏上面一屏有可能显示6个item,当Pool的MaxScrap小于需要显示的个数时，
     * 从Pool获取的VH有可能为null,导致在大屏上面重复执行onCreateViewHolder()
     *
     * @param screenWidth 屏幕宽度
     * @param itemWidth   波形item宽度
     */
    protected void setMaxRecycledViews(int screenWidth, int itemWidth, int viewType) {
        if ((screenWidth <= 0) || (itemWidth <= 0)) {
            DebugUtil.e(TAG,
                    "setMaxRecycledViews width is illegal argument: screen="
                            + screenWidth + " item=" + itemWidth);
            return;
        }
        int count = (int) Math.ceil(screenWidth * NumberConstant.NUM_F1_0 / itemWidth) + 2;
        DebugUtil.d(TAG, "setMaxRecycledViews screen=" + screenWidth + " item=" + itemWidth + " "
                + "count=" + count);
        if (count > DEFAULT_MAX_RECYCLER_COUNT) { // 5: 默认的RecyclerViewPool的MaxScrap
            getRecycledViewPool().setMaxRecycledViews(viewType, count);
        }
    }

    /**
     * Initialize the paints
     */
    private void initPaint() {
        mCenterLinePaint = new Paint();
        mCenterLinePaint.setAntiAlias(true);
        mCenterLinePaint.setStrokeWidth(mCenterLineStrokeWidth);
        mCenterLinePaint.setColor(mCenterLineColor);
    }

    @Override
    public void stopScroll() {
        DebugUtil.d(TAG, "-----stopScroll");
        super.stopScroll();
    }

    protected void checkMarkRegion(MotionEvent e) {
        getParent().requestDisallowInterceptTouchEvent(true);
        //在触摸标记区域的时候拦截事件，判断是否点击的是缩略图
        if ((e.getAction() == MotionEvent.ACTION_DOWN)
                && (e.getY() > 0)
                && (e.getY() < mMarkViewHeight)
        ) {
            mIsTouchingMarkView = true;
        }
        if (e.getAction() == MotionEvent.ACTION_UP) {
            //按下的时候是在标记区域切抬起手的时候也在标记区域才触发标记的事件
            if (mIsTouchingMarkView) {
                outsideEvent(e);
            }
            mIsTouchingMarkView = false;
        }
    }

    public long getSlideTime(String from) {
        int leftScrollXCalculated = getTotalScrolledLength();
        DebugUtil.d(TAG, "getSlideTime:" + from + "; leftScrollXCalculated = " + leftScrollXCalculated);
        return (leftScrollXCalculated != DEFAULT_SCROLL_LENGTH) ? (long) (leftScrollXCalculated * mMsPerPx) : -1;
    }

    public void setDragListener(DragListener mDragListener) {
        this.mDragListener = mDragListener;
    }

    /**
     * Set whether you can slide
     */
    public void setIsCanScrollTimeRuler(boolean canScroll) {
        mCanScrollHorizontally = canScroll;
    }

    public void setMaxAmplitudeSource(MaxAmplitudeSource source) {
        mMaxAmplitudeSource = source;
    }

    public void setTotalTime(long time) {
        mTotalTime = time;
        mWaveAdapter.setTotalTime(time);
        mWaveAdapter.notifyDataSetChanged();
    }

    /**
     * for play mode
     */
    public synchronized void setSelectTime(long currentTimeMillis) {
        if (mScreenWidth == 0) {
            // onLayout 执行
            DebugUtil.i(TAG, "setSelectTime not layout view mScreenWidth is  0, currentTimeMillis=" + currentTimeMillis);
            return;
        }
        DebugUtil.d(TAG, "setSelectTime: curTime = " + currentTimeMillis + ", centerPointDuration"
                + " =" + mCenterPointDuration);
        updateCenterLinePosition(currentTimeMillis);
        if (currentTimeMillis == 0) {
            mPreTimeMillis = 0;
        } else {
            mPreTimeMillis = -1;
        }
    }

    public void setPreTimeMillis(long preTimeMillis) {
        mPreTimeMillis = preTimeMillis;
    }

    /**
     * update the position of the center point
     */
    protected void updateCenterLinePosition(long currentTimeMillis) {
        DebugUtil.d(TAG, "updateCenterLinePosition" + currentTimeMillis + "; centerPointDuration "
                + "=" + mCenterPointDuration + "; perMs =" + mPxPerMs);
        //the time on the left screen
        long leftTime = currentTimeMillis - mCenterPointDuration;
        //calculate leftTimeIndex and offset based on the left time
        int leftTimeIndex = 0;
        int offset = 0;
        if (leftTime < 0) {
            offset = (int) (currentTimeMillis * mPxPerMs);
        } else if (leftTime == 0) {
            leftTimeIndex = 1;
        } else {
            leftTimeIndex =
                    (int) Math.ceil((double) leftTime / WaveViewUtil.ONE_WAVE_VIEW_DURATION);

            if (leftTime % WaveViewUtil.ONE_WAVE_VIEW_DURATION == 0) {
                leftTimeIndex += 1;
            }
            offset = (int) ((leftTime % WaveViewUtil.ONE_WAVE_VIEW_DURATION) * mPxPerMs);
        }
        offset = -offset;
        //播放计算正确的offset
        offset = calculateCorrectOffset(currentTimeMillis, offset);
        if (mLayoutManager != null) {
            mLayoutManager.scrollToPositionWithOffset(leftTimeIndex, offset);
        }
    }

    protected int calculateCorrectOffset(long currentTimeMillis, int offset) {
        return offset;
    }

    /**
     * 避免播放完成后无法停在最后的位置，所以手动scroll一次
     * 重建场景也需要scroll
     */
    public int getDiffMoreThanLastItemRealWidth() {
        if (mTotalTime == 0) {
            DebugUtil.e(TAG, "getDiffMoreThanLastItemRealWidth return!");
            return 0;
        }
        //最后一个item的实际宽度
        float lastAmplitudeWith =
                (float) Math.ceil(((mTotalTime % WaveViewUtil.ONE_WAVE_VIEW_DURATION) * mPxPerMs));
        //波形的宽度
        float amplitudeWidth = getResources().getDimension(R.dimen.wave_one_amplitude_width);
        //波形之间空格宽度
        float amplitueLineGap = WaveViewUtil.getAmplitueLineGapByWaveType(getContext(), mWaveType);
        //波形+空格宽度
        float oneWaveWidth = amplitudeWidth + amplitueLineGap;
        //最后一个item超出实际宽度的大小
        float diff = lastAmplitudeWith % oneWaveWidth;
        //需要offset的总距离
        lastAmplitudeWith = lastAmplitudeWith + diff;
        int realDiff = (int) (Math.ceil(lastAmplitudeWith));
        DebugUtil.d(TAG, "getDiffMoreThanLastItemRealWidth, realDiff = " + realDiff);
        return realDiff;
    }

    protected boolean isReverseLayout() {
        LayoutManager layoutManager = getLayoutManager();
        if (layoutManager instanceof LinearLayoutManager) {
            return ((LinearLayoutManager) layoutManager).getReverseLayout();
        }
        return false;
    }

    /**
     * 播放/录制过程中波形每隔70ms移动一次
     * <p>
     * 原因是底层player2倍数的时候返回的时间不是线性增长的，
     * 正常应该是每次间隔70ms * 2 =140ms，但是实际可能返回的是几十到两百多的间隔，所以导致波形也不能线性移动。
     */
    public synchronized void startSmoothScroll(long currentTimeMillis) {
        if (mScreenWidth == 0) {
            // onLayout 会执行一次setSelectTime，so这里就忽略
            DebugUtil.i(TAG, "startSmoothScroll not layout view mScreenWidth is  0, currentTimeMillis=" + currentTimeMillis);
            return;
        }
        removeCallbacks(mSetSelectTimeRunnable);
        if (mPreTimeMillis == -1) {
            DebugUtil.d(TAG, "startSmoothScroll: mPreTimeMillis = -1");
        } else {
            int scrolledLength = getTotalScrolledLength();
            if (scrolledLength == DEFAULT_SCROLL_LENGTH) {
                if (Looper.myLooper() == Looper.getMainLooper()) {
                    setSelectTime(currentTimeMillis);
                } else {
                    post(mSetSelectTimeRunnable);
                }
            } else {
                int delta = getDeltaScrollLength(currentTimeMillis, scrolledLength);
                delta *= WaveViewUtil.NUM_8;
                int newX = isReverseLayout() ? -delta : delta;
                CoroutineUtils.INSTANCE.doInMain(() -> {
                    smoothScrollBy(newX, 0, mQuinticInterpolator);
                    return null;
                });
            }
        }

        mPreTimeMillis = currentTimeMillis;
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        synchronized (this) {
            removeCallbacks(mSetSelectTimeRunnable);
            removeCallbacks(mOnLayoutChangeUiRunnable);
        }
    }

    protected int getDeltaScrollLength(long currentTimeMillis, int scrolledLength) {
        int expectedLength = (int) (currentTimeMillis * mPxPerMs);
        int delta = expectedLength - scrolledLength;
        long deltaTime = currentTimeMillis - mPreTimeMillis;
        if (scrolledLength < 0) {
            delta = 0;
        } else if ((scrolledLength == 0) && (mPreTimeMillis != 0)) {
            //分屏或者旋转等重建时波形显示位置为0
            delta = (int) (deltaTime * mPxPerMs);
        } else if (delta <= 0) {
            delta = (int) (deltaTime * mPxPerMs);
        }
        DebugUtil.d(TAG, "startSmoothScroll: currentTime = " + currentTimeMillis + " "
                + "scrolledLength = " + scrolledLength + ", expectedLength = " + expectedLength
                + ", dx = " + delta + ", deltaTime = " + deltaTime);
        return delta;
    }

    /**
     * 计算已滚动的距离
     */
    protected int getTotalScrolledLength() {
        try {
            int firstVisibleItemPosition = mLayoutManager.findFirstVisibleItemPosition();
            View firstVisibleItem = mLayoutManager.findViewByPosition(firstVisibleItemPosition);
            if (firstVisibleItem != null) {
                int left = isReverseLayout()
                        ? Math.abs(firstVisibleItem.getRight() - this.getWidth())
                        : firstVisibleItem.getLeft();
                int scrollTotalLength = 0;
                if (firstVisibleItemPosition == 0) {
                    scrollTotalLength = Math.abs(left);
                } else {
                    scrollTotalLength = Math.abs(left) + mFirstWaveViewWidth
                            + (firstVisibleItemPosition - 1) * mWaveViewWidth;
                }

                return scrollTotalLength;
            } else {
                DebugUtil.w(TAG, "getCurrentVisiblePos, firstVisibleItem is null, "
                        + "firstVisibleItemPosition = " + firstVisibleItemPosition);
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, " find first visible view error.", e);
        }

        return DEFAULT_SCROLL_LENGTH;
    }

    @Override
    public void smoothScrollBy(int dx, int dy) {
        smoothScrollBy(dx, dy, null);
    }

    @Override
    public void smoothScrollBy(int dx, int dy, @Nullable Interpolator interpolator) {
        smoothScrollBy(dx, dy, interpolator, DEFAULT_NUM_560);
    }

    @Override
    public void smoothScrollBy(int dx, int dy, @Nullable Interpolator interpolator, int duration) {
        super.smoothScrollBy(dx, dy, interpolator, duration);
    }

    @Override
    protected void onMeasure(int widthSpec, int heightSpec) {
        super.onMeasure(widthSpec, heightSpec);
        mItemHeight = getMeasuredHeight();
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        int width = right - left;
        super.onLayout(changed, left, top, right, bottom);
        if (!changed || (mScreenWidth == width) || (mWaveAdapter == null)) {
            return;
        }
        DebugUtil.i(TAG, "onLayout (" + left + "," + top + "-" + right + "," + bottom + ") width:" + width + " old:" + mScreenWidth);
        removeCallbacks(mOnLayoutChangeUiRunnable);
        updateRecycleViewWidth(width);
        // recycleView 绘制完后，再更新AdapterWaveItemView的宽度以及setSelectTime
        post(mOnLayoutChangeUiRunnable);
    }

    protected void updateRecycleViewWidth(int width) {
        if (mScreenWidth == width) {
            return;
        }
        mScreenWidth = width;
        /*向上取整，父子级或分屏下可能会少1px，导致手动滑动波形滚动到末尾后，计算出的slideTime小于duration，需要点击两次播放才能从0开播*/
        mFirstWaveViewWidth = (int) Math.ceil(mScreenWidth / (WaveViewUtil.NUM_TWO * NumberConstant.NUM_F1_0));
        mCenterPointDuration =
                (int) (((float) mFirstWaveViewWidth / mTimeLineGap) * WaveViewUtil.DURATION_INTERVAL);
        if (mWaveAdapter != null) {
            mWaveAdapter.updateWidth(mScreenWidth);
        }
        // 更新缓存池的最大个数
        setMaxRecycledViews(mScreenWidth, mWaveViewWidth, WaveAdapter.NORMAL_ITEM);
    }

    @Override
    public void draw(Canvas canvas) {
        super.draw(canvas);
        drawCenterLine(canvas);
    }

    @Override
    public void setBackground(Drawable background) {
        //屏蔽xml的背景色设置,如果在xml设置背景色，则会绘制到顶部的标记区域
    }

    public long getTotalTime() {
        return mTotalTime;
    }

    /**
     * draw the center line
     */
    private void drawCenterLine(Canvas canvas) {
        float centerLineStartX = getWidth() / WaveViewUtil.NUM_TWO_F;
        mCenterLinePaint.setStrokeWidth(mCenterLineStrokeWidth);
        float centerLineStartY = mNeedShowTimeLine ? mMarkViewHeight : 0;
        canvas.drawLine(centerLineStartX, centerLineStartY, centerLineStartX, mItemHeight,
                mCenterLinePaint);
    }

    public void setAmplitudeList(List<Integer> amplitudeList) {
        mAmplitudeValue = amplitudeList;
    }

    public List<Integer> getAmplitudes() {
        return mAmplitudeValue;
    }

    public void setDirectOn(boolean directOn) {
        this.mIsDirectOn = directOn;
    }

    public boolean isDirectOn() {
        return mIsDirectOn;
    }

    public void setDirectTime(String directTime) {
        this.mDirectTime = directTime;
    }

    public String getDirectTime() {
        return mDirectTime;
    }

    public void setLastDirectOnTime(long time) {
        this.mLastDirectOnTime = time;
    }

    public long getLastDirectOnTime() {
        return mLastDirectOnTime;
    }

    public void setDuration(long mDuration) {
        this.mDuration = mDuration;
    }

    public long getDuration() {
        return mDuration;
    }

    public interface DragListener {

        void onDragged();

    }

    /**
     * 父布局的点击事件
     *
     * @param event motion event
     */
    public void outsideEvent(MotionEvent event) {
        if (event.getAction() != MotionEvent.ACTION_UP) {
            return;
        }
        int firstVisibleItemPosition = mLayoutManager.findFirstVisibleItemPosition();
        int lastVisibleItemPosition = mLayoutManager.findLastVisibleItemPosition();
        for (int i = firstVisibleItemPosition; i <= lastVisibleItemPosition; i++) {
            View child = mLayoutManager.findViewByPosition(i);
            WaveItemView itemView = null;
            if (child != null) {
                itemView = (WaveItemView) child;
            }
            if (itemView == null) {
                continue;
            }
            List<MarkDataBean> markTimeList = itemView.getMarkTimeList();
            if (markTimeList.size() == 0) {
                continue;
            }
            int[] location = new int[2];
            itemView.getLocationOnScreen(location);
            RectF newRect = new RectF();
            for (MarkDataBean mark : markTimeList) {
                RectF rect = mark.getRect();
                newRect.set(location[0] + rect.left - MARK_EXPAND,
                        location[1] + rect.top - MARK_EXPAND,
                        location[0] + rect.right + MARK_EXPAND,
                        location[1] + rect.bottom + MARK_EXPAND);
                if (newRect.contains(event.getRawX(), event.getRawY())) {
                    itemView.setEventOnClick(mark);
                    return;
                }
            }
        }
    }

    public void setMarkTimeList(@Nullable List<MarkDataBean> markData) {
        mWaveAdapter.setMarkTimeList(markData);
    }

    public void notifyDataSetChanged() {
        if (mWaveAdapter != null) {
            mWaveAdapter.notifyDataSetChanged();
        }
    }

    public void addBookMark() {
        clearWaveHandlerMessage(MSG_FRESH_RECYCLER_VIEW);
        sendWaveHandlerMessage(MSG_FRESH_RECYCLER_VIEW, DURATION);
    }

    public void setAddMarkData(MarkDataBean data) {
        mAddMarkData = data;
    }

    public MarkDataBean getAddMarkData() {
        return mAddMarkData;
    }

    public void setRemoveMarkData(MarkDataBean data) {
        mRemoveMarkData = data;
    }

    public MarkDataBean getRemoveMarkData() {
        return mRemoveMarkData;
    }

    public void removeBookMark(int i) {
        if (mWaveAdapter != null) {
            mWaveAdapter.removeBookMark(i);
        }
    }

    /**
     * wave handler-------------------------------------------------------------------
     */
    protected void sendWaveHandlerMessage(int what) {
        sendWaveHandlerMessage(what, 0);
    }

    protected void sendWaveHandlerMessage(int what, long delay) {
        mHandler.sendEmptyMessageDelayed(what, delay);
    }

    protected void clearWaveHandlerMessage(int what) {
        mHandler.removeMessages(what);
    }

    protected void onWaveHandlerMessageArrived(Message msg) {
        //do nothing
    }

    protected void onWaveHandlerRefreshBookMark() {
        postInvalidateOnAnimation();
    }

    private static class WaveHandler extends Handler {

        private WeakReference<WaveRecyclerView> mReference;

        public WaveHandler(WaveRecyclerView recyclerView) {
            super(Looper.getMainLooper());
            mReference = new WeakReference<>(recyclerView);
        }

        @Override
        public void handleMessage(Message msg) {
            WaveRecyclerView recyclerView = mReference.get();
            if (msg.what == MSG_FRESH_RECYCLER_VIEW) {
                if (recyclerView != null) {
                    recyclerView.onWaveHandlerRefreshBookMark();
                }
            } else {
                if (recyclerView != null) {
                    recyclerView.onWaveHandlerMessageArrived(msg);
                }
            }
        }
    }
    //wave handler end -------------------------------------------------------------------
}
