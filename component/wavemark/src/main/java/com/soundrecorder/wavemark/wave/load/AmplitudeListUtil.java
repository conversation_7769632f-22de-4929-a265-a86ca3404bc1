/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AmplitudeListUtil
 Description:
 Version: 1.0
 Date: 2022/8/9
 Author: W9013333(v-zhengt<PERSON><EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/8/9 1.0 create
 */
package com.soundrecorder.wavemark.wave.load;

import android.content.Context;
import android.net.Uri;
import android.text.TextUtils;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.DebugUtil;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import com.soundrecorder.base.utils.FileUtils;
import com.soundrecorder.base.utils.NumberUtil;
import com.soundrecorder.common.constant.Constants;
import com.soundrecorder.common.constant.RecordConstant;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.db.MediaDBUtils;
import com.soundrecorder.common.db.PictureMarkDbUtils;
import com.soundrecorder.common.db.RecorderDBUtil;
import com.soundrecorder.common.databean.markdata.MarkDataBean;
import com.soundrecorder.common.utils.MarkProcessUtil;
import com.soundrecorder.common.utils.MarkSerializUtil;
import com.soundrecorder.wavemark.wave.WaveViewUtil;
import com.soundrecorder.wavemark.wave.id3tool.Mp3File;

import org.jetbrains.annotations.NotNull;

import kotlin.collections.CollectionsKt;

public class AmplitudeListUtil {
    private static final String TAG = "AmplitudeListUtil";
    private static final String SYMBOL_COMMA = ",";
    private final String mPlayPath;
    private final Uri mPlayUri;
    private final Context mContext;
    private SoundFile mSoundFile = null;
    private String mMarkStringFromMp3 = null;
    private String mAmpStringFromMp3 = null;
    private DecodeFinish mDecodeFinish = null;
    private DecodeReady mDecodeReady = null;
    private Mp3File mMp3File = null;
    private SoundFile.DecodedReadyListener mSoundDecodeReady = null;
    private SoundFile.DecodedSoundFileListener mSoundDecodeFinish = null;
    private SoundFile.ProgressCallback mProgressCallback = null;
    private boolean mIsDecodeMp3 = false;

    private boolean mIsRecycle = false;

    public AmplitudeListUtil(Context context, String path, Uri playUri, boolean isRecycle) {
        mContext = context;
        mPlayPath = path;
        mPlayUri = playUri;
        mIsRecycle = isRecycle;
    }

    private String getAmpFromDB() {
        return RecorderDBUtil.getInstance(mContext).getAmpStringByPath(mPlayPath, mIsRecycle);
    }

    private String getOldMarkStringFromDB() {
        return RecorderDBUtil.getInstance(mContext).getMarkStringByPath(mPlayPath);
    }

    private void decodeMp3() {
        if ((mPlayPath == null) || (mPlayUri == null)) {
            DebugUtil.d(TAG, "mPlayName " + FileUtils.getDisplayNameByPath(mPlayPath) + " mPlayUri " + mPlayUri);
            return;
        }

        // 通过mimeType判断是否为mp3，拦截 非mp3文件
        String mimeType = mContext.getContentResolver().getType(mPlayUri);
        if (!RecordConstant.MIMETYPE_MP3.equals(mimeType)) {
            DebugUtil.d(TAG, "mime type is not mp3: " + mimeType);
            return;
        }
        mIsDecodeMp3 = true;

        try {
            mMp3File = new Mp3File();
            mMp3File.create(mPlayUri, mContext);
            mMp3File.extractMp3FileWithDefault(true);
            byte[] tag = mMp3File.getCustomTag();
            if (tag == null) {
                DebugUtil.i(TAG, "mMp3File.getCustomTag is null.");
                return;
            }
            String markTagString = new String(tag, Constants.UTF_8);
            int index = markTagString.indexOf(MarkSerializUtil.SPLIT_BETWEEN_MARK_AND_AMPLITUDE);
            if (index > 0) {
                mAmpStringFromMp3 = markTagString.substring(index + 1);
                mMarkStringFromMp3 = markTagString.substring(0, index);
            } else {
                mMarkStringFromMp3 = markTagString;
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "extract mp3 file error", e);
            mAmpStringFromMp3 = null;
            mMarkStringFromMp3 = null;
        } finally {
            if (mMp3File != null) {
                mMp3File.release();
            }
        }
    }

    public void getAmpFromSoundFile() {
        try {
            mSoundFile = new SoundFile();
            mSoundFile.setDecodedSoundFileListener(mSoundDecodeFinish);
            mSoundFile.setWaitAmplitudes((int) WaveViewUtil.getOneLargeWaveItemAmplitueLineCount(mContext), mSoundDecodeReady);
            mSoundFile.setOneWaveLineTime(WaveViewUtil.getOneLargeWaveLineTime(mContext));
            if (BaseUtil.isAndroidQOrLater() && !mIsRecycle) {
                mSoundFile.create(FileUtils.getDisplayNameByPath(mPlayPath), mPlayUri, mContext);
            } else {
                mSoundFile.create(mPlayPath, null, mContext);
            }

        } catch (Exception e) {
            DebugUtil.e(TAG, "getAmpFromSoundFile error ", e);
        }
    }

    private String soundList2String(List<Integer> list) {
        StringBuilder ampString = new StringBuilder();
        if ((list != null) && !list.isEmpty()) {
            for (int i = 0; i < list.size(); i++) {
                ampString.append(list.get(i));
                if (i != list.size()) {
                    ampString.append(",");
                }
            }
        }
        return ampString.toString();
    }

    public List<Integer> getAmpList() {
        String ampString = getAmpString();
        DebugUtil.d(TAG, "getAmpList, ampString:" + ampString);
        return convertStringToInt(ampString);
    }

    public List<Integer> convertStringToInt(String str) {
        if (!TextUtils.isEmpty(str)) {
            String[] amps = str.split(",");
            List<Integer> ampList = new ArrayList<>(amps.length);
            for (String amp : amps) {
                ampList.add(NumberUtil.parseStr2Int(amp, 0));
            }
            return ampList;
        }
        return new ArrayList<>();
    }

    public String getAmpString() {
        String ampString = getAmpFromDB();
        if ((ampString != null) && !TextUtils.isEmpty(ampString)) {
            DebugUtil.d(TAG, "getAmpString getAmpFromDB");
            return ampString;
        }

        if (!mIsDecodeMp3) {
            decodeMp3();
        }
        ampString = mAmpStringFromMp3;
        if ((ampString != null) && !ampString.isEmpty()) {
            DebugUtil.d(TAG, "getAmpString getAmpFromMP3");
            return ampString;
        }

        return ampString;
    }

    public String getMarkString() {
        String markString = getOldMarkStringFromDB();
        if ((markString != null) && !TextUtils.isEmpty(markString)) {
            DebugUtil.d(TAG, "getMarkString getMarkFromDB");
            return markString;
        }

        if (!mIsDecodeMp3) {
            decodeMp3();
        }
        markString = mMarkStringFromMp3;
        if ((markString != null) && !TextUtils.isEmpty(markString)) {
            DebugUtil.d(TAG, "getMarkString getMarkFromMp3");
            return markString;
        }
        DebugUtil.d(TAG, "getMark is null ");
        return markString;
    }

    public List<MarkDataBean> getMergeMarkList() {
        List<MarkDataBean> oldMarkList = new ArrayList<>();
        String oldMarkString = getMarkString();
        if (oldMarkString != null) {
            oldMarkList = MarkSerializUtil.INSTANCE.parseMarkDataBeanListFromString(oldMarkString);
        }
        List<MarkDataBean> newMarkList = getNewMarkList();
        return MarkProcessUtil.mergeOldAndNewMarkList(oldMarkList, newMarkList);
    }

    private List<MarkDataBean> getNewMarkList() {
        List<MarkDataBean> result = new ArrayList<>();
        String keyId = RecorderDBUtil.getKeyIdByPath(mPlayPath);
        if (!TextUtils.isEmpty(keyId)) {
            result = PictureMarkDbUtils.INSTANCE.queryPictureMarks(keyId);
        }
        return result;
    }

    public List<Integer> string2Integer(List<String> list) {
        if ((list == null) || list.isEmpty()) {
            return new ArrayList<>();
        }
        List<Integer> stringList = new ArrayList<>(list.size());
        try {
            for (int i = 0; i < list.size(); i++) {
                stringList.add(Integer.valueOf(list.get(i)));
            }
        } catch (NumberFormatException e) {
            DebugUtil.d(TAG, e.toString());
        }
        return stringList;
    }

    public interface DecodeFinish {
        void decodeFinish(String ampString);
    }

    public void setDecodeFinish(DecodeFinish decodeFinish) {
        mDecodeFinish = decodeFinish;
        mSoundDecodeFinish = isFinish -> {
            DebugUtil.i(TAG, "decodedSoundFileFinish Callback " + isFinish);
            if ((mDecodeFinish != null) && (mSoundFile != null) && isFinish) {
                mDecodeFinish.decodeFinish(soundList2String(mSoundFile.getAmplitudeList()));
            }
            return false;
        };
        if (mSoundFile != null) {
            mSoundFile.setDecodedSoundFileListener(mSoundDecodeFinish);
        }
    }

    public interface DecodeReady {
        void onReady(String string, SoundFile soundFile);
    }

    public void setDecodeReady(DecodeReady decodeReady) {
        mDecodeReady = decodeReady;
        mSoundDecodeReady = () -> {
            DebugUtil.i(TAG, "onReady Callback");
            if ((mDecodeReady != null) && (mSoundFile != null)) {
                mDecodeReady.onReady(soundList2String(mSoundFile.getAmplitudeList()), mSoundFile);
            }
        };
        if (mSoundFile != null) {
            mSoundFile.setWaitAmplitudes((int) WaveViewUtil.getOneLargeWaveItemAmplitueLineCount(mContext), mSoundDecodeReady);
        }
    }

    public void release() {
        if (mSoundDecodeReady != null) {
            mSoundDecodeReady = null;
        }
        if (mSoundDecodeFinish != null) {
            mSoundDecodeFinish = null;
        }
        if (mProgressCallback != null) {
            mProgressCallback = null;
        }

        releaseSound();
        releaseMp3();
    }

    public void releaseSound() {
        if (mSoundFile != null) {
            mSoundFile.setStopDecoded(true);
            mSoundFile = null;
        }
    }

    public void releaseMp3() {
        if (mMp3File != null) {
            mMp3File.setCancleScanFlag(true);
            mIsDecodeMp3 = false;
            mMp3File = null;
        }
    }

    public static String getAmplitudeString(List<Integer> amplitudeList) {
        if (amplitudeList == null || amplitudeList.isEmpty()) {
            return Constants.EMPTY;
        }
        int size = amplitudeList.size();
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < size; i++) {
            Integer v = CollectionsKt.getOrNull(amplitudeList, i);
            if (v == null) {
                v = 0;
            }
            if (i == size - 1) {
                builder.append(v);
            } else {
                builder.append(v).append(SYMBOL_COMMA);
            }
        }
        return builder.toString();
    }

    public static void writeAmpData(Context context, String inputName, Uri uri, List<Integer> amplitudeList) {
        if (TextUtils.isEmpty(inputName)) {
            DebugUtil.i(TAG, "input name is empty, return");
            return;
        }
        if (FileUtils.checkFileNotExistOrFileSizeZero(inputName, uri)) return;
        String ampString = contactAmpListToString(amplitudeList);
        if (TextUtils.isEmpty(ampString)) {
            DebugUtil.i(TAG, "markString is empty , return");
            return;
        }
        Record record = RecorderDBUtil.getInstance(context).qureyRecordByPath(inputName);
        if (record != null) {
            RecorderDBUtil.getInstance(context).updateRecordAmplitudeById(record.getId(), ampString);
        } else {
            DebugUtil.e(TAG, "writeAmpData no record found in recordDb, update error");
        }
    }

    @NotNull
    private static String contactAmpListToString(List<Integer> amplitudeList) {
        StringBuilder ampStringBuilder = new StringBuilder();
        if ((amplitudeList != null) && (amplitudeList.size() > 0)) {
            Integer tmpAmplitude = null;
            for (int i = 0; i < amplitudeList.size(); i++) {
                tmpAmplitude = amplitudeList.get(i);

                if (tmpAmplitude != null) {
                    String amplitude = String.valueOf(tmpAmplitude);
                    ampStringBuilder.append(amplitude);
                }
                if ((i + 1) < amplitudeList.size()) {
                    ampStringBuilder.append(",");
                }
            }
        }
        return ampStringBuilder.toString();
    }

    public static String decodeAmplitudeByUri(Uri uri) {
        Record recordFormMedia = MediaDBUtils.getRecordFromMediaByUriId(uri);
        if (recordFormMedia == null) {
            return "";
        }

        SoundFile soundFile = new SoundFile();
        soundFile.setOneWaveLineTime(Constants.WAVE_SAMPLE_INTERVAL_TIME);
        try {
            soundFile.create(recordFormMedia.getDisplayName(), uri, BaseApplication.getAppContext());
            return contactAmpListToString(soundFile.getAmplitudeList());
        } catch (IOException e) {
            DebugUtil.w(TAG, "decodeAmplitudeByUri error: " + e);
            return "";
        }
    }
}
