/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  DrawPlayAmpExt.kt
 * * Description : 定向录音播放绘制波形扩展
 * * Version     : 1.0
 * * Date        : 2024/8/20
 * * Author      : W9035969
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.wavemark.wave.view

import android.graphics.Canvas
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.DirectRecordTime
import com.soundrecorder.wavemark.wave.WaveViewUtil
import kotlin.math.ceil

private const val TAG = "DrawPlayAmpExt"

/**
 * 绘制包含定向录音分段波形
 */
fun WaveItemView.drawPlayAmplitudeForCurrentX(canvas: Canvas, currentX: Float, lineHeight: Float): Float {
    /*val waveStartY = getStartYByHeight(lineHeight)
    val waveEndY = getEndYByHeight(lineHeight)*/
    //boolean isDrawDirectAmp = false;
    val waveItemTimePair = getWaveItemTimePair()
    /*[1055-9602,11219-14095,16204-19222,20766-22580]*/
    val directRecordTimeList = directTimeList
    if (isEnhance && directRecordTimeList.isNotEmpty()) {
        directRecordTimeList.forEach { directRecordTime ->
            return drawDirectPlayAmp(
                canvas,
                currentX,
                directRecordTime,
                waveItemTimePair,
                lineHeight
            )
        }
    } else {
        return drawNormalPlayAmp(canvas, currentX, lineHeight)
    }
    return currentX
}

private fun WaveItemView.getWaveItemTimePair(): Pair<Long, Long> {
    /*0-6000,6000-12000,12000-18000,18000-24000*/
    val waveStartTime = (mViewIndex - 1) * WaveViewUtil.ONE_WAVE_VIEW_DURATION //ms
    val waveEndTime = mViewIndex * WaveViewUtil.ONE_WAVE_VIEW_DURATION //ms
    DebugUtil.d(TAG, "getWaveItemTimePair, waveStartTime:$waveStartTime waveEndTime:$waveEndTime")
    return Pair(waveStartTime, waveEndTime)
}

private fun WaveItemView.drawDirectPlayAmp(
    canvas: Canvas,
    currentX: Float,
    directRecordTime: DirectRecordTime,
    waveItemTimePair: Pair<Long, Long>,
    lineHeight: Float
): Float {
    val waveStartY = getStartYByHeight(lineHeight)
    val waveEndY = getEndYByHeight(lineHeight)

    val waveStartTime = waveItemTimePair.first
    val waveEndTime = waveItemTimePair.second

    val startTime = directRecordTime.startTime
    val endTime = directRecordTime.endTime
    val directStartTime = if (startTime >= waveStartTime) {
        startTime
    } else { // startTime < waveStartTime
        waveStartTime
    }
    val directEndTime = if (endTime <= waveEndTime) {
        endTime
    } else {
        waveEndTime
    }
    val isRtl = isReverseLayout
    var whileCurrentX = currentX
    var newCurrentX = currentX
    val newCurrentDirectEndX = currentX + mDirectAmpWidth
    DebugUtil.d(TAG, "drawDirectPlayAmp,newCurrentX:$newCurrentX,newCurrentXLatter:$newCurrentDirectEndX")
    if (directStartTime in 0..directEndTime && directEndTime > 0) {
        val startX = if (directStartTime <= waveStartTime) getXByTime(waveStartTime) else getXByTime(directStartTime)
        val endX = if ((mViewIndex == mTotalCount - 1) || directEndTime <= waveEndTime) {
            getXByTime(directEndTime)
        } else {
            getXByTime(waveEndTime)
        }
        DebugUtil.d(TAG, "drawDirectPlayAmp, startX:$startX endX:$endX")
        val ampPaintWith = getDirectAmpPaintWith(isRtl, newCurrentX, startX, newCurrentDirectEndX, endX)
        var newCurrentXLatter = currentX + ampPaintWith
        if (isRtl) {
            newCurrentX = width - currentX
            newCurrentXLatter = newCurrentX - ampPaintWith
        }
        newCurrentXLatter = ceil(newCurrentXLatter.toDouble()).toFloat()
        newCurrentX = ceil(newCurrentX.toDouble()).toFloat()

        val pair = addCurrentXPair(isRtl, newCurrentXLatter, whileCurrentX)
        val addCurrentX = pair.first
        whileCurrentX = pair.second
        //步骤三：开始绘制矩形
        canvas.drawRoundRect(newCurrentX, waveStartY.toFloat(), newCurrentXLatter,
            waveEndY.toFloat(), ampPaintWith, ampPaintWith, mAmplitudePaint)
        //步骤四：一个波形绘制完成，则移动当前位置
        if (addCurrentX) {
            whileCurrentX += mVirtualAmpGap
        }
        return whileCurrentX
    } else {
        return drawNormalPlayAmp(canvas, currentX, lineHeight)
    }
}

private fun WaveItemView.getDirectAmpPaintWith(
    isRtl: Boolean,
    newCurrentX: Float,
    startX: Float,
    newCurrentDirectEndX: Float,
    endX: Float
): Float {
    val ampPaintWith = if (isRtl) {
        if (newCurrentX < startX && newCurrentDirectEndX > endX) {
            mDirectAmpWidth
        } else {
            mAmplitudeWidth
        }
    } else {
        if (newCurrentX >= startX && newCurrentDirectEndX <= endX) {
            mDirectAmpWidth
        } else {
            mAmplitudeWidth
        }
    }
    return ampPaintWith
}

private fun WaveItemView.addCurrentXPair(
    isRtl: Boolean,
    newCurrentXLatter: Float,
    whileCurrentX: Float
): Pair<Boolean, Float> {
    var whileCurrentX1 = whileCurrentX
    var addCurrentX = true
    if ((mViewIndex == mTotalCount - 1)) {
        if (isRtl) {
            if ((newCurrentXLatter < mCenterLineX)) {
                if (mEndItemWidth > 0) {
                    //大于0表示不是初始化的时候调用此方法，是waveRecyclerView真正滑动到最后一个item
                    whileCurrentX1 += mVirtualAmpGap
                }
                addCurrentX = false
            }
        } else {
            if ((newCurrentXLatter > mEndItemWidth)) {
                if (mEndItemWidth > 0) {
                    //大于0表示不是初始化的时候调用此方法，是waveRecyclerView真正滑动到最后一个item
                    whileCurrentX1 += mVirtualAmpGap
                }
                addCurrentX = false
            }
        }
    }
    return Pair(addCurrentX, whileCurrentX1)
}

/**
 * 绘制普通波形
 */
fun WaveItemView.drawNormalPlayAmp(
    canvas: Canvas,
    currentX: Float,
    lineHeight: Float,
): Float {
    val waveStartY = getStartYByHeight(lineHeight)
    val waveEndY = getEndYByHeight(lineHeight)

    var whileCurrentX = currentX
    val ampPaintWith = mAmplitudeWidth
    val isRtl = isReverseLayout
    var newCurrentX = currentX
    var newCurrentXLatter: Float = currentX + ampPaintWith
    if (isRtl) {
        newCurrentX = width - currentX
        newCurrentXLatter = newCurrentX - ampPaintWith
    }
    newCurrentXLatter = ceil(newCurrentXLatter.toDouble()).toFloat()
    newCurrentX = ceil(newCurrentX.toDouble()).toFloat()

    val pair = addCurrentXPair(isRtl, newCurrentXLatter, whileCurrentX)
    val addCurrentX = pair.first
    whileCurrentX = pair.second

    //步骤三：开始绘制矩形
    canvas.drawRoundRect(newCurrentX, waveStartY.toFloat(), newCurrentXLatter, waveEndY.toFloat(),
        mAmplitudeWidth, mAmplitudeWidth, mAmplitudePaint)
    //步骤四：一个波形绘制完成，则移动当前位置
    if (addCurrentX) {
        //缓存波形音柱开始绘制的位置
        dmpStartXList.add(newCurrentX)
        whileCurrentX += mVirtualAmpGap
    }
    return whileCurrentX
}
