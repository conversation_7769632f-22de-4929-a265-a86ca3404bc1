package com.soundrecorder.wavemark.mark

import android.net.Uri
import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.base.PlayerHelperCallback
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.databean.MarkMetaData
import com.soundrecorder.common.db.PictureMarkDbUtils
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.common.utils.MarkSerializUtil
import com.soundrecorder.wavemark.R
import java.util.concurrent.atomic.AtomicBoolean
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.math.abs

class MarkHelper(var playerHelperCallback: PlayerHelperCallback?, var viewModelScope: CoroutineScope, var isRecordMode: Boolean) {

    var isAddPictureMark = false
    private val TAG: String = "MarkHelper"
    private var uri: Uri? = null
    private var pathString: String? = null

    private var markDatas: MutableLiveData<MutableList<MarkDataBean>> = MutableLiveData(mutableListOf())
    private var lastMarkIndex: Int = -1
    private val markerCounter: MarkerCounter = MarkerCounter.newInstance()

    private var markLoaded = AtomicBoolean(false)

    companion object {
        const val MAX_MARKER_COUNT = 50
        const val ADD_RESULT_PLAY_COMPLETE = -4
        const val ADD_RESULT_DUPLICATE_TIME = -3
        const val ADD_RESULT_LIMIT_NUMBER_EXCEED = -2
        const val ADD_RESULT_OTHER_ERRORS = -1
    }

    init {
        markerCounter.reset()
    }

    //标记查询成功后，重置MarkHelper的计数器的开始的起始数据
    fun startIndexWhenReadMarksComplete(data: List<MarkDataBean>?) {
        var maxStartIndex = 0
        data?.forEach {
            if (it.defaultNo > maxStartIndex) {
                maxStartIndex = it.defaultNo
            }
        }
        if (maxStartIndex < 0) {
            maxStartIndex = 0
        }
        DebugUtil.d(TAG, "markerCounter.startIndex = $maxStartIndex")
        markerCounter.startIndex = maxStartIndex
    }


    fun refreshData(markDataBeans: MutableList<MarkDataBean>?, uri: Uri?, pathString: String?) {
        markDataBeans?.let {
            if (playerHelperCallback != null) {
                correctMarkData(it)
            }
            markDatas.postValueSafe(it)
        }
        uri?.let {
            this.uri = uri
        }
        pathString?.let {
            this.pathString = pathString
        }
        DebugUtil.i(TAG, "refresh data invoked, markLoaded set true")
        markLoaded.set(true)
    }

    /**
     * 播放文件路径变更后，需要更新，场景：播放页录音重命名
     */
    fun refreshPlayPath(path: String?) {
        pathString = path
    }

    fun isMarkLoaded(): Boolean {
        return markLoaded.get()
    }

    private fun correctMarkData(markedData: MutableList<MarkDataBean>) {
        val audioDuration = playerHelperCallback?.getDuration() ?: 0
        for (index in markedData.size - 1 downTo 0) {
            val newItem = markedData[index]
            if (newItem.timeInMills > audioDuration) {
                //just set the newItem's time as the duration
                newItem.correctTime(audioDuration)
            } else {
                //no need to correct
                break
            }
        }
    }

    fun getMarkDatas(): MutableLiveData<MutableList<MarkDataBean>> {
        return markDatas
    }

    /**
     * 判断标记按钮、图片标记按钮是否是enable状态
     * @return true表示按钮enable，false表示unable
     */
    fun isMarkEnabled(): Boolean {
        return isMarkEnabled(-1)
    }

    fun isMarkEnabled(curTime: Long = -1): Boolean {
        if (isAddPictureMark) {
            return false
        }
        if ((markDatas.value != null) && (markDatas.value!!.size >= MAX_MARKER_COUNT)) {
            return false
        }
        val isPaused = playerHelperCallback?.hasPaused() == true
        val curTimeMillis = if (curTime < 0) playerHelperCallback?.getCurrentPlayerTime() ?: 0 else curTime
        if (isPaused && (MarkSerializUtil.checkInTimeScopeInMarkList(markDatas.value, curTimeMillis) != -1)) {
            return false
        }
        if (!isRecordMode) {
            val duration = playerHelperCallback?.getDuration() ?: 0
            if (curTimeMillis >= duration) {
                return false
            }
        }
        if ((markDatas.value != null && markDatas.value?.isNotEmpty() == true)) {
            val duration = playerHelperCallback?.getDuration() ?: 0
            val lastMarkDataBean = markDatas.value?.last()
            lastMarkDataBean?.let {
                //判断最后一个标记位置是否处于音频的最后一秒时间段内
                val bInLastOneSecondDuration = duration > 0 && duration - it.timeInMills < Constants.SECOND
                //判断当前时间是否在最后一个标记位置的限制时间间隔内
                val bInLimitedTimeScope = abs(curTimeMillis - it.timeInMills) < Constants.SECOND
                if (bInLastOneSecondDuration && bInLimitedTimeScope) {
                    return false
                }
            }
        }
        return true
    }

    fun getLastMark(): MarkDataBean? {
        DebugUtil.i("NotificationHelper", "get Last Mark:$lastMarkIndex")
        return if (lastMarkIndex != -1) {
            getMark(lastMarkIndex)
        } else {
            null
        }
    }

    fun getMark(index: Int): MarkDataBean? {
        if ((markDatas.value != null) && (index <= MAX_MARKER_COUNT) && (index < markDatas.value!!.size)) {
            return markDatas.value?.get(index)
        }
        return null
    }

    fun getMarkCorrectTime(index: Int): Long {
        return getMark(index)?.correctTime ?: 0
    }

    fun addMark(isFromNotification: Boolean, pictureMarkMetaData: MarkMetaData): Int {
        DebugUtil.i(TAG, "add mark begin")
        if (playerHelperCallback == null) {
            DebugUtil.i(TAG, "playerHelperCallback is null")
            return ADD_RESULT_OTHER_ERRORS
        }
        var currentTimeMillis = pictureMarkMetaData.currentTimeMillis
        var imagePath = pictureMarkMetaData.imagePath
        if (currentTimeMillis < 0) {
            currentTimeMillis = playerHelperCallback?.getCurrentPlayerTime() ?: 0
        }
        if (markDatas.value != null) {
            val size = markDatas.value!!.size
            if (size >= MAX_MARKER_COUNT) {
                ToastManager.showShortToast(
                    BaseApplication.getAppContext(),
                    BaseApplication.getAppContext().getString(R.string.photo_mark_recommend_mark_limit)
                )
                return ADD_RESULT_LIMIT_NUMBER_EXCEED
            }
            val duplicatedIndex = MarkSerializUtil.checkInTimeScopeInMarkList(markDatas.value, currentTimeMillis)
            if (duplicatedIndex != -1) {
                if (isFromNotification) {
                    lastMarkIndex = duplicatedIndex
                }
                DebugUtil.i("NotificationHelper", "Duplicate timing:$duplicatedIndex")
                return ADD_RESULT_DUPLICATE_TIME
            }
        }
        if (!isRecordMode) {
            val isProgressCompleted = (currentTimeMillis == (playerHelperCallback?.getDuration()
                ?: 0))
            if (isProgressCompleted) {
                DebugUtil.w(TAG, "playcomplete, click mark return")
                return ADD_RESULT_PLAY_COMPLETE
            }
        }
        var newMarkData: MarkDataBean? = null
        var index = ADD_RESULT_OTHER_ERRORS
        if (markDatas.value != null) {
            newMarkData = MarkDataBean(currentTimeMillis, MarkSerializUtil.VERSION_NEW)
            if (imagePath.isNotEmpty()) {
                newMarkData.version = MarkSerializUtil.VERSION_PICTURE
            }
            newMarkData.isDefault = true
            newMarkData.defaultNo = markerCounter.next
            newMarkData.pictureFilePath = imagePath
            newMarkData.pictureWith = pictureMarkMetaData.width
            newMarkData.pictureHeight = pictureMarkMetaData.height
            newMarkData.markSource = pictureMarkMetaData.from
            //标记新增时的时间
            newMarkData.addTimeMills = System.currentTimeMillis()
            markDatas.value!!.add(newMarkData)
            markDatas.value!!.sort()
            index = markDatas.value!!.lastIndexOf(newMarkData)
            val keyId = playerHelperCallback?.getKeyId() ?: ""
            addPictureMark(keyId, newMarkData)
            lastMarkIndex = index
            DebugUtil.i(TAG, "ADD MARK SUCCESS MARKBeanList: $markDatas")
        } else {
            DebugUtil.i(TAG, "markData value is null")
        }
        return index
    }

    fun addMultiPictureMark(files: ArrayList<MarkMetaData>): ArrayList<Int> {
        val successIndex = ArrayList<Int>()
        if (playerHelperCallback == null || markDatas.value == null) {
            DebugUtil.i(TAG, "playerHelperCallback is null")
            return successIndex
        }
        val keyId: String = playerHelperCallback?.getKeyId() ?: ""
        val targetList = ArrayList<MarkDataBean>()
        for (file in files) {
            val newMarkData = MarkDataBean(file.currentTimeMillis, MarkSerializUtil.VERSION_PICTURE).apply {
                isDefault = true
                defaultNo = markerCounter.next
                pictureFilePath = file.imagePath
                pictureWith = file.width
                pictureHeight = file.height
            }
            markDatas.value?.add(newMarkData)
            targetList.add(newMarkData)
        }

        markDatas.value?.sort()
        targetList.forEach {
            try {
                successIndex.add(markDatas.value!!.indexOf(it))
            } catch (e: Exception) {
                DebugUtil.e(TAG, "addMultiPictureMark Error! ${e.message}")
            }
        }
        addPictureMarks(keyId, targetList)
        return successIndex
    }

    fun removeMark(position: Int): MarkDataBean? {
        val removeItem = markDatas.value?.getOrNull(position)
        if (removeItem != null) {
            markDatas.value?.remove(removeItem)
            val mediaId = playerHelperCallback?.getKeyId() ?: ""
            deletePictureMark(mediaId, removeItem)
        }
        return removeItem
    }


    fun renameMark(newMarkText: String, pos: Int): Boolean {
        val markDataBean = markDatas.value?.getOrNull(pos)
        if (markDataBean == null) {
            DebugUtil.e(TAG, "onRenameMark error, old positon: $pos mark not exist")
            return false
        }
        markDataBean.version = MarkSerializUtil.VERSION_NEW
        if (markDataBean.pictureFilePath.isNotEmpty()) {
            markDataBean.version = MarkSerializUtil.VERSION_PICTURE
        }
        markDataBean.isDefault = false
        markDataBean.markText = newMarkText
        markDataBean.isUpdate = true
        markDatas.value!![pos] = markDataBean
        val mediaId = playerHelperCallback?.getKeyId() ?: ""
        updatePictureMark(mediaId, markDataBean)
        return true
    }

    private fun updateTextMarkInRecordDb(markDataBean: MarkDataBean) {
        if (markDataBean.isTextType()) {
            if ((pathString != null) && (uri != null) && (markDatas.value != null)) {
                RecorderDBUtil.writeMarkData(pathString, uri, markDatas.value!!)
            }
        }
    }

    fun clear() {
        markDatas.value?.let {
            it.clear()
        }
        uri = null
        pathString = null
    }

    private fun addPictureMark(keyId: String, data: MarkDataBean) {
        if (TextUtils.isEmpty(keyId)) return
        viewModelScope.launch(Dispatchers.IO) {
            if (!TextUtils.isEmpty(keyId)) {
                val count = PictureMarkDbUtils.queryPictureMarksByTimeInMills(keyId, data.timeInMills)
                if (count <= 0) {
                    PictureMarkDbUtils.addPictureMark(keyId, data)
                    updateTextMarkInRecordDb(data)
                }
            }
        }
    }

    private fun addPictureMarks(keyId: String, data: List<MarkDataBean>) {
        if (TextUtils.isEmpty(keyId)) return
        viewModelScope.launch(Dispatchers.IO) {
            PictureMarkDbUtils.addPictureMarks(keyId, data)
        }
    }

    private fun updatePictureMark(keyId: String, data: MarkDataBean) {
        viewModelScope.launch(Dispatchers.IO) {
            if (!TextUtils.isEmpty(keyId)) {
                val count = PictureMarkDbUtils.queryPictureMarksByTimeInMills(keyId, data.timeInMills)
                if (count > 0) {
                    PictureMarkDbUtils.updatePictureMark(keyId, data)
                    updateTextMarkInRecordDb(data)
                }
            }
        }
    }

    private fun deletePictureMark(keyId: String, data: MarkDataBean) {
        data.release()
        viewModelScope.launch(Dispatchers.IO) {
            if (!TextUtils.isEmpty(keyId)) {
                PictureMarkDbUtils.deletePictureMarkWithMarkPictureFile(keyId, data.timeInMills)
                updateTextMarkInRecordDb(data)
            }
        }
    }
}