/******************************************************************
* Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd
* VENDOR_EDIT
* File: - WaveViewUtil.java
* Description:
* Version: 1.0
* Date : 2018-11-14
* Author: <EMAIL>
**
* ---------------- Revision History: ---------------------------
*      <author>        <data>        <version >        <desc>
*    PengFei.Ma      2018-11-14          1.0         build this module
********************************************************************/
package com.soundrecorder.wavemark.wave;

import android.content.Context;

import java.util.Locale;

import com.soundrecorder.wavemark.R;
import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.common.constant.Constants;
import com.soundrecorder.base.utils.DebugUtil;

public class WaveViewUtil {

    private static final String TAG = "WaveViewUtil";

    public static final long DURATION_INTERVAL = 500L;
    /**
     * Time scale count for each WaveView
     */
    public static final int TIME_SCALE_COUNT = 12;
    /**
     * Duration of each WaveView(ms)
     */
    public static final long ONE_WAVE_VIEW_DURATION = DURATION_INTERVAL * TIME_SCALE_COUNT;
    public static final int ONE_WAVE_VIEW_DURATION_SECOND = 6;
    public static final int DEFAUL_CENTER_TIME = 5000; //5s
    public static final float AMPLITUDE_SCALE = 1F;
    public static final int MAX_AMPLITUDE = 32768;
    public static final int NUM_TWO = 2;
    public static final int NUM_3 = 3;
    public static final int NUM_4 = 4;
    public static final int NUM_8 = 8;
    public static final int NUM_TEN = 10;
    public static final int NUM_255 = 255;
    public static final float PERCENT_0 = 0F;
    public static final float PERCENT_8 = 0.08F;
    public static final float PERCENT_10 = 0.1F;
    public static final float PERCENT_15 = 0.15F;
    public static final float PERCENT_20 = 0.2F;
    public static final float PERCENT_30 = 0.3F;
    public static final float PERCENT_70 = 0.7F;
    public static final float PERCENT_90 = 0.9F;
    public static final float PERCENT_100 = 1F;
    public static final float PERCENT_110 = 1.1F;
    public static final float FLOAT_0 = 0.0F;
    public static final float FLOAT_1 = 1.0F;
    public static final float NUM_TWO_F = 2f;

    private static final int MINUTES_60 = 60;
    private static final int HOUR_10 = 10;
    private static final int SIX_MINUTES_3600 = 3600;

    private static int sTimeLineGap = 0;
    private static float sAmplitueLineGap = 0;
    private static float sOneWaveItemWidth = 0;
    private static float sOneWaveItemAmplitueLineCount = 0;
    private static float sOneWaveLineTime = 0;


    public static float getTimeLineGap(Context context) {
        if (sTimeLineGap == 0) {
            sTimeLineGap = context.getResources().getDimensionPixelSize(R.dimen.wave_time_line_gap);
            DebugUtil.i(TAG, "getTimeLineGap: " + sTimeLineGap);
        }
        return sTimeLineGap;
    }

    public static float getAmplitueLineGap(Context context) {
        if (sAmplitueLineGap == 0) {
            float px500s = getTimeLineGap(context);
            float ampGap = px500s * Constants.WAVE_SAMPLE_INTERVAL_TIME / DURATION_INTERVAL;
            DebugUtil.i(TAG, "getAmpGap: ampGap " + ampGap + ", getTimeLineGap: " + px500s);
            sAmplitueLineGap = ampGap;
        }
        return sAmplitueLineGap;
    }

    public static float getOneWaveItemWidth(Context context) {
        if (sOneWaveItemWidth == 0) {
            sOneWaveItemWidth = getTimeLineGap(context) * WaveViewUtil.TIME_SCALE_COUNT;
        }

        return sOneWaveItemWidth;
    }

    public static float getOneWaveItemAmplitueLineCount(Context context) {
        if (sOneWaveItemAmplitueLineCount == 0) {
            sOneWaveItemAmplitueLineCount = getOneWaveItemWidth(context) / getAmplitueLineGap(context);
        }

        return sOneWaveItemAmplitueLineCount;
    }

    public static float getOneWaveLineTime(Context context) {
        if (Float.compare(sOneWaveLineTime, 0) <= 0) {
            sOneWaveLineTime = (float)ONE_WAVE_VIEW_DURATION / getOneWaveItemAmplitueLineCount(context);
        }

        return sOneWaveLineTime;
    }

    public static String getStringBySecond(int seconds) {
        int hour = seconds / SIX_MINUTES_3600;
        int minute = seconds / MINUTES_60 % MINUTES_60;
        int second = seconds % MINUTES_60;
        Locale locale = Locale.getDefault();
        String timeStr = String.format(locale, BaseApplication.getAppContext().getString(R.string.time_default), minute, second, 0);

        if (hour > HOUR_10) {
            timeStr = String.format(locale, BaseApplication.getAppContext().getString(R.string.time_ten_hour), hour, minute, second, 0);
            return timeStr.substring(0, timeStr.length() - Constants.THREE);
        }

        if (hour > 0) {
            timeStr = String.format(locale, BaseApplication.getAppContext().getString(R.string.time_one_hour), hour, minute, second, 0);
            return timeStr.substring(0, timeStr.length() - Constants.THREE);
        }

        if (seconds >= 0) {
            timeStr = String.format(locale, BaseApplication.getAppContext().getString(R.string.time_default), minute, second, 0);
            return timeStr.substring(0, timeStr.length() - Constants.THREE);
        }
        return timeStr;
    }

    public static void clearAll() {
        DebugUtil.i(TAG, "WaveViewUtil clear all");
        sTimeLineGap = 0;
        sAmplitueLineGap = 0;
        sOneWaveItemWidth = 0;
        sOneWaveItemAmplitueLineCount = 0;
        sOneWaveLineTime = 0;
    }
}

