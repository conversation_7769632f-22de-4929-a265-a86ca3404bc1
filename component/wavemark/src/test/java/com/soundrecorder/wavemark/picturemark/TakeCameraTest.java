package com.soundrecorder.wavemark.picturemark;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;

import androidx.core.content.FileProvider;
import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.wavemark.shadows.ShadowFeatureOption;
import com.soundrecorder.wavemark.shadows.ShadowOS12FeatureUtil;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

import kotlin.Unit;
import static org.mockito.ArgumentMatchers.any;


@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class TakeCameraTest {

    private Context mContext;


    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
    }

    @After
    public void tearDown() {
        mContext = null;
    }


    @Test
    @PrepareForTest(TakeCamera.class)
    public void createIntent() {
        MockedStatic<FileProvider> mockedStatic = Mockito.mockStatic(FileProvider.class);
        mockedStatic.when(() -> FileProvider.getUriForFile(any(), any(), any())).thenReturn(Mockito.mock(Uri.class));
        TakeCamera takeCamera = Mockito.spy(new TakeCamera());
        Intent intent = takeCamera.createIntent(mContext, Unit.INSTANCE);
        Assert.assertNotNull(intent);
        mockedStatic.close();
    }

    @Test
    @PrepareForTest(TakeCamera.class)
    public void scanToAlbum() throws Exception {
//        String pictureFilePath = "IMG_SOUNDER_"+System.currentTimeMillis()+".jpg";
//        File file=FileUtils.getAppFile(pictureFilePath,true);
//        Assert.assertTrue(file.exists());
//
//        IntentFilter intentFilter = new IntentFilter();
//        intentFilter.addAction(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE);
//        intentFilter.addDataScheme("file");
//        final boolean[] hasReceivedBroadcast = {false};
//        BroadcastReceiver localReceiver = new BroadcastReceiver() {
//            @Override
//            public void onReceive(Context context, Intent intent) {
//                hasReceivedBroadcast[0] = true;
//            }
//        };
//        mContext.registerReceiver(localReceiver, intentFilter);
//        Whitebox.invokeMethod(TakeCamera.Companion,"scanToAlbum",file);
//        Assert.assertTrue(hasReceivedBroadcast[0]);

    }


}
