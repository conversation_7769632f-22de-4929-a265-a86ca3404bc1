/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: MarkListAdapterTest
 * Description:
 * Version: 1.0
 * Date: 2023/5/23
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/5/23 1.0 create
 */

package com.soundrecorder.wavemark.mark

import android.app.Activity
import android.content.Context
import android.os.Build
import android.view.View
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.recyclerview.widget.AsyncListDiffer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.transition.RemoveItemAnimator
import com.soundrecorder.wavemark.R
import com.soundrecorder.wavemark.shadows.ShadowFeatureOption
import com.soundrecorder.wavemark.shadows.ShadowOS12FeatureUtil
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class, ShadowOS12FeatureUtil::class])
class MarkListAdapterTest {
    private var mContext: Context? = null
    private var mActivity: Activity? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        mActivity = Robolectric.buildActivity(Activity::class.java).get()
    }

    @Test
    fun should_when_setData() {
        val adapter = MarkListAdapter(mContext, true)

        adapter.setData(null)
        Assert.assertNotNull(
            Whitebox.getInternalState<AsyncListDiffer<MarkDataBean>>(
                adapter, "diff"
            ).currentList
        )
        Assert.assertEquals(0, adapter.itemCount)
        Assert.assertEquals(0, adapter.itemCount)
    }

    @Test
    fun should_when_onAttachedToRecyclerView() {
        val adapter = MarkListAdapter(mContext, true)
        val recyclerView = RecyclerView(mContext!!)
        adapter.onAttachedToRecyclerView(recyclerView)
        Assert.assertNotNull(Whitebox.getInternalState<RecyclerView>(adapter, "recyclerView"))
        Assert.assertNotNull(Whitebox.getInternalState<RemoveItemAnimator>(adapter, "mAnimator"))
    }

    @Test
    fun should_when_onBindViewHolder() {
        val recyclerView = RecyclerView(mContext!!).apply {
            layoutManager = LinearLayoutManager(mContext)
        }
        val adapter = MarkListAdapter(mContext, true)
        val textMark = MarkDataBean(1000)
        val pictureMark = MarkDataBean(2000).apply {
            pictureFilePath = "fakePath"
        }
        adapter.setData(mutableListOf(textMark, pictureMark))
        // text mark
        val textHolder = adapter.onCreateViewHolder(recyclerView, 0)
        adapter.onBindViewHolder(textHolder, 0)
        Assert.assertNotNull(textHolder.mTextMarkTime.text)
        Assert.assertTrue(textHolder.mPhotoMark.isGone)
        Assert.assertTrue(textHolder.mBtnMenuMore.isVisible)

        // picture mark
        val pictureHolder = adapter.onCreateViewHolder(recyclerView, 1)
        adapter.onBindViewHolder(pictureHolder, 1)
        Assert.assertTrue(pictureHolder.mPhotoMark.isVisible)
        pictureHolder.mPhotoMark.performClick()

        adapter.onViewRecycled(pictureHolder)
    }

    @Test
    fun should_when_setOnMarkClickListener() {
        val recyclerView = RecyclerView(mContext!!).apply {
            layoutManager = LinearLayoutManager(mContext)
        }
        val adapter = MarkListAdapter(mContext, false, false, false, false)
        val textMark = MarkDataBean(1000)
        adapter.setData(mutableListOf(textMark))
        val textHolder = adapter.onCreateViewHolder(recyclerView, 0)
        adapter.onBindViewHolder(textHolder, 0)

        adapter.setOnMarkClickListener {
            Assert.assertEquals(textMark.timeInMills, it.timeInMills)
        }
        Assert.assertNotNull(Whitebox.getInternalState(adapter, "mOnMarkClickListener"))
        textHolder.mRootLayout.performClick()
        Assert.assertFalse(textHolder.mBtnMenuMore.hasOnClickListeners())
    }

    @Test
    fun should_correct_when_getShowMarkText() {
        val adapter = MarkListAdapter(mContext, true)
        val markDataBean = MarkDataBean(1000)
        // text is null,no is 0
        var result = Whitebox.invokeMethod<String>(adapter, "getShowMarkText", markDataBean)
        Assert.assertEquals(mContext!!.getString(R.string.custom_mark_description), result)
        // text is null,no not 0
        markDataBean.defaultNo = 1
        result = Whitebox.invokeMethod<String>(adapter, "getShowMarkText", markDataBean)
        Assert.assertEquals(mContext!!.getString(R.string.default_flag_new, 1), result)
        //text not null
        markDataBean.markText = "11"
        result = Whitebox.invokeMethod<String>(adapter, "getShowMarkText", markDataBean)
        Assert.assertEquals("11", result)
    }

    @Test
    fun should_showDialog_when_clickView() {
        val recyclerView = RecyclerView(mActivity!!).apply {
            layoutManager = LinearLayoutManager(mActivity)
        }
        val adapter = MarkListAdapter(mActivity, true)
        val textMark = MarkDataBean(1000)
        adapter.setData(mutableListOf(textMark))
        val textHolder = adapter.onCreateViewHolder(recyclerView, 0)
        adapter.onBindViewHolder(textHolder, 0)
        textHolder.mBtnMenuMore.performClick()
        Assert.assertNotNull(Whitebox.getInternalState(adapter, "mMenuPop"))

        adapter.dismissMenuPop()
        Assert.assertNull(Whitebox.getInternalState(adapter, "mMenuPop"))
    }

    @Test
    fun should_correct_when_showMenuPop() {
        var adapter = MarkListAdapter(mContext, true)
        Whitebox.invokeMethod<Unit>(adapter, "showMenuPop", MarkDataBean(1000), View(mActivity))
        var pop = Whitebox.getInternalState<COUIPopupListWindow>(adapter, "mMenuPop")
        Assert.assertNotNull(pop)
        Assert.assertEquals(2, pop.itemList.size)

        adapter = MarkListAdapter(mContext, true, true, false, true)
        Whitebox.invokeMethod<Unit>(adapter, "showMenuPop", MarkDataBean(1000), View(mActivity))
        pop = Whitebox.getInternalState<COUIPopupListWindow>(adapter, "mMenuPop")
        Assert.assertEquals(1, pop.itemList.size)

        adapter = MarkListAdapter(mActivity, true, true, false, false)
        Whitebox.invokeMethod<Unit>(adapter, "showMenuPop", MarkDataBean(1000), View(mActivity))
        pop = Whitebox.getInternalState<COUIPopupListWindow>(adapter, "mMenuPop")
        Assert.assertEquals(1, pop.itemList.size)
    }

    @Test
    fun should_when_showRenameMark() {
        val adapter = MarkListAdapter(mContext, true)
        val markDataBean = MarkDataBean(1000)
        adapter.showRenameMark(null, markDataBean, "")
        Assert.assertNull(adapter.mRenameMark)
        Assert.assertNull(adapter.renameMarkDialog)

        adapter.showRenameMark(mContext, null, "")
        Assert.assertNull(adapter.renameMarkDialog)

        adapter.setOnRenameMarkListener { data, newMarkText ->
            Assert.assertEquals("112", newMarkText)
        }
        adapter.showRenameMark(mActivity, markDataBean, "11")
        Assert.assertNotNull(adapter.mRenameMark)
        Assert.assertNotNull(adapter.renameMarkDialog)

        adapter.renameMarkDialog.getEditText()?.setText("112")
        adapter.renameMarkDialog.onSave()
        adapter.dismissRenameDialog()
        Assert.assertNull(adapter.renameMarkDialog)
    }

    @Test
    fun should_when_showDeleteMark() {
        val adapter = MarkListAdapter(mContext, true)
        val markDataBean = MarkDataBean(1000)
        adapter.showDeleteMark(null, markDataBean)
        Assert.assertNull(adapter.mDeleteMark)
        Assert.assertNull(adapter.deleteMarkDialog)

        adapter.showDeleteMark(mContext, null)
        Assert.assertNull(adapter.deleteMarkDialog)

        adapter.setOnDeleteListener {}
        adapter.showDeleteMark(mActivity, markDataBean)
        Assert.assertNotNull(adapter.mDeleteMark)
        Assert.assertNotNull(adapter.deleteMarkDialog)

        adapter.dismissDeleteDialog()
        Assert.assertNull(adapter.deleteMarkDialog)
    }
}