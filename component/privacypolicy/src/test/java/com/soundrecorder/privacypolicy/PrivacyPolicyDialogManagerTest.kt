/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: PrivacyPolicyDialogManagerTest
 Description:
 Version: 1.0
 Date: 2023/05/25 1.0
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2023/05/25 1.0 create
 */

package com.soundrecorder.privacypolicy

import android.content.Context
import android.os.Build
import androidx.appcompat.app.AppCompatActivity
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant
import com.soundrecorder.privacypolicy.shadows.ShadowCOUIMaxHeightScrollView
import com.soundrecorder.privacypolicy.shadows.ShadowCOUIVersionUtil
import com.soundrecorder.privacypolicy.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowFeatureOption::class,
        ShadowCOUIMaxHeightScrollView::class, ShadowCOUIVersionUtil::class]
)
class PrivacyPolicyDialogManagerTest {

    private var context: Context? = null
    private var mActivity: AppCompatActivity? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        mActivity = Robolectric.buildActivity(AppCompatActivity::class.java).get()
    }

    @Test
    fun check_addStatementView() {
        val manager = PrivacyPolicyDialogManager(mActivity!!, null)
        Whitebox.invokeMethod<PrivacyPolicyDialogManager>(manager, "addStatementView")
        Whitebox.invokeMethod<PrivacyPolicyDialogManager>(manager, "removeStatementView")
    }

    @Test
    fun check_createDialog() {
        val manager = PrivacyPolicyDialogManager(mActivity!!, null)
        manager.reCreateStateDialog()
        manager.checkAndDismissDialog()
        manager.onSaveShowingDialog()
        val array = arrayListOf<Int>()
        array.add(PrivacyPolicyConstant.TYPE_USER_NOTICE)
        array.add(PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC)
        array.add(PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC_STILL)
        array.add(PrivacyPolicyConstant.TYPE_USER_NOTICE_LIGHT_OS)
        array.add(PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_UPDATE)
        array.add(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT)
        array.add(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_SEARCH)
        array.add(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_WITHDRAWN)
        manager.onRestoreShowingDialog(array)
        Assert.assertFalse(manager.isShowing(PrivacyPolicyConstant.TYPE_USER_NOTICE))
        manager.resumeShowDialog(PrivacyPolicyConstant.TYPE_USER_NOTICE, false)
        manager.clearAll()
    }

    @After
    fun tearDown() {
        context = null
        mActivity = null
    }
}