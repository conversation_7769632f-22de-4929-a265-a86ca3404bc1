package com.photoviewer.ui

import android.content.ContentValues
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.provider.MediaStore.Images.Media.EXTERNAL_CONTENT_URI
import android.util.Log
import android.view.View
import android.view.View.OnClickListener
import android.widget.FrameLayout
import android.widget.Toast
import androidx.core.content.FileProvider
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsCompat.CONSUMED
import androidx.core.view.doOnPreDraw
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.core.view.updatePadding
import com.coui.appcompat.bottomnavigation.COUINavigationView
import com.coui.appcompat.toolbar.COUIToolbar
import com.photoviewer.PhotoViewer
import com.photoviewer.extensions.doAnimateAlpha
import com.soundrecorder.base.BaseActivity
import com.stfalcon.imageviewer.R
import kotlinx.coroutines.CoroutineName
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.Dispatchers.Main
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import kotlinx.coroutines.withContext
import java.io.File
import java.util.UUID

class PhotoViewerActivity : BaseActivity(), OnClickListener {

    companion object {
        /*用于识别,统计页面 勿改*/
        private const val FUNCTION_NAME = "PhotoViewer"
    }

    private val viewerView by lazy {
        findViewById<PhotoViewerView>(R.id.photoViewerView)
    }
    private val singleSaveRootView by lazy {
        findViewById<FrameLayout>(R.id.singleSaveRootView)
    }
    private val layoutHeader by lazy {
        findViewById<FrameLayout>(R.id.layoutHeader)
    }
    private val layoutFooter by lazy {
        findViewById<FrameLayout>(R.id.layoutFooter)
    }
    private val mainScope by lazy { MainScope() + CoroutineName("PictureMark") }

    override fun onCreate(savedInstanceState: Bundle?) {
        overridePendingTransition(0, 0)
        setRequestedOrientation()
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_photo_viewer)
        if (PhotoViewer.INSTANCE == null) {
            finish()
            return
        }
        setupPhotoViewerView(savedInstanceState == null)
        WindowCompat.getInsetsController(window, window.decorView).apply {
            isAppearanceLightStatusBars = false
            isAppearanceLightNavigationBars = false
        }
        WindowCompat.setDecorFitsSystemWindows(window, false)
        ViewCompat.setOnApplyWindowInsetsListener(window.decorView) { _, insets ->
            val systemBar = insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.systemBars())
            singleSaveRootView.updatePadding(
                    top = systemBar.top
            )
            layoutHeader.updatePadding(top = systemBar.top, right = systemBar.right, left = systemBar.left)
            layoutFooter.updatePadding(bottom = systemBar.bottom)
            CONSUMED
        }
        findViewById<COUIToolbar>(R.id.toolbar).setNavigationOnClickListener { onBackPressed() }
        findViewById<COUINavigationView>(R.id.navigationBar).apply {
            dividerView.visibility = View.GONE
            setOnNavigationItemSelectedListener {
                val src = PhotoViewer.src?.invoke(getCurrentPosition())
                if (src is File) {
                    if (it.itemId == R.id.save_to_gallery) {
                        saveToGallery(src)
                    } else if (it.itemId == R.id.share) {
                        shareImage(src)
                    }
                }
                true
            }
        }
        registerBackPressed()
    }

    private fun shareImage(src: File) {
        val intent = Intent(Intent.ACTION_SEND)
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        val uri = FileProvider.getUriForFile(this, "$packageName.fileProvider", src)
        intent.putExtra(Intent.EXTRA_STREAM, uri)
        intent.type = "image/*"
        startActivity(Intent.createChooser(intent, ""))
    }

    private fun saveToGallery(file: File) {
        mainScope.launch(IO) {
            var success = false
            val context = applicationContext
            try {
                val suffix = file.absolutePath.split(".").last()
                val values = ContentValues()
                values.put(
                        MediaStore.MediaColumns.DISPLAY_NAME,
                        "IMG_${UUID.randomUUID()}.$suffix"
                )
                values.put(MediaStore.MediaColumns.MIME_TYPE, "image/$suffix")
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    values.put(
                            MediaStore.MediaColumns.RELATIVE_PATH,
                            "${Environment.DIRECTORY_DCIM}${File.separator}Recorder"
                    )
                }
                val uri =
                        context.contentResolver.insert(EXTERNAL_CONTENT_URI, values)
                                ?: return@launch
                try {
                    val out = context.contentResolver.openOutputStream(uri)
                    if (out != null) {
                        file.inputStream().use { input ->
                            out.use { output ->
                                input.copyTo(output)
                            }
                        }
                        success = true
                    }
                } catch (e: Exception) {
                }
            } catch (e: Exception) {
            }
            withContext(Main) {
                if (success) {
                    Toast.makeText(
                            context, R.string.save_to_gallery_success, Toast.LENGTH_LONG
                    ).show()
                } else {
                    Log.e("saveToGallery", "saveToGallery failed")
                }
            }
        }
    }

    private fun getCurrentPosition(): Int = viewerView.currentPosition

    override fun onBackPressed() {
        if (viewerView.isScaled) {
            viewerView.resetScale()
        } else {
            PhotoViewer.INSTANCE?.onResult?.invoke(emptyList())
            closeViewer()
        }
    }

    private fun setupPhotoViewerView(animateOpen: Boolean) {
        viewerView.doOnPreDraw {
            viewerView.open(animateOpen) {
                singleSaveRootView.doAnimateAlpha(1f, it)
                layoutHeader.doAnimateAlpha(1f, it)
                layoutFooter.doAnimateAlpha(1f, it)
            }
        }
        viewerView.onScaleChange = {
            layoutHeader.isGone = true
            layoutFooter.isGone = true
        }
        setOtherView()
        viewerView.onDismiss = { finish() }
        viewerView.onClick = {
            if (PhotoViewer.INSTANCE?.onResult == null) {
                if (layoutHeader.isVisible) {
                    layoutHeader.isGone = true
                    layoutFooter.isGone = true
                } else {
                    layoutHeader.isVisible = true
                    layoutFooter.isVisible = true
                }
            }
        }
        viewerView.onSwipe = {
            if (PhotoViewer.INSTANCE?.onResult == null) {
                if (layoutHeader.isVisible) {
                    layoutHeader.alpha = it
                    layoutFooter.alpha = it
                }
            }
        }
    }

    private fun setOtherView() {
        if (PhotoViewer.INSTANCE?.onResult == null) {
            singleSaveRootView.isGone = true
            layoutHeader.isVisible = true
            layoutFooter.isVisible = true
        } else {
            singleSaveRootView.isVisible = true
            layoutHeader.isGone = true
            layoutFooter.isGone = true
            findViewById<View>(R.id.tvTitle).isGone = isInMultiWindowMode
            findViewById<View>(R.id.btnCancel).setOnClickListener(this)
            findViewById<View>(R.id.btnOk).setOnClickListener(this)
        }
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.btnCancel -> {
                if (PhotoViewer.INSTANCE != null) {
                    PhotoViewer.INSTANCE!!.onResult?.invoke(emptyList())
                }
                closeViewer()
            }
            R.id.btnOk -> {
                val data = PhotoViewer.INSTANCE?.images ?: emptyList()
                PhotoViewer.INSTANCE?.onResult?.invoke(data)
                closeViewer()
            }
        }
    }

    private fun closeViewer() {
        singleSaveRootView.isGone = true
        layoutHeader.isGone = true
        layoutFooter.isGone = true
        viewerView.animateClose()
    }

    override fun onDestroy() {
        if (isFinishing) {
            mainScope.cancel()
            viewerView.releaseAdapterCache()
            PhotoViewer.release()
        }
        unRegisterBackPressed()
        super.onDestroy()
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(0, 0)
    }
}