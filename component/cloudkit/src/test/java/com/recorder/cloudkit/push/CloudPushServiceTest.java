package com.recorder.cloudkit.push;

import android.content.Context;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.heytap.msp.push.mode.DataMessage;
import com.recorder.cloudkit.shadows.ShadowCOUIVersionUtil;
import com.recorder.cloudkit.shadows.ShadowFeatureOption;
import com.soundrecorder.base.BaseApplication;

import org.json.JSONException;
import org.json.JSONObject;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.robolectric.annotation.Config;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class, ShadowCOUIVersionUtil.class})
public class CloudPushServiceTest {

    private Context mContext;
    private MockedStatic<BaseApplication> mApplicationMockedStatic;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
        mApplicationMockedStatic = Mockito.mockStatic(BaseApplication.class);
        mApplicationMockedStatic.when(() -> BaseApplication.getAppContext()).thenReturn(mContext);
    }

    @Test
    public void should_return_processMessage() throws JSONException {
        CloudPushService service = new CloudPushService();
        DataMessage message = new DataMessage();

        JSONObject object = new JSONObject();
        object.put("channel","cloudkit");
        message.setContent(object.toString());
        service.processMessage(mContext, message);

        JSONObject object1 = new JSONObject();
        object1.put("channel","cloudkit");
        object1.put("op","sync");
        message.setContent(object1.toString());
        service.processMessage(mContext, message);

        JSONObject object2 = new JSONObject();
        object2.put("channel","cloudkit");
        object2.put("op","fullSync");
        message.setContent(object2.toString());
        service.processMessage(mContext, message);

        JSONObject object3 = new JSONObject();
        object3.put("action","enable_log_upload");
        message.setContent(object3.toString());
        service.processMessage(mContext, message);
    }
}
