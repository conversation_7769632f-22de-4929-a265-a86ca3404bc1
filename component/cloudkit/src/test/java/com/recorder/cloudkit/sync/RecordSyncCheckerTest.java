package com.recorder.cloudkit.sync;

import static org.mockito.Mockito.mock;
import static org.robolectric.Shadows.shadowOf;

import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.os.Build;
import android.os.PowerManager;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError;
import com.heytap.cloudkit.libsync.cloudswitch.bean.GetSyncSwitchResult;
import com.heytap.cloudkit.libsync.cloudswitch.bean.SwitchState;
import com.heytap.cloudkit.libsync.ext.CloudSyncManager;
import com.soundrecorder.common.permission.PermissionUtils;
import com.recorder.cloudkit.shadows.ShadowFeatureOption;
import com.recorder.cloudkit.shadows.ShadowStorageUtil;
import com.recorder.cloudkit.sync.bean.SyncCheckResult;
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowConnectivityManager;
import org.robolectric.shadows.ShadowIntent;
import org.robolectric.shadows.ShadowPowerManager;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowStorageUtil.class, ShadowFeatureOption.class})
public class RecordSyncCheckerTest {
    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
    }

    @Test
    public void when_checkCloudSwitchState() {
        MockedStatic<CloudSynStateHelper> stateHelperMockedStatic = Mockito.mockStatic(CloudSynStateHelper.class);
        stateHelperMockedStatic.when(CloudSynStateHelper::isLoginFromCache).thenReturn(false, true, true);

        SyncCheckResult syncCheckResult = RecordSyncChecker.checkCloudSwitchState(mContext, true);
        Assert.assertTrue(!syncCheckResult.success());

        CloudSyncManager mockCloudSyncManager = mock(CloudSyncManager.class);
        MockedStatic<CloudSyncManager> syncManagerMockedStatic = Mockito.mockStatic(CloudSyncManager.class);
        syncManagerMockedStatic.when(CloudSyncManager::getInstance).thenReturn(mockCloudSyncManager);
        syncManagerMockedStatic.when(mockCloudSyncManager::getSyncSwitchCompat).thenReturn(new GetSyncSwitchResult(CloudKitError.createSuccess(), SwitchState.CLOSE.state));
        syncCheckResult = RecordSyncChecker.checkCloudSwitchState(mContext, true);
        Assert.assertEquals(SyncErrorCode.RESULT_SWITCH_CLOSE, syncCheckResult.getResultCode());

        syncManagerMockedStatic.close();
        stateHelperMockedStatic.close();
    }

    @Ignore
    @Test
    public void when_checkSyncPreconditionsMet() {
        RecordSyncChecker.checkSyncPreconditionsMet(mContext, true);
    }

    @Test
    public void should_returnSuccess_when_checkPermission() {
        MockedStatic<PermissionUtils> mockedStatic = Mockito.mockStatic(PermissionUtils.class);
        mockedStatic.when(PermissionUtils::hasAllFilePermission).thenReturn(true);
        Assert.assertTrue(RecordSyncChecker.checkPermission(mContext).success());
        mockedStatic.when(PermissionUtils::hasAllFilePermission).thenReturn(false);
        Assert.assertFalse(RecordSyncChecker.checkPermission(mContext).success());
        mockedStatic.close();
    }

    @Test
    public void when_hasInternetConnect() {
        ConnectivityManager connManager = (ConnectivityManager) mContext.getSystemService(Context.CONNECTIVITY_SERVICE); // 获取网络服务
        ShadowConnectivityManager shadowConnectivityManager = shadowOf(connManager);
        shadowConnectivityManager.setActiveNetworkInfo(null);

        boolean hasInternet = RecordSyncChecker.hasInternetConnect(mContext);
        Assert.assertFalse(hasInternet);
    }

    @Test
    public void when_isLocalStorageAvailable() {
        boolean isAvailable = RecordSyncChecker.isLocalStorageAvailable(mContext);
        Assert.assertFalse(isAvailable);
    }

    @Ignore
    @Test
    public void when_getBatteryInfo() {
        IntentFilter intentFilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
        Intent intent = mContext.registerReceiver(null, intentFilter);
        ShadowIntent shadowIntent = shadowOf(intent);
        RecordSyncChecker.BatteryInfo batteryInfo = RecordSyncChecker.getBatteryInfo(mContext);
        Assert.assertNotNull(batteryInfo);
    }

    @Test
    public void when_isPowerSaveMode() {
        PowerManager connManager = (PowerManager) mContext.getSystemService(Context.POWER_SERVICE); // 获取网络服务
        ShadowPowerManager shadowPowerManager = shadowOf(connManager);
        shadowPowerManager.setIsPowerSaveMode(true);

        boolean isSaveMode = RecordSyncChecker.isPowerSaveMode(mContext);
        Assert.assertTrue(isSaveMode);
    }

    @Test
    public void when_checkNeedFullRecovery() {
        boolean isNeedCheck = RecordSyncChecker.checkNeedFullRecovery(mContext);
        Assert.assertTrue(isNeedCheck);
    }

    @Test
    public void when_updateFullRecoveryTime() {
        RecordSyncChecker.updateFullRecoveryTime(mContext);
    }

}
