/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - SharedPreferenceUtil
 ** Description:
 **         v1.0:   SharedPreferenceUtil
 **
 ** Version: 1.0
 ** Date: 2023/03/09
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot       2023/5/6   1.0      Create this module
 ********************************************************************************/
package com.oplus.soundrecorder.semantic.ssa

import android.content.Context

object SharedPreferenceUtil {

    const val SHARED_PREFERENCES_NAME = "semantic_tool"
    const val VERSION_NAME = "version_name"

    @JvmStatic
    fun setVersionName(context: Context, value: String?) {
        val sharedPreferences =
            context.getSharedPreferences(SHARED_PREFERENCES_NAME, Context.MODE_PRIVATE)
        val editor = sharedPreferences.edit()
        editor.putString(VERSION_NAME, value)
        editor.apply()
    }

    @JvmStatic
    fun getVersionName(context: Context): String? {
        val sharedPreferences =
            context.getSharedPreferences(SHARED_PREFERENCES_NAME, Context.MODE_PRIVATE)
        return sharedPreferences.getString(VERSION_NAME, "")
    }
}