/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - SemanticParseHelper
 ** Description:
 **         v1.0:   parse time in text
 **
 ** Version: 1.0
 ** Date: 2023/02/27
 ** Author: <PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot       2023/2/27   1.0      Create this module
 ********************************************************************************/
package com.oplus.soundrecorder.semantic.ssa.timexparser

import com.google.gson.Gson
import com.oplus.soundrecorder.semantic.ssa.base.SemanticBean
import com.oplus.soundrecorder.semantic.ssa.base.SemanticTime
import com.soundrecorder.base.utils.DebugUtil

object SemanticParseHelper {

    private const val TAG = "SpeechParseHelper"
    private const val TIMEX_DATE = "date"
    private const val TIMEX_TIME = "exact_time"
    private const val TIMEX_FESTIVAL = "festival"

    @JvmStatic
    fun parseAlarmTime(jsonStr: String): SemanticTime? {
        DebugUtil.d(TAG, "parseAlarmTime: result:$jsonStr")
        return parseAnalysisResult(jsonStr)
    }

    @JvmStatic
    @Suppress("LongMethod")
    private fun parseAnalysisResult(analysisResult: String?): SemanticTime? {
        DebugUtil.d(TAG, "parseAnalysisResult")
        var ret: SemanticTime? = null
        if (analysisResult != null) {
            kotlin.runCatching {
                val gson = Gson()
                val analysisBean = gson.fromJson(analysisResult, SemanticBean::class.java)
                var firstDateData: SemanticBean.DateObject? = null
                var firstTimeData: SemanticBean.DateObject? = null
                if (analysisBean != null) {
                    DebugUtil.d(TAG, "parseAnalysisResult: $analysisBean")
                    analysisBean.entities?.forEach { item ->
                        //SDK返回结果数据不正确跳出循环
                        if (item.date == null && item.exactTime == null && item.festival == null) {
                            DebugUtil.e(TAG, "parseAnalysisResult: empty analysisBean")
                            return@forEach
                        }

                        //已经检索出第一个日期和第一个时间则跳出循环
                        if (firstDateData != null && firstTimeData != null) {
                            return@forEach
                        }
                        if ((firstDateData == null) && (item.date != null) && (item.date?.type == TIMEX_DATE)) {
                            firstDateData = item.date
                        }
                        if ((firstDateData == null) && (item.festival != null) && (item.festival?.type == TIMEX_FESTIVAL)) {
                            firstDateData = item.festival
                        }
                        if ((firstTimeData == null) && (item.exactTime != null) && (item.exactTime?.type == TIMEX_TIME)) {
                            firstTimeData = item.exactTime
                        }
                    }
                }

                DebugUtil.d(
                    TAG,
                    "parseAnalysisResult: firstDateData:$firstDateData ,firstTimeData:$firstTimeData"
                )
                //have time or date at least(至少有时间字段或日期字段中的1个才可以推断准确时间点）
                if (firstDateData != null || firstTimeData != null) {
                    val alarmObject = SemanticTime()
                    firstDateData?.let {
                        alarmObject.addHighLightRange(it.offset, it.length)
                    }
                    firstTimeData?.let {
                        alarmObject.addHighLightRange(it.offset, it.length)
                    }

                    TimexParserManager.parserTimeX(
                        alarmObject,
                        firstDateData?.timex,
                        firstTimeData?.timex
                    )

                    if (alarmObject.isDateValid() || alarmObject.isNoTimeDateValid()) {
                        ret = alarmObject
                    }
                }
            }.onSuccess {
                DebugUtil.d(TAG, "parseAnalysisResult success")
            }.onFailure {
                DebugUtil.e(TAG, "parseAnalysisResult failed!${it.message}")
            }
        } else {
            DebugUtil.e(TAG, "analysisResult is null")
        }
        return ret
    }
}