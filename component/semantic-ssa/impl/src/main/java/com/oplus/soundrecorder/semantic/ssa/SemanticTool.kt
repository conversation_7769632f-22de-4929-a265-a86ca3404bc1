/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - SemanticTool
 ** Description:
 **         v1.0:   Create SemanticTool file
 **
 ** Version: 1.0
 ** Date: 2023/03/09
 ** Author: <PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot       2023/5/6   1.0      Create this module
 ********************************************************************************/
package com.oplus.soundrecorder.semantic.ssa

import android.content.Context
import android.text.TextUtils
import com.oplus.soundrecorder.semantic.ssa.api.ISemanticTool
import com.oplus.soundrecorder.semantic.ssa.base.SemanticTime
import com.oplus.soundrecorder.semantic.ssa.timexparser.SemanticParseHelper
import com.slp.cu.ssa.SemanticParser
import com.slp.library.SlpSdk
import com.soundrecorder.base.utils.DebugUtil
import java.io.File
import java.io.FileOutputStream

class SemanticTool : ISemanticTool {

    companion object {
        private const val TAG = "SemanticTool"

        private const val CONFIG_FILE_NAME = "config.json"
        private const val BIN_FILE_TAG_TIME_NAME = "tag_time_re.bin"
        private const val BIN_FILE_TIME_SLOT_NAME = "time_slot_model_231011.bin"
        private const val BIN_FILE_SLOT_RE = "slot_re.bin"

        private const val BUFFER_SIZE = 1024
    }

    override fun install(context: Context) {
        SlpSdk.install(context)
        DebugUtil.d(TAG, "install")
    }

    override fun initModel(context: Context): Boolean {
        val versionName = context.resources.getString(R.string.version_name)
        val oldVersionName = SharedPreferenceUtil.getVersionName(context)
        val newPath = context.filesDir.absolutePath + versionName
        if (TextUtils.equals(versionName, oldVersionName)) {
            return SemanticParser.initModel(context, "$newPath/$CONFIG_FILE_NAME")
        } else {
            val oldPath = context.filesDir.absolutePath + oldVersionName
            val oldFile = File(oldPath)
            if (!TextUtils.isEmpty(oldVersionName) && oldFile.exists()) {
                deleteFile(oldFile)
            }
            val hasConfigFile = copyFile(context, newPath, CONFIG_FILE_NAME, R.raw.config)
            val hasBinFile = (copyFile(context, newPath, BIN_FILE_TAG_TIME_NAME, R.raw.tag_time_re)
                && copyFile(context, newPath, BIN_FILE_TIME_SLOT_NAME, R.raw.time_slot_model_231011)
                && copyFile(context, newPath, BIN_FILE_SLOT_RE, R.raw.slot_re))
            if (hasConfigFile && hasBinFile) {
                SharedPreferenceUtil.setVersionName(context, versionName)
                return SemanticParser.initModel(context, "$newPath/$CONFIG_FILE_NAME")
            }
        }
        return false
    }

    override fun process(context: Context, text: String): SemanticTime? {
        val jsonStr = SemanticParser.process(text, null)
        return SemanticParseHelper.parseAlarmTime(jsonStr)
    }

    override fun processResult(context: Context, result: String): SemanticTime? {
        return SemanticParseHelper.parseAlarmTime(result)
    }

    override fun release() {
        SemanticParser.release()
    }

    private fun copyFile(context: Context, fileDirPath: String, fileName: String, id: Int): Boolean {
        val filePath = "$fileDirPath/$fileName"
        var result = false
        runCatching {
            val dir = File(fileDirPath)
            if (!dir.exists()) {
                dir.mkdirs()
            }
            val file = File(filePath)
            if (!file.exists()) {
                file.createNewFile()
                context.resources.openRawResource(id).use { inputStream ->
                    FileOutputStream(file).use { fs ->
                        val buffer = ByteArray(BUFFER_SIZE)
                        var count: Int
                        while (inputStream.read(buffer).also { count = it } > 0) {
                            fs.write(buffer, 0, count)
                        }
                    }
                }
            }
        }.onFailure {
            result = false
            DebugUtil.e(TAG, "${it.message}")
        }.onSuccess {
            result = true
        }
        return result
    }

    private fun deleteFile(file: File) {
        if (file.isFile) {
            file.delete()
        } else {
            val files = file.listFiles()
            if (files != null) {
                for (listFile in files) {
                    listFile.delete()
                }
                file.delete()
            }
        }
    }
}