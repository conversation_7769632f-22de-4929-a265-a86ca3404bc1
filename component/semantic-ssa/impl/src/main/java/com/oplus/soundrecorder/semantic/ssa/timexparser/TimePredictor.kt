/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - TimePredictor
 ** Description:
 **         v1.0:   Create TimePredictor file
 **
 ** Version: 1.0
 ** Date: 2023/04/24
 ** Author: <PERSON><PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot       2023/4/24   1.0      Create this module
 ********************************************************************************/
package com.oplus.soundrecorder.semantic.ssa.timexparser

import androidx.annotation.VisibleForTesting
import com.oplus.soundrecorder.semantic.ssa.base.SemanticTime
import com.soundrecorder.base.utils.DebugUtil
import java.time.LocalTime
import java.util.Calendar

data class TimePredictor(val hour: Int, val minute: Int, val extraDay: Int = 0) {

    companion object {
        const val TIME_5 = 5
        const val TIME_9 = 9
        const val TIME_12 = 12
        const val TIME_17 = 17
        const val TIME_20 = 20
        const val TIME_23 = 23
        const val TIME_0 = 0
        const val TIME_24 = 24
        const val TAG = "TimePredictor"
    }

    class Builder {
        private var prefix: String = ""
        private var originHour: Int = 0
        private var originMin: Int = 0

        @get:VisibleForTesting
        var originTime: LocalTime? = null

        @get:VisibleForTesting
        var currentHour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY) //24

        @get:VisibleForTesting
        var currentMin = Calendar.getInstance().get(Calendar.MINUTE)

        @get:VisibleForTesting
        val currentTime: LocalTime = LocalTime.of(currentHour, currentMin)

        @get:VisibleForTesting
        var semanticTime: SemanticTime? = null

        fun setOriginTime(p: String, h: Int, m: Int): Builder {
            this.prefix = p
            this.originHour = h
            this.originMin = m
            this.originTime = LocalTime.of(originHour.mod(TIME_24), originMin)
            return this
        }

        fun setSemanticTime(semanticTime: SemanticTime): Builder {
            this.semanticTime = semanticTime
            return this
        }


        /**
         * eg: 今天早上8点
         *    16点
         * prefix 都是T
         */
        private fun whetherAddDay(originHour: Int, originMin: Int): TimePredictor {
            return if (semanticTime?.hasOriginDate == true) {
                if (originHour == TIME_24) {
                    TimePredictor(0, originMin, 1)
                } else {
                    TimePredictor(originHour, originMin, 0)
                }
            } else {
                return if (currentTime.isAfter(originTime)) {
                    TimePredictor(originHour.mod(TIME_24), originMin, 1)
                } else {
                    TimePredictor(originHour, originMin, 0)
                }
            }
        }

        /**
         * eg: 今天八点
         * @return 8:00 or 20:00
         * eg: 明天/后天8点
         * 只有0-12点才会走AT
         * @return 8:00
         */
        @VisibleForTesting
        fun predictAmbiguous(originHour: Int, originMin: Int): TimePredictor {
            return if (semanticTime?.isToday() == true) {
                if (originTime?.isAfter(currentTime) == true && semanticTime?.hasOriginDate == true) {
                    TimePredictor(originHour, originMin, 0)
                } else {
                    /**
                     * before current time
                     * It is necessary to distinguish whether the original text has a clear date
                     */
                    getNextAvailableTimePoint(originHour, originMin)
                }
            } else {
                TimePredictor(originHour, originMin, 0)
            }
        }

        /**
         * 十二时辰推断，对应E01-E12
         * @param input 十二时辰顺序，对应1-12，不可能大于12
         * @return 十二时辰开始点
         */
        @VisibleForTesting
        fun predictTwelveDivision(input: Int): TimePredictor? {

            if (input > TIME_12) {
                return null
            }

            val futureHour = (input - 1) * 2 + TIME_23
            val isNextDay = currentTime.isAfter(LocalTime.of(futureHour.mod(TIME_24), 0))

            return TimePredictor(
                futureHour % TIME_24,
                originMin,
                if (isNextDay) 1 else 0
            )
        }


        /**
         * origin hour must be 0 - 12
         * or it will be "TXX:XX", like "T13:00"
         */
        @VisibleForTesting
        fun getNextAvailableTimePoint(originHour: Int, originMin: Int): TimePredictor {
            if (originHour != TIME_12) {
                val inputTime = LocalTime.of(originHour, originMin)
                val availableTimes = mutableListOf(
                    inputTime.withHour(originHour).withMinute(originMin),
                    inputTime.withHour(originHour + TIME_12).withMinute(originMin)
                )
                val nextTime = availableTimes.firstOrNull { it.isAfter(currentTime) }
                    ?: return if (semanticTime?.hasOriginDate == false) {
                        TimePredictor(originHour, originMin, 1)
                    } else {
                        TimePredictor(originHour, originMin, 0)
                    }

                return TimePredictor(nextTime.hour, nextTime.minute, 0)
            } else {
                return if (currentTime.isAfter(LocalTime.of(TIME_12, TIME_0))) {
                    TimePredictor(TIME_0, originMin, 1)
                } else {
                    TimePredictor(TIME_12, originMin, 0)
                }
            }
        }

        /**
         * eg:早上，中午...
         * @return 8:00 12:00
         */
        @VisibleForTesting
        fun predictPeriodTime(prefix: String): TimePredictor? {
            return when (prefix) {
                "TMI" -> TimePredictor(TIME_12, 0, dateOfAdd(TIME_12))
                "TAF" -> TimePredictor(TIME_17, 0, dateOfAdd(TIME_17))
                "TEV" -> TimePredictor(TIME_20, 0, dateOfAdd(TIME_20))
                "TMO" -> TimePredictor(TIME_9, 0, dateOfAdd(TIME_9))
                "TEM" -> TimePredictor(TIME_5, 0, dateOfAdd(TIME_5))
                /**
                 * At 0:00, 1 day needs to be added unconditionally
                 * regardless of whether there is an exact date
                 */
                "TNI" -> TimePredictor(TIME_0, 0, 1)
                else -> null
            }
        }

        /**
         * eg： 今天早上 今天9：00
         * eg: 早上 今天9：00 or 明天9点
         * eg: 当前时间9：01 ，早上 = 明天9点
         */
        @VisibleForTesting
        fun dateOfAdd(period: Int): Int {
            return if ((currentHour >= period) && (semanticTime?.hasOriginDate == false)) {
                1
            } else {
                0
            }
        }

        fun build(): TimePredictor? {
            var predictor: TimePredictor? = null
            kotlin.runCatching {
                predictor = when (prefix) {
                    "T" -> whetherAddDay(originHour, originMin)
                    "AT" -> predictAmbiguous(originHour, originMin)
                    "TMI", "TAF", "TEV", "TMO", "TEM", "TNI" -> null
                    "E" -> predictTwelveDivision(originHour)
                    else -> null
                }
            }.onSuccess {
                return predictor
            }.onFailure {
                DebugUtil.e(TAG, "build date error:${it.message}")
                return null
            }
            return predictor
        }
    }
}