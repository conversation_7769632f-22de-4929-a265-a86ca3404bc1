/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - TimexParserManager
 ** Description:
 **         v1.0:   parse time in text
 **
 ** Version: 1.0
 ** Date: 2023/02/27
 ** Author: <PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot       2023/2/27   1.0      Create this module
 ********************************************************************************/
package com.oplus.soundrecorder.semantic.ssa.timexparser

import androidx.annotation.VisibleForTesting
import com.oplus.soundrecorder.semantic.ssa.base.SemanticTime
import com.soundrecorder.base.utils.DebugUtil
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.regex.Pattern

/**
 * Created by 80326921 on 2021/6/3.
 * Email
 */
object TimexParserManager {

    //提取日期的正则表达式
    private val datePattern by lazy {
        Pattern.compile("TimeX\\|(\\S+)\\|Date")
    }
    private val timePattern by lazy {
        Pattern.compile("TimeX\\|(\\S+)\\|Time")
    }

    private const val TAG = "TimexParserManager"
    private val mDateParserList: MutableList<AbstractParser> = ArrayList()
    private val mTimeParserList: MutableList<AbstractParser> = ArrayList()

    init {
        //注册日期解析器（Date+Festival）
        registerDateParser(DateParserNormal())

        //注册时间解析器
        registerTimeParser(TimeParserNormal())
    }


    @VisibleForTesting
    fun registerDateParser(parser: AbstractParser) {
        mDateParserList.add(parser)
    }

    private fun registerTimeParser(parser: AbstractParser) {
        mTimeParserList.add(parser)
    }

    @VisibleForTesting
    fun loopDateParser(semanticTime: SemanticTime, timexStr: String?) {
        if (timexStr == null) {
            return
        }
        for (dataParser in mDateParserList) {
            if (dataParser.parser(semanticTime, timexStr)) {
                break
            }
        }
    }

    @VisibleForTesting
    fun loopTimeParser(semanticTime: SemanticTime, timexStr: String?) {
        if (timexStr == null) {
            return
        }
        for (timeParser in mTimeParserList) {
            if (timeParser.parser(semanticTime, timexStr)) {
                break
            }
        }
    }


    fun parserTimeX(semanticTime: SemanticTime, dateStr: String?, timexStr: String?) {
        DebugUtil.d(TAG, "parserTimeX: dateStr:$dateStr , timexStr:$timexStr")
        if (dateStr.isNullOrEmpty()) {
            semanticTime.setHasOriginDate(false)
            loopDateParser(semanticTime, obtainCurrentDate())
        } else {
            semanticTime.setHasOriginDate(true)
            val matcher = datePattern.matcher(dateStr)
            //解析为Date类型
            if (matcher.find() && matcher.groupCount() >= 1) {
                loopDateParser(semanticTime, matcher.group(1))
            }
        }

        if (timexStr.isNullOrEmpty()) {
            semanticTime.setHasOriginTime(false)
            DebugUtil.d(TAG, "parserTimeX: Not supported when time is empty")
        } else {
            semanticTime.setHasOriginTime(true)
            val matcher = timePattern.matcher(timexStr)
            //解析为Time类型
            if (matcher.find() && matcher.groupCount() >= 1) {
                loopTimeParser(semanticTime, matcher.group(1))
            }
        }
    }

    /**
     * If there is no specific date, the current date is generated as the default date
     */
    private fun obtainCurrentDate(): String? {
        val currentDate = LocalDate.now()
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
        return currentDate.format(formatter)
    }
}