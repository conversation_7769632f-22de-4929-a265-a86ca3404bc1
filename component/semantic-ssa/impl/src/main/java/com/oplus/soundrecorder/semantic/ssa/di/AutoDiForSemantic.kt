/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  AutoDiForSemantic.kt
 * * Description : AutoDiForSemantic
 * * Version     : 1.0
 * * Date        : 2025/07/01
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.oplus.soundrecorder.semantic.ssa.di

import androidx.annotation.Keep
import com.oplus.soundrecorder.semantic.ssa.SemanticTool
import com.oplus.soundrecorder.semantic.ssa.api.ISemanticTool
import org.koin.dsl.module

@Keep
object AutoDiForSemantic {

    val semanticModule = module {
        single<ISemanticTool>(createdAtStart = true) {
            SemanticTool()
        }
    }
}