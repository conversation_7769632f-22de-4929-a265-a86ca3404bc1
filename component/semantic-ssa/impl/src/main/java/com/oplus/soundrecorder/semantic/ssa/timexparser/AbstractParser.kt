/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - AbstractParser
 ** Description:
 ** v1.0:   parse time in text
 **
 ** Version: 1.0
 ** Date: 2023/02/27
 ** Author: <PERSON><PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<EMAIL>       2023/2/27   1.0      Create this module
 ********************************************************************************/
package com.oplus.soundrecorder.semantic.ssa.timexparser

import com.oplus.soundrecorder.semantic.ssa.base.SemanticTime

abstract class AbstractParser {

    /**
     * @param semanticTime 实体对象
     * @param timexString 文字
     */
    abstract fun parser(semanticTime: SemanticTime, timexString: String): Boolean
}