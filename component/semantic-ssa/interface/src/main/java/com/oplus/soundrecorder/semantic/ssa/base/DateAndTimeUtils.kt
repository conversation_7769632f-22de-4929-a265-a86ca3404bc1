/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: DateAndTimeUtils.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2024/01/29
 * * Author: W9013333
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/

package com.oplus.soundrecorder.semantic.ssa.base

import android.content.Context
import android.text.format.DateUtils
import android.text.format.DateUtils.FORMAT_NUMERIC_DATE
import android.text.format.DateUtils.FORMAT_SHOW_DATE
import android.text.format.DateUtils.FORMAT_SHOW_TIME
import android.text.format.DateUtils.FORMAT_SHOW_YEAR
import com.soundrecorder.base.utils.DebugUtil
import java.util.Calendar
import java.util.Date
import java.util.Locale

object DateAndTimeUtils {

    private const val TAG = "DateAndTimeUtils"
    private const val TIME_SECOND = 1000
    private const val TIME_MIN = 60
    private const val LANGUAGE_DE = "de"
    private const val LANGUAGE_FR = "fr"
    private const val LANGUAGE_IT = "it"

    @JvmStatic
    fun calendarSwitchByMinute(calendar: Calendar): Calendar {
        val timeInMillis = calendar.timeInMillis
        val minuteCount = timeInMillis / TIME_SECOND / TIME_MIN
        val adjustTimeMills = minuteCount * TIME_MIN * TIME_SECOND
        calendar.timeInMillis = adjustTimeMills
        return calendar
    }

    /**
     *  日期 本土化 样式格式 语言和地区不同样式都可能不一样
     */
    @JvmStatic
    fun timeInMillis2Date(ctx: Context?, timeInMillis: Long, showNumeric: Boolean): String {
        if (ctx == null) return ""
        val flags = if (showNumeric) {
            FORMAT_SHOW_YEAR or FORMAT_SHOW_DATE or FORMAT_NUMERIC_DATE
        } else {
            FORMAT_SHOW_YEAR or FORMAT_SHOW_DATE
        }
        return formatDateTime(ctx, timeInMillis, flags)
    }

    /**
     *  时间 本土化 样式格式 语言和地区不同样式都可能不一样
     */
    @JvmStatic
    fun timeInMillis2Time(ctx: Context?, timeInMillis: Long, showNumeric: Boolean): String {
        if (ctx == null) return ""
        val flags = if (showNumeric) {
            FORMAT_SHOW_TIME or FORMAT_NUMERIC_DATE
        } else {
            FORMAT_SHOW_TIME
        }
        return formatDateTime(ctx, timeInMillis, flags)
    }

    /**
     *  日期和时间 本土化 样式格式 语言和地区不同样式都可能不一样
     */
    @JvmStatic
    fun timeInMillis2DateAndTime(ctx: Context?, timeInMillis: Long, showNumeric: Boolean): String {
        if (ctx == null) return ""
        val flags = if (showNumeric) {
            FORMAT_SHOW_YEAR or FORMAT_SHOW_DATE or FORMAT_SHOW_TIME or FORMAT_NUMERIC_DATE
        } else {
            FORMAT_SHOW_YEAR or FORMAT_SHOW_DATE or FORMAT_SHOW_TIME
        }
        return formatDateTime(ctx, timeInMillis, flags)
    }

    @JvmStatic
    private fun formatDateTime(context: Context, timeInMillis: Long, flags: Int): String {
        runCatching {
            return DateUtils.formatDateTime(context, timeInMillis, flags)
        }.onFailure {
            DebugUtil.e(TAG, "formatDateTime", it)
        }
        return ""
    }

    @JvmStatic
    fun timeInMillisToMonthAndDaySpecWithTimezone(ctx: Context?, timeInMillis: Long): String {
        if (ctx == null) return ""
        val language = Locale.getDefault().language
        val flags = if (language == LANGUAGE_DE || language == LANGUAGE_FR || language == LANGUAGE_IT) {
            FORMAT_SHOW_DATE or DateUtils.FORMAT_NO_YEAR or DateUtils.FORMAT_ABBREV_ALL
        } else {
            FORMAT_SHOW_DATE or DateUtils.FORMAT_NO_YEAR or DateUtils.FORMAT_ABBREV_MONTH
        }
        return formatDateTime(ctx, timeInMillis, flags)
    }

    @JvmStatic
    fun isSameDay(date1: Date, date2: Date): Boolean {
        DebugUtil.d(TAG, "checkSameDay date1=$date1,date2=$date2")
        val calendar1 = Calendar.getInstance()
        calendar1.time = date1
        val calendar2 = Calendar.getInstance()
        calendar2.time = date2
        val isSameYear = calendar1.get(Calendar.YEAR) == calendar2.get(Calendar.YEAR)
        val isSameMonth = calendar1.get(Calendar.MONTH) == calendar2.get(Calendar.MONTH)
        val isSameDay = calendar1.get(Calendar.DAY_OF_MONTH) == calendar2.get(Calendar.DAY_OF_MONTH)
        return isSameYear && isSameMonth && isSameDay
    }
}