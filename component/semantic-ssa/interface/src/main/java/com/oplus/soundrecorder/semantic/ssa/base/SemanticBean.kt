/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - SemanticBean
 ** Description:
 **         v1.0:   parse time in text
 **
 ** Version: 1.0
 ** Date: 2023/02/27
 ** Author: <PERSON><PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot       2023/2/27   1.0      Create this module
 ********************************************************************************/
package com.oplus.soundrecorder.semantic.ssa.base

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
@Suppress("MatchingDeclarationName")
class SemanticBean {

    @SerializedName("entities")
    var entities: List<Entities>? = null

    @SerializedName("query")
    var query: String? = null
    override fun toString(): String {
        val sb = StringBuilder()
        entities?.forEach {
            sb.append(it.toString())
        }
        return sb.toString()
    }

    @Keep
    class Entities {
        @SerializedName("date")
        var date: DateObject? = null

        @SerializedName("exact_time")
        var exactTime: DateObject? = null

        @SerializedName("festival")
        var festival: DateObject? = null

        override fun toString(): String {
            return "Entities(date=$date, exactTime=$exactTime ,festival = $festival)"
        }
    }

    @Keep
    data class DateObject(
        @SerializedName("byte_length") var byteLength: Int,
        @SerializedName("byte_offset") var byteOffset: Int,
        @SerializedName("length") var length: Int,
        @SerializedName("offset") var offset: Int,
        @SerializedName("timex") var timex: String? = "",
        @SerializedName("type") var type: String? = "",
        @SerializedName("value") var value: String? = "",
        @SerializedName("value_ori") var valueOri: String? = "",
    ) {
        override fun toString(): String {
            return ("DateObject{byte_length=$byteLength, b" +
                    "yte_offset=$byteOffset, length=$length," +
                    " offset=$offset, timex='$timex'," +
                    " type='$type', value='$value', " +
                    "value_ori='$valueOri'}")
        }
    }
}