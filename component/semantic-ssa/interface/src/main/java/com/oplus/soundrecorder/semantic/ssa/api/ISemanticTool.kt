/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - ISemanticTool
 ** Description:
 **         v1.0:   Create ISemanticTool file
 **
 ** Version: 1.0
 ** Date: 2023/03/09
 ** Author: <PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot       2023/3/9   1.0      Create this module
 ********************************************************************************/
package com.oplus.soundrecorder.semantic.ssa.api

import android.content.Context
import com.oplus.soundrecorder.semantic.ssa.base.SemanticTime

interface ISemanticTool {

    fun install(context: Context)

    fun initModel(context: Context): Boolean

    fun release()

    fun process(context: Context, text: String): SemanticTime?

    /**
     * 服务端做NLP，直接返回结果，可直接解析
     * */
    fun processResult(context: Context, result: String): SemanticTime?
}