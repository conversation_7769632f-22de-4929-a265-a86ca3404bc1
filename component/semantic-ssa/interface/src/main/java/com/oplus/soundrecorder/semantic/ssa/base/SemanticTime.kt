/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - SemanticTime
 ** Description:
 **         v1.0:   Create SemanticTime file
 **
 ** Version: 1.0
 ** Date: 2023/02/27
 ** Author: <PERSON><PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot       2023/2/27   1.0      Create this module
 ********************************************************************************/
package com.oplus.soundrecorder.semantic.ssa.base

import java.util.Calendar
import java.util.Date

/**
 * Created by 80326921 on 2021/6/3.
 * Email
 */
class SemanticTime {
    private val calendar: Calendar = DateAndTimeUtils.calendarSwitchByMinute(Calendar.getInstance())
    private var dateInitialed: Boolean = false
    private var timeInitialed: Boolean = false
    var hasOriginDate: Boolean = false
        private set
    var hasOriginTime: Boolean = false
        private set
    var isOriginDateUncertain: Boolean = false
        private set

    /*
     * Distinguish whether it is an indefinite time
     * eg:三点开会 AT03:00  下午三点开会 T15:00
     */
    private var timePrefix: String? = null
    private var hightList = mutableListOf<HightRange>()


    fun isDateValid(): Boolean {

        //日期与时间都设置并且大于0,且识别的时间在当前时间以后
        return dateInitialed && timeInitialed && calendar.time.time > 0 && calendar.time.after(Calendar.getInstance().time)
    }

    fun isNoTimeDateValid(): Boolean {
        //日期大于0,且识别的时间在当前时间以后
        return dateInitialed && calendar.time.time > 0 && calendar.time.after(Calendar.getInstance().time)
    }

    fun getTimeInitialed(): Boolean {
        return timeInitialed
    }

    fun addHighLightRange(start: Int, length: Int) {
        if (start >= 0 && length >= 0) {
            hightList.add(HightRange(start, start + length))
        }
    }

    fun getHighList(): MutableList<HightRange> = hightList


    fun setDate(date: Calendar) {
        calendar.set(Calendar.YEAR, date.get(Calendar.YEAR))
        calendar.set(Calendar.MONTH, date.get(Calendar.MONTH))
        calendar.set(Calendar.DAY_OF_MONTH, date.get(Calendar.DAY_OF_MONTH))
        dateInitialed = true
    }

    fun setTime(hour: Int, minute: Int) {
        calendar.set(Calendar.HOUR_OF_DAY, hour)
        calendar.set(Calendar.MINUTE, minute)
        timeInitialed = true
    }

    fun translate2Date(): Date = calendar.time

    fun isToday(): Boolean {
        val currentDate = Calendar.getInstance()
        return (dateInitialed
                && (calendar.get(Calendar.YEAR) == currentDate.get(Calendar.YEAR))
                && (calendar.get(Calendar.MONTH) == currentDate.get(Calendar.MONTH))
                && (calendar.get(Calendar.DAY_OF_MONTH) == currentDate.get(Calendar.DAY_OF_MONTH)))
    }

    fun setHasOriginDate(has: Boolean) {
        this.hasOriginDate = has
    }

    fun setHasOriginTime(has: Boolean) {
        this.hasOriginTime = has
    }

    /**
     * 原始日期是否是不确定的
     * 如2023-09-xx 或2023-xx-xx等情况
     */
    fun setOriginDateUncertain(isUncertain: Boolean) {
        this.isOriginDateUncertain = isUncertain
    }
    /**
     * if date Initialed , add extra day to date.
     */
    fun plusDate(extraDay: Int) {
        if (dateInitialed && extraDay != 0) {
            calendar.add(Calendar.DAY_OF_MONTH, extraDay)
        }
    }

    fun isSemanticTimeEqual(semanticTime: SemanticTime?): Boolean {
        if (semanticTime == null) {
            return false
        }
        if (semanticTime.isDateValid() != this.isDateValid()) {
            return false
        }
        if (semanticTime.translate2Date() != this.translate2Date()) {
            return false
        }
        if (semanticTime.hightList != this.hightList) {
            return false
        }
        return true
    }
}