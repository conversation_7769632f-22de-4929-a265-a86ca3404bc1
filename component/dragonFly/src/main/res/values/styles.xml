<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <declare-styleable name="AppCardButton">
        <attr name="animType" format="integer" />
        <attr name="enableColor" format="color" />
        <attr name="disabledColor" format="color" />
        <attr name="strokeWidth" format="dimension" />
    </declare-styleable>

    <style name="Borderless">
        <item name="animType">0</item>
        <item name="android:background">@null</item>
        <item name="disabledColor">#EDEDED</item>
        <item name="enableColor">#EDEDED</item>
        <item name="strokeWidth">@dimen/dp1</item>
    </style>

    <style name="Fill">
        <item name="animType">1</item>
        <item name="android:background">@null</item>
        <item name="disabledColor">#EDEDED</item>
        <item name="enableColor">#E32E27</item>
        <item name="strokeWidth">@dimen/dp1</item>
    </style>
</resources>