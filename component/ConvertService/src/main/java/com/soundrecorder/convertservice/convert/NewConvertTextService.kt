/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  NewConvertTextService
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.convert

import android.app.Service
import android.content.Intent
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import com.soundrecorder.common.databean.ConvertStatus
import com.soundrecorder.base.utils.DebugUtil

class NewConvertTextService : IConvertProcess, Service() {

    companion object {
        const val TAG: String = "NewConvertTextService"
    }

    var mConvertTaskManager: ConvertTaskThreadManager? = null

    var callback: IJobManagerLifeCycleCallback? = object : IJobManagerLifeCycleCallback {
        override fun onFinalJobEnd(mediaId: Long) {
            DebugUtil.i(TAG, "onFinalJobEnd, stop Service")
            stopSelf()
        }
    }
    private var mMainHandler: Handler? = Handler(Looper.getMainLooper())

    override fun onCreate() {
        super.onCreate()
        mConvertTaskManager = ConvertTaskThreadManager
        ConvertTaskThreadManager.jobManagerLifeCycleCallback = callback
    }

    override fun onBind(intent: Intent?): IBinder {
        DebugUtil.i(TAG, "onBind")
        return NewConvertTextBinder(this)
    }

    override fun onUnbind(intent: Intent?): Boolean {
        DebugUtil.i(TAG, "onUnbind")
        if (ConvertTaskThreadManager.checkNoTaskRunning() != false) {
            DebugUtil.i(TAG, "no TASK running , stop Service")
            stopSelf()
        }
        return super.onUnbind(intent)
    }

    override fun startOrResumeConvert(mediaId: Long, convertAbilityType: Int, convertAiTitle: Boolean): Boolean {
        DebugUtil.i(TAG, "startConvert, mediaId:$mediaId")
        return ConvertTaskThreadManager.startOrResumeConvert(mediaId, convertAbilityType, convertAiTitle)
    }

    override fun cancelConvert(mediaId: Long): Boolean {
        DebugUtil.i(TAG, "cancelConvert")
        return ConvertTaskThreadManager.cancelConvert(mediaId) ?: false
    }

    override fun releaseConvert(mediaId: Long) {
        DebugUtil.i(TAG, "releaseConvert")
        ConvertTaskThreadManager.releaseConvert(mediaId)
    }

    override fun registerCallback(mediaId: Long, callback: IConvertCallback) {
        DebugUtil.i(TAG, "registerCallback mediaId: $mediaId")
        ConvertTaskThreadManager.registerCallback(mediaId, callback)
    }

    override fun unregisterCallback(mediaId: Long, callback: IConvertCallback) {
        DebugUtil.i(TAG, "unregisterCallback mediaId: $mediaId")
        ConvertTaskThreadManager.unregisterCallback(mediaId, callback)
    }

    fun checkConvertTaskRunning(mediaId: Long): Boolean {
        return ConvertTaskThreadManager.checkIsTaskRunning(mediaId) ?: false
    }

    fun checkCanAddNewTask(mediaId: Long): Int {
        return ConvertTaskThreadManager.checkCanAddNewTask(mediaId)
                ?: ConvertTaskThreadManager.DEFAULT_NULL
    }

    fun getConvertStatus(mediaId: Long): ConvertStatus? {
        return ConvertTaskThreadManager.getCurrentConvertStatus(mediaId)
    }

    override fun onDestroy() {
        DebugUtil.i(TAG, "onDestory")
        super.onDestroy()
        ConvertTaskThreadManager.releaseAll()
        mConvertTaskManager = null
        mMainHandler?.removeCallbacksAndMessages(null)
        mMainHandler = null
    }
}