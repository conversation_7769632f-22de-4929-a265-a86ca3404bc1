/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: ConvertRunnableFactory
 * Description:
 * Version: 1.0
 * Date: 2025/3/10
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/3/10 1.0 create
 */

package com.soundrecorder.convertservice.convert

import com.soundrecorder.convertservice.ConvertSupportManager
import com.soundrecorder.convertservice.convert.asr.AIConvertTextRunnable

object ConvertRunnableFactory {

    @JvmStatic
    fun getRunnable(mediaId: Long, convertAbilityType: Int, convertAiTitle: Boolean): IConvertTextRunnable? {
        return when (convertAbilityType) {
            ConvertSupportManager.CONVERT_AI_CONVERT -> AIConvertTextRunnable(mediaId, convertAiTitle)
            ConvertSupportManager.CONVERT_DOMESTIC_DEFAULT -> NewConvertTextRunnable(mediaId, convertAiTitle)
            else -> null
        }
    }
}