/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.api

import com.soundrecorder.convertservice.convert.ConvertTaskThreadManager
import com.soundrecorder.modulerouter.convertService.ConvertThreadManageAction

object ConvertThreadManageApi : ConvertThreadManageAction {

    override fun cancelAllTask() {
        ConvertTaskThreadManager.cancelAllTask()
    }

    override fun cancelConvert(mediaId: Long): Boolean {
        return ConvertTaskThreadManager.cancelConvert(mediaId)
    }

    override fun checkIsTaskRunning(mediaId: Long): Boolean {
        return ConvertTaskThreadManager.checkIsTaskRunning(mediaId)
    }

    override fun checkCanAddNewTask(mediaId: Long): Int {
        return ConvertTaskThreadManager.checkCanAddNewTask(mediaId)
    }

    override fun startOrResumeConvert(mediaId: Long, convertAbilityType: Int, convertAiTitle: Boolean): Boolean {
        return ConvertTaskThreadManager.startOrResumeConvert(mediaId, convertAbilityType, convertAiTitle)
    }
}