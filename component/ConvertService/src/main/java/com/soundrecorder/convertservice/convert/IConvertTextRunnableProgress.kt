/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  IConvertTestRunnableProgress
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.convert

interface IConvertTextRunnableProgress {

    fun preStartConvert(mediaId: Long)

    fun preCancelConvert(mediaId: Long)

    fun postConvertEnd(mediaId: Long, convertAiTitle: Boolean)

    fun postCancelConvert(mediaId: Long, convertAiTitle: <PERSON>olean)
}