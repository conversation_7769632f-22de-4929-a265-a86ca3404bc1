/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  SmartNameApi
 * * Description: SmartNameApi
 * * Version: 1.0
 * * Date : 2025/4/16
 * * Author: W9035969
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9035969    2025/4/16   1.0    build this module
 ****************************************************************/
package com.soundrecorder.convertservice.smartname

import android.content.Context
import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.OS12FeatureUtil
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.common.buryingpoint.SmartNameStatisticsUtil
import com.soundrecorder.common.databean.SmartNameParam
import com.soundrecorder.common.dialog.AIUnitDialog
import com.soundrecorder.convertservice.ConvertSupportManager
import com.soundrecorder.modulerouter.smartname.ISmartNameCallback
import com.soundrecorder.modulerouter.smartname.IUnifiedSummaryCallBack
import com.soundrecorder.modulerouter.smartname.SmartNameAction
import com.soundrecorder.translate.AIAsrManager

object SmartNameApi : SmartNameAction {

    private const val TAG = "SmartNameApi"

    override fun newUnifiedSummaryManager(): IUnifiedSummaryCallBack {
        return UnifiedSummaryManager()
    }

    /**
     * 是否支持智能标题能力
     * @param supportConvert 是否支持转文本
     * 若 supportConvert = null 则返回智能标题能力支持
     * 若 supportConvert ！=null 则返回智能标题能力+是否支持转文本；
     * 由于智能标题依赖转文本，若不支持转文本：则不支持智能标题
     *                      若支持转文本：则获取实际智能标题能力
     */
    override fun checkSupportSmartName(context: Context?, supportConvert: Boolean?, forceUpdate: Boolean): Boolean {
        if (context == null) {
            return false
        }
        if (BaseUtil.isRealme()) {
            return false
        }
        if (supportConvert == false) {
            //不支持转文本，智能命名也不能支持
            DebugUtil.d(TAG, "checkSupportSmartName convert_disable")
            return false
        }
        return OS12FeatureUtil.isColorOS16OrLater() && AIAsrManager.isSupportSmartName(context, forceUpdate)
    }

    override fun startSmartName(mediaId: Long): Boolean {
        return SmartNameTaskManager.startSmartName(mediaId, null)
    }

    override fun startSmartName(mediaId: Long, jsonParams: String?): Boolean {
        val params = GsonUtil.fromJson(jsonParams, SmartNameParam::class.java)
        return SmartNameTaskManager.startSmartName(mediaId, params)
    }

    override fun <T> startSmartNameByBean(mediaId: Long, param: T?): Boolean {
        return SmartNameTaskManager.startSmartName(mediaId, param as? SmartNameParam)
    }

    override fun checkHasTaskRunning(): Boolean {
        return SmartNameTaskManager.checkHasTaskRunning()
    }

    override fun checkIsTaskRunning(mediaId: Long): Boolean {
        return SmartNameTaskManager.checkIsTaskRunning(mediaId)
    }

    override fun registerCallback(mediaId: Long, callback: ISmartNameCallback) {
        SmartNameTaskManager.registerSmartNameCallback(mediaId = mediaId, callback)
    }

    override fun unRegisterCallback(mediaId: Long, callback: ISmartNameCallback) {
        SmartNameTaskManager.unregisterSmartNameCallback(mediaId = mediaId, callback)
    }

    override fun cancelSmartNameTask(mediaId: Long) {
        SmartNameTaskManager.cancelSmartName(mediaId)
    }

    override fun <T> fromJson(json: String?, cls: Class<T>): T? {
        return GsonUtil.fromJson(json, cls)
    }

    override fun setSmartNameSwitchStatus(mContext: Context, isOpen: Boolean, needStatistics: Boolean) {
        val switchValue = if (isOpen) {
            AIUnitDialog.SMART_NAME_SWITCH_OPEN
        } else {
            AIUnitDialog.SMART_NAME_SWITCH_CLOSE
        }
        PrefUtil.putInt(
            mContext,
            PrefUtil.KEY_SMART_NAME_SWITCH_OPEN,
            switchValue
        )
        if (needStatistics) {
            //只有在点击开启、插件下载完成时才进行埋点，后续插件是否下载的检测不进行埋点
            SmartNameStatisticsUtil.addSmartNameSwitchStateEvent(switchValue)
        }
    }

    override fun isSmartNameSwitchOpen(mContext: Context): Boolean {
        val switchValue = smartNameSwitchValue(mContext)
        DebugUtil.d(TAG, "isSmartNameSwitchOpen, switchValue:$switchValue")
        return OS12FeatureUtil.isColorOS16OrLater() && (switchValue == AIUnitDialog.SMART_NAME_SWITCH_OPEN)
    }

    override fun smartNameSwitchValue(mContext: Context): Int {
        return PrefUtil.getInt(mContext, PrefUtil.KEY_SMART_NAME_SWITCH_OPEN, AIUnitDialog.SMART_NAME_SWITCH_INVALID)
    }

    override fun needShowSmartGuideDialog(mContext: Context): Boolean {
        val needShow = !PrefUtil.getBoolean(mContext, PrefUtil.KEY_SMART_NAME_GUIDE_SHOW, false)
        if (!needShow) {
            return false
        }
        /*判断是否支持智能标题含转文本*/
        return checkSupportSmartName(mContext, ConvertSupportManager.isSupportConvert())
    }

    override fun clearSmartRetryCacheTask() {
        SmartRetryCacheTask.clear()
    }

    override fun checkTaskRunningMaxLimitSize(): Boolean {
        return SmartNameTaskManager.checkTaskRunningMaxLimitSize()
    }

    override fun releaseAllTask() {
        SmartNameTaskManager.releaseAll()
    }
}