package com.soundrecorder.convertservice.security;

import android.os.Build;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import com.soundrecorder.convertservice.shadows.ShadowFeatureOption;
import com.soundrecorder.convertservice.shadows.ShadowOS12FeatureUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;
import java.security.NoSuchAlgorithmException;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class,  ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class AesUtilsTest {

    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/CTR/NoPadding";
    private static final String IV_CONNECT = "%IV1%";
    private static final String TEST_CONTENT = "TEST_CONTENT";
    private static final int KEY_BYTE_SIZE = 256;

    @Test
    public void should_not_null_key_when_genKey() throws NoSuchAlgorithmException {
        String actualKey = AesUtils.genKey();
        Assert.assertNotNull(actualKey);
    }

    @Test
    public void should_not_null_when_encrypt() throws Exception {
        String encryptedResult = AesUtils.encrypt(AesUtils.genKey() ,TEST_CONTENT);
        Assert.assertNotNull(encryptedResult);
    }
}
