/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  SeedingInterface.kt
 * * Description : SeedingInterface
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.modulerouter

import android.content.Context
import org.json.JSONObject

interface SeedingInterface {
    fun init()
    fun doActionInThread(callback: () -> Unit)
    fun getStatusBarSupportType(context: Context, callback: (Int) -> Unit)
    fun initSupportFluidCardCallback(context: Context, callback: (Boolean?, Boolean) -> Unit)
    fun isSupportFluidCloud(context: Context): Boolean
    fun isSupportSystemSendIntent(context: Context): Boolean
    fun sendShowSeedlingStatusBar(originData: JSONObject? = null, callback: ((Boolean) -> Unit)? = null)
    fun sendHideSeedlingStatusBar(forceDismiss: Boolean = false, callback: ((Boolean) -> Unit)? = null)
    fun sendUpdateCardData(card: Any, jsonData: JSONObject, updateAll: Boolean)
    fun registerResultCallBack()
    fun unRegisterResultCallBack()
    fun refreshSeedlingData(jsonData: JSONObject?)
    fun getCardServiceId(): String
    fun release()
    fun sendRecordDeleteEvent()
    fun sendRecordAddEvent()
    fun sendRecordFileInnerRename(mediaId: Long)
    fun sendRecordRecoverEvent()
    fun onConvertProgressChanged(mediaId: Long, uploadProgress: Int, convertProgress: Int, serverPlanCode: Int)
    fun onConvertStatusChange(mediaId: Long, uploadStatus: Int, convertStatus: Int, errorMessage: String)
    fun onConvertEnd(mediaId: Long)
    fun onSummaryStart(mediaId: Long?)
    fun onSummaryProgressEnd(mediaId: Long?, noteId: String?, recordUUID: String?, asrErrorCode: Int?, summaryErrorCode: Int?)
    fun hasReleased(): Boolean
    fun forceRefreshSeedlingData(
        seedlingCard: Any? = null,
        jsonData: JSONObject? = null,
        updateAll: Boolean = true
    )
}

/*
* 13.1老胶囊
*/
const val STATUS_BAR_SUPPORT_OLD = 0

/*
* 13.2余光交互
*/
const val STATUS_BAR_SUPPORT_SEEDLING_CARD = 1

/*
* 14.0流体云
*/
const val STATUS_BAR_SUPPORT_FLUID_CARD = 2