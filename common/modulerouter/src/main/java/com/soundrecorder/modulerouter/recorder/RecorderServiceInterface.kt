/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  RecorderServiceInterface.kt
 * * Description : RecorderServiceInterface
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.modulerouter.recorder

import android.content.Intent
import android.net.Uri
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.soundrecorder.modulerouter.convertService.listener.RecorderControllerListener
import org.json.JSONObject

interface RecorderServiceInterface {
    var saveFileState: Int
    fun startRecorderService(function: Intent.() -> Unit)
    fun <T> getMarkData(): List<T>
    fun getLastMarkTime(): Long
    fun getRecordType(): Int
    fun getAmplitudeList(): List<Int>
    fun getLatestAmplitude(): Int
    fun getMaxAmplitude(): Int
    fun getMarkEnabledLiveData(): LiveData<Boolean>?
    fun isMarkEnabledFull(): Boolean
    fun checkMarkDataMoreThanMax(): Boolean
    fun isNeedShowNotificationPermissionDeniedSnackBar(): Boolean
    fun resetNeedShowNotificationPermissionDeniedSnackBar()
    fun start()
    fun resume()
    fun pause()
    fun cancel()
    fun stop(): String?
    fun cancelRecordNotification()
    fun getSampleUri(): Uri?
    fun getSuffix(): String?
    fun getRecordFilePath(): String?
    fun getRelativePath(): String?
    fun getSampleDisplayName(genNewNameWhenSampleNull: Boolean = true): String
    fun getRecordModeName(): String
    fun getFileBeingRecorded(): String?
    fun isQuickRecord(): Boolean
    fun saveRecordInfo(displayName: String? = null, originalDisplayName: String? = null, saveRecordFromWhere: Int, needStopService: Boolean = true)
    fun hasInitRecorderService(): Boolean
    fun stopService()
    fun switchRecorderStatus(from: String)
    fun <T> addMark(mark: T)
    fun <T> addMultiPictureMark(marks: ArrayList<T>): Int
    fun removeMark(index: Int)
    fun renameMark(newText: String, index: Int): Boolean
    fun isFromSlidBar(): Boolean
    fun isFromAppCard(): Boolean
    fun isFromMiniApp(): Boolean
    fun isFromSmallCard(): Boolean
    fun isFromOtherApp(): Boolean
    fun isFromBreno(): Boolean
    fun addSourceForNotificationBtnDisabled(addPictureMarking: MutableLiveData<Boolean>)
    fun addListener(listener: RecorderControllerListener)
    fun removeListener(listener: RecorderControllerListener)
    fun setDoMultiPictureMarkLoading(doMultiPictureMarkLoading: Boolean)
    fun isStartServiceFromOtherApp(): Boolean
    fun getCurrentStatus(): Int
    fun getRecordStatusBeforeSaving(): Int?
    fun getLastStatus(): Int
    fun isAlreadyRecording(): Boolean
    fun isRecordSaving(): Boolean
    fun checkModeCanRecord(needToast: Boolean): Boolean
    fun isAudioModeChangePause(): Boolean
    fun isNeedResume(): Boolean
    fun checkDistBeforeStartRecord(): Boolean
    fun hasInitAmplitude(): Boolean
    fun getAmplitudeCurrentTime(): Long
    fun forceHideRecordStatusBar(from: String)
    fun getSeedlingData(): JSONObject?
    fun showOrHideStatusBar(from: String)
    fun onSeedlingCardStateChanged(isShow: Boolean)
    fun fluidCardDismiss(from: String)
    fun getSaveProgressValue(): Int
    fun getDirectRecordOn(): Boolean
    fun getSampleUriStr(): String
    fun setDirectRecordSwitch(isOn: Boolean)
    fun getDirectRecordEnable(): Boolean
    fun setDirectRecordTime(directTime: String)
    fun setLastDirectRecordOnTime(time: Long)
    fun getLastDirectRecordOnTime(): Long
    fun isFromCubeButtonOrLockScreen(): Boolean
    fun changeAsrLanguage(language: String)
    fun registerRtAsrListener(listener: Any)
    fun unregisterRtAsrListener(listener: Any)
    fun startTranslationConfig()
    fun getAllAsrContent(): List<*>
    fun getRealtimeAsrStatus(): Any
    fun externalInitAsr()
    fun getCurSelectedLanguage(): String
    fun setCurSelectedLanguage(language: String)
    fun getSupportLanguageList(callback: IGetSupportLanguage)
    fun getLangListFromCatchOrSp(): List<String>?
    fun externalStopAsr()
    fun updateSpeakerName(roleId: Int, newName: String): Boolean
    fun getRealtimeSubtitleCache(): Any?
    fun isRealTimeSwitch(): Boolean
    fun setRealTimeSwitch(mRealTimeSwitch: Boolean)
    fun getRealTimeSubtitleBuryingPointCallback(callback: IGetBuryingPointRealTimeSubtitleInstance)
    fun getSpeechLanguage(): String
    fun setSpeechLanguage(language: String)
}


const val FROM_SWITCH_RECORD_STATUS_SMALL_CARD = "record_status_small_card"
const val FROM_SWITCH_RECORD_STATUS_MINI = "record_status_mini"
const val FROM_SWITCH_RECORD_STATUS_APP_CARD = "record_status_app_card"