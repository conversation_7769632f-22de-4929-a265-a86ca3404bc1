/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: IAsrPluginCallBack
 * Description: IAsrPluginCallBack
 * Version: 1.0
 * Date: 2025/6/6
 * Author: W9017232
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9017232                         2025/6/6      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.modulerouter.translate

import android.content.Context
import com.soundrecorder.modulerouter.smartname.IPluginDownloadCallback

interface IAsrPluginCallBack {
    fun showAIAsrPluginsDialog(context: Context, callback: IPluginDownloadCallback? = null)

    fun isAsrPluginSupportAndDownload(context: Context): Boolean
}