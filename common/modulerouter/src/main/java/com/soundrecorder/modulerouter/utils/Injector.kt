/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : IAdvertManager
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/06/05 10:29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  keweiwei        2024/06/05       1.0      create
 ***********************************************************************/
package com.soundrecorder.modulerouter.utils

import android.util.Log
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import org.koin.core.parameter.parametersOf
import org.koin.core.qualifier.named

object Injector : KoinComponent {
    const val TAG = "Injector"

    @Suppress("TooGenericExceptionCaught")
    inline fun <reified T> injectFactory(): T? = runCatching {
        val instance: T by inject()
        instance
    }.onFailure {
        Log.e(TAG, "inject has error:${it.message}")
    }.getOrNull()

    @Suppress("TooGenericExceptionCaught")
    inline fun <reified T> injectFactory(vararg parameters: Any): T? = runCatching {
        val instance: T by inject { parametersOf(*parameters) }
        instance
    }.onFailure {
        Log.e(TAG, "inject has error:${it.message}")
    }.getOrNull()

    @Suppress("TooGenericExceptionCaught")
    inline fun <reified T> factoryByName(key: String): T? = runCatching {
        Log.d(TAG, "inject: factoryByName key$key")
        val obj: T by inject(named(name = key))
        obj
    }.onFailure {
        Log.e(TAG, "factory instance by name key:$key has error:${it.message}")
    }.getOrNull()
}