/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  HomeInterface.kt
 * * Description : HomeInterface
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.modulerouter

import android.content.Context

interface HomeInterface {
    fun isTransparentActivity(context: Context?): Boolean
    fun getTransParentActivityClass(): Class<*>?
}