/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: ViewUtilsTest
 Description:
 Version: 1.0
 Date: 2022/7/28
 Author: W9013333(v-z<PERSON><PERSON><PERSON><PERSON><PERSON>@oppo.com)
 --------------------Revision History: ---------------------
 <author> <date> <version> <desc>
 W9013333 2022/7/28 1.0 create
 */
package com.soundrecorder.common.utils

import android.animation.ValueAnimator
import android.app.Activity
import android.content.Context
import android.graphics.Rect
import android.os.Build
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.animation.PathInterpolator
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.oplus.anim.EffectiveAnimationView
import com.soundrecorder.common.R
import com.soundrecorder.common.shadows.ShadowFeatureOption
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.common.utils.ViewUtils.addItemDecoration
import com.soundrecorder.common.utils.ViewUtils.addItemDecorationBottom
import com.soundrecorder.common.utils.ViewUtils.changeScaleXY
import com.soundrecorder.common.utils.ViewUtils.changeTransitionY
import com.soundrecorder.common.utils.ViewUtils.findAnchor
import com.soundrecorder.common.utils.ViewUtils.fixTextFlash
import com.soundrecorder.common.utils.ViewUtils.getUnDisplayViewSize
import com.soundrecorder.common.utils.ViewUtils.isOutOfBounds
import com.soundrecorder.common.utils.ViewUtils.onRelease
import com.soundrecorder.common.utils.ViewUtils.setAnimatePressBackground
import com.soundrecorder.common.utils.ViewUtils.updateConstraintHeight
import com.soundrecorder.common.utils.ViewUtils.updateConstraintPercentWidth
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.any
import org.mockito.ArgumentMatchers.anyInt
import org.mockito.Mockito
import org.mockito.Mockito.mock
import org.robolectric.Robolectric
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class ViewUtilsTest {

    private var context: Context? = null
    private var mActivity: Activity? = null

    @Before
    fun init() {
        context = ApplicationProvider.getApplicationContext()
        mActivity = Robolectric.buildActivity(Activity::class.java).get()
    }

    @After
    fun clear() {
        context = null
        mActivity = null
    }

    @Test
    fun check_addItemDecoration() {
        val ctx = context ?: return
        val recyclerView = mock(RecyclerView::class.java)
        Mockito.`when`(recyclerView.resources).thenReturn(ctx.resources)
        Mockito.`when`(recyclerView.setTag(anyInt(), any())).thenCallRealMethod()
        Mockito.`when`(recyclerView.getTag(anyInt())).thenCallRealMethod()
        recyclerView.addItemDecoration(R.dimen.dp10)
        val itemDecoration =
            recyclerView.getTag(R.id.tag_recyclerview_add_item_decoration) as RecyclerView.ItemDecoration
        Assert.assertNotNull(itemDecoration)
        val outRect = Rect()
        val v1 = View(context)
        Mockito.`when`(recyclerView.getChildAdapterPosition(v1)).thenReturn(0)
        itemDecoration.getItemOffsets(outRect, v1, recyclerView, RecyclerView.State())
        Assert.assertEquals(outRect.top, 0)
        val v2 = View(context)
        Mockito.`when`(recyclerView.getChildAdapterPosition(v2)).thenReturn(1)
        itemDecoration.getItemOffsets(outRect, v2, recyclerView, RecyclerView.State())
        Assert.assertNotEquals(outRect.top, 0)
    }

    @Test
    fun check_setAnimatePressBackground() {
        val ctx = context ?: return
        val key = R.id.tag_animate_press
        val view = View(ctx)
        val bgColorAnimator = mock(ValueAnimator::class.java)
        val interpolator = PathInterpolator(0.33f, 0f, 0.67f, 1f)
        Mockito.`when`(bgColorAnimator.duration).thenReturn(200)
        Mockito.`when`(bgColorAnimator.interpolator).thenReturn(interpolator)
        view.setTag(key, bgColorAnimator)
        view.setAnimatePressBackground()
        Assert.assertEquals(view.getTag(key), bgColorAnimator)
        Assert.assertTrue(view.background == null)
    }

    @Test
    fun check_getUnDisplayViewSize() {
        val ctx = context ?: return
        val view = View(ctx)
        val size = view.getUnDisplayViewSize()
        Assert.assertTrue(size != null)
    }

    @Test
    fun check_onRelease() {
        val imageView = mock(EffectiveAnimationView::class.java)
        Mockito.`when`(imageView.isAnimating).thenReturn(true)
        imageView.onRelease()
        Assert.assertTrue(imageView.isAnimating)
    }

    @Test
    fun check_isOutOfBounds() {
        val ctx = context ?: return
        val event = mock(MotionEvent::class.java)
        Mockito.`when`(event.x).thenReturn(100F)
        Mockito.`when`(event.y).thenReturn(100F)
        val view = View(ctx)
        val isOut = view.isOutOfBounds(event)
        Assert.assertTrue(isOut)
    }

    @Test
    fun check_findAnchor() {
        val ctx = context ?: return
        val recyclerView = mock(RecyclerView::class.java)
        val layoutManager = mock(LinearLayoutManager::class.java)
        recyclerView.layoutManager = layoutManager
        val v1 = View(ctx)
        Mockito.`when`(recyclerView.getChildAdapterPosition(v1)).thenReturn(0)
        val v2 = View(ctx)
        Mockito.`when`(recyclerView.getChildAdapterPosition(v2)).thenReturn(1)
        Mockito.`when`(layoutManager.findFirstVisibleItemPosition()).thenReturn(-1)
        val view1 = recyclerView?.findAnchor()
        Assert.assertTrue(view1 == null)
        Mockito.`when`(layoutManager.findLastVisibleItemPosition()).thenReturn(-1)
        val view2 = recyclerView?.findAnchor()
        Assert.assertTrue(view2 == null)
        Mockito.`when`(layoutManager.findLastVisibleItemPosition()).thenReturn(-4)
        val view3 = recyclerView?.findAnchor()
        Assert.assertTrue(view3 == null)
        Mockito.`when`(layoutManager.findFirstVisibleItemPosition()).thenReturn(0)
        Mockito.`when`(layoutManager.findLastVisibleItemPosition()).thenReturn(0)
        val view4 = recyclerView?.findAnchor()
        Assert.assertTrue(view4 == layoutManager.findViewByPosition(0))
        Mockito.`when`(layoutManager.findFirstVisibleItemPosition()).thenReturn(0)
        Mockito.`when`(layoutManager.findLastVisibleItemPosition()).thenReturn(1)
        Mockito.`when`(layoutManager.findFirstCompletelyVisibleItemPosition()).thenReturn(0)
        val view5 = recyclerView?.findAnchor()
        Assert.assertTrue(view5 == layoutManager.findViewByPosition(0))
        Mockito.`when`(layoutManager.findFirstCompletelyVisibleItemPosition()).thenReturn(-2)
        val view6 = recyclerView?.findAnchor()
        Assert.assertTrue(view6 == layoutManager.findViewByPosition(1))

        Mockito.`when`(layoutManager.findFirstVisibleItemPosition()).thenReturn(0)
        Mockito.`when`(layoutManager.findLastVisibleItemPosition()).thenReturn(2)
        Mockito.`when`(layoutManager.findFirstCompletelyVisibleItemPosition()).thenReturn(1)
        val view7 = recyclerView?.findAnchor()
        Assert.assertTrue(view7 == layoutManager.findViewByPosition(1))
    }

    @Test
    fun check_fixTextFlash() {
        val ctx = context ?: return
        val textView = TextView(ctx)
        textView.width = -1
        textView.fixTextFlash("单元测试")
        Assert.assertTrue(textView.getTag(R.id.tag_textview_by_layout_change) == textView)
        textView.width = 100
        textView.fixTextFlash("单元测试")
    }

    @Test
    fun check_resetAnimator() {
        val ctx = context ?: return
        val view = View(ctx)
        ViewUtils.resetAnimator(view)
        Assert.assertTrue(view.alpha == 1f)
        Assert.assertTrue(view.scaleY == 1f)
        Assert.assertTrue(view.scaleX == 1f)
    }

    @Test
    fun should_correct_when_dp2px() {
        val value = ViewUtils.dp2px(1f, true)
        Assert.assertNotEquals(0, value)

        val valueNotFollow = ViewUtils.dp2px(1f, true)
        Assert.assertNotEquals(0, valueNotFollow)
    }

    @Test
    fun should_correct_when_addItemDecorationBottom() {
        val ctx = context ?: return
        val view = RecyclerView(ctx)
        view.setTag(R.id.tag_recyclerview_add_item_decoration, 1)
        view.addItemDecorationBottom(-1)
        Assert.assertEquals(0, view.itemDecorationCount)

        view.setTag(R.id.tag_recyclerview_add_item_decoration, null)
        view.addItemDecorationBottom(R.dimen.divider_background_height)
        Assert.assertEquals(1, view.itemDecorationCount)
    }

    @Test
    fun should_correct_when_updateConstraintHeight() {
        val ctx = context ?: return
        val view = View(ctx)
        view.updateConstraintHeight(1)
        Assert.assertNotEquals(1, view.height)

        val parentView = ConstraintLayout(ctx)
        parentView.addView(view)
        view.updateConstraintHeight(10)
    }

    @Test
    fun should_correct_when_updateConstraintPercentWidth() {
        val ctx = context ?: return
        val view = View(ctx)
        view.updateConstraintPercentWidth(0.5f)
        Assert.assertNotEquals(0.5f, view.height)

        val parentView = ConstraintLayout(ctx)
        parentView.addView(view)
        view.updateConstraintPercentWidth(0.6f)
        Assert.assertEquals(
            0.6f,
            (view.layoutParams as? ConstraintLayout.LayoutParams)?.matchConstraintPercentWidth
        )
    }

    @Test
    fun should_correct_when_changeScaleXY() {
        val ctx = context ?: return
        val view = View(ctx)
        view.changeScaleXY(1f)
        Assert.assertEquals(1f, view.scaleX)

        view.changeScaleXY(0.3f)
        Assert.assertEquals(0.3f, view.scaleX)
    }

    @Test
    fun should_correct_when_changeTransitionY() {
        val ctx = context ?: return
        val view = View(ctx)
        view.changeTransitionY(1f)
        Assert.assertEquals(1f, view.translationY)

        view.changeTransitionY(0.3f)
        Assert.assertEquals(0.3f, view.translationY)
    }
}