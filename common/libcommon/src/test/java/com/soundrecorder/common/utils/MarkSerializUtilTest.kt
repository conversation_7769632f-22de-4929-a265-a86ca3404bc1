package com.soundrecorder.common.utils

import android.os.Build
import android.text.TextUtils
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.shadows.ShadowFeatureOption
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog
import org.junit.Assert.*

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowLog::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class MarkSerializUtilTest {

    companion object {
        val BEAN_ZERO = MarkDataBean(0L).apply { defaultNo = 0 }
        val BEAN_ONE = MarkDataBean(1000L).apply { defaultNo = 1 }
        val BEAN_TWO = MarkDataBean(2000L).apply { defaultNo = 2 }
        val BEAN_THREE = MarkDataBean(3000L).apply { defaultNo = 3 }
        val BEAN_FOUR = MarkDataBean(4000L).apply { defaultNo = 4 }
        val OLD_BEAN_ONE = MarkDataBean(10000L, MarkSerializUtil.VERSION_OLD).apply { defaultNo = 5 }
        val OLD_BEAN_TWO = MarkDataBean(20000L, MarkSerializUtil.VERSION_OLD).apply { defaultNo = 6 }
        val BEAN_PICTURE = MarkDataBean(10000L, MarkSerializUtil.VERSION_PICTURE).apply { defaultNo = 7 }
    }

    @Before
    fun setUp() {
    }

    @After
    fun tearDown() {
    }

    @Test
    fun parseMarkDataBeanListFromString() {
        var markDataBeanList = mutableListOf(BEAN_ONE, BEAN_TWO)
        var convertedString = MarkSerializUtil.convertStoredDBStringForMarkDataBeanList(markDataBeanList)
        var result = MarkSerializUtil.parseMarkDataBeanListFromString(convertedString)
        assertNotNull(result)
        assertEquals(mutableListOf(BEAN_ONE, BEAN_TWO), result)
        markDataBeanList = mutableListOf(OLD_BEAN_ONE, OLD_BEAN_TWO)
        convertedString = MarkSerializUtil.convertStoredDBStringForMarkDataBeanList(markDataBeanList)
        result = MarkSerializUtil.parseMarkDataBeanListFromString(convertedString)
        assertNotNull(result)
        assertEquals(0, result.size)
    }

    @Test
    fun should_not_null_when_convertStoredDBStringForMarkDataBeanList_different_inputs() {
        var result = MarkSerializUtil.convertStoredDBStringForMarkDataBeanList(null)
        assertEquals("", result)
        result = MarkSerializUtil.convertStoredDBStringForMarkDataBeanList(mutableListOf(
            BEAN_PICTURE
        ))
        assertEquals("", result)
        val markDataBeanList = mutableListOf(BEAN_ONE, BEAN_TWO, OLD_BEAN_ONE, OLD_BEAN_TWO)
        result = MarkSerializUtil.convertStoredDBStringForMarkDataBeanList(markDataBeanList)
        val expected = "${BEAN_ONE.toStoreString()}\uFFF0${BEAN_TWO.toStoreString()}\uFFF010000,20000"
        assertEquals(expected, result)
    }

    @Test
    fun should_correct_when_checkInTimeScopeInMarkList_different_inputs() {
        val markDataBeanList = mutableListOf(BEAN_ZERO, BEAN_ONE, BEAN_TWO, BEAN_THREE, BEAN_FOUR)
        val currentTimeMills = 3099L
        var result = MarkSerializUtil.checkInTimeScopeInMarkList(mutableListOf(), currentTimeMills)
        assertEquals(-1, result)
        result = MarkSerializUtil.checkInTimeScopeInMarkList(null, currentTimeMills)
        assertEquals(-1, result)
        result = MarkSerializUtil.checkInTimeScopeInMarkList(markDataBeanList, currentTimeMills)
        assertEquals(3, result)
    }

    @Test
    fun should_returnNotNull_getMarkString_withNormal() {
        assertTrue(TextUtils.isEmpty(MarkSerializUtil.getMarkString(null)))
        val marks: MutableList<String> = ArrayList()
        marks.add("test mark1")
        marks.add("test mark2")
        assertNotNull(MarkSerializUtil.getMarkString(marks))
    }
}