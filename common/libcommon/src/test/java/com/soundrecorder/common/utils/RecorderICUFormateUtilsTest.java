package com.soundrecorder.common.utils;

import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.base.utils.RecorderICUFormateUtils;
import com.soundrecorder.common.shadows.ShadowFeatureOption;
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.common.shadows.ShadowOplusUsbEnvironment;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

import java.util.Date;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOplusUsbEnvironment.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class RecorderICUFormateUtilsTest {

    @Before
    public void setUp() {
    }

    @Test
    public void should_notNull_when_formatDate() {
        Date date = new Date();
        String fullString = null;
        fullString = RecorderICUFormateUtils.formatDate(date, RecorderICUFormateUtils.DateAndTimeFormatType.FULL);
        Assert.assertNotNull(fullString);
        String longString = null;
        longString = RecorderICUFormateUtils.formatDate(date, RecorderICUFormateUtils.DateAndTimeFormatType.LONG);
        Assert.assertNotNull(longString);
        String mediumString = null;
        mediumString = RecorderICUFormateUtils.formatDate(date, RecorderICUFormateUtils.DateAndTimeFormatType.MEDIUM);
        Assert.assertNotNull(mediumString);
        String shortString = null;
        shortString = RecorderICUFormateUtils.formatDate(date, RecorderICUFormateUtils.DateAndTimeFormatType.SHORT);
        Assert.assertNotNull(shortString);

        Assert.assertTrue(fullString.length() > mediumString.length());
        Assert.assertTrue(mediumString.length() > shortString.length());
    }

    @Test
    public void should_notNull_when_formatTime() {
        Date date = new Date();
        String fullString = null;
        fullString = RecorderICUFormateUtils.formatTime(date, RecorderICUFormateUtils.DateAndTimeFormatType.FULL);
        Assert.assertNotNull(fullString);
        String longString = null;
        longString = RecorderICUFormateUtils.formatTime(date, RecorderICUFormateUtils.DateAndTimeFormatType.LONG);
        Assert.assertNotNull(longString);
        String mediumString = null;
        mediumString = RecorderICUFormateUtils.formatTime(date, RecorderICUFormateUtils.DateAndTimeFormatType.MEDIUM);
        Assert.assertNotNull(mediumString);
        String shortString = null;
        shortString = RecorderICUFormateUtils.formatTime(date, RecorderICUFormateUtils.DateAndTimeFormatType.SHORT);
        Assert.assertNotNull(shortString);

        Assert.assertTrue(fullString.length() > mediumString.length());
        Assert.assertTrue(mediumString.length() > shortString.length());
    }

    @Test
    public void should_notNull_when_formatDateTime() {
        Date date = new Date();
        String dateTimeString = null;
        dateTimeString = RecorderICUFormateUtils.formatDateTime(date);
        Assert.assertNotNull(dateTimeString);
    }

    @After
    public void tearDown() {
    }
}