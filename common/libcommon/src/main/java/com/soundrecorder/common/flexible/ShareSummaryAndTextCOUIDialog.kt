/*
Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
File: - ShareSummaryAndTextPreference
Description: 录音分享摘要分享原文弹窗工具类
Version: 1.0
Date : 2025/6/3
Author: ********
--------------------- Revision History: ---------------------
<author>	<data> 	  <version >	   <desc>
********  2025/6/3     1.0      create this file
*/
package com.soundrecorder.common.flexible

import android.content.DialogInterface
import android.os.Bundle
import androidx.fragment.app.FragmentManager
import com.soundrecorder.base.utils.DebugUtil

class ShareSummaryAndTextCOUIDialog(val childFragmentManager: FragmentManager?) {
    companion object {
        private const val TAG = "ShareRecordCOUIDialog"
        const val SHARE_TEXT = "share_text"
        const val SELECT_TYPE = "select_type"
    }

    private var bottomSheetDialogFragment: ShareBottomSheetDialogFragment? = null

    fun showShareSummaryAndTextDialog(isShareText: <PERSON>olean, selectShareType: Int, callBack: (type: Int) -> Unit) {
        if (isShowing()) {
            DebugUtil.i(TAG, "bottomSheetDialogFragment is showing")
            return
        }
        bottomSheetDialogFragment = ShareBottomSheetDialogFragment()
        val shareRecordDialogFragment = ShareSummaryDialogFragment().apply {
            arguments = Bundle().apply {
                putBoolean(SHARE_TEXT, isShareText)
            }
        }
        bottomSheetDialogFragment?.setMainPanelFragment(shareRecordDialogFragment)
        childFragmentManager?.let {
            val fragment = it.findFragmentByTag(ShareSummaryDialogFragment.TAG)
            if (fragment != null) {
                it.beginTransaction().remove(fragment).commit()
            }
            bottomSheetDialogFragment?.show(it, ShareSummaryDialogFragment.TAG)
        }
        shareRecordDialogFragment.setShareSummaryCallBack(isShareText, selectShareType) { type ->
            callBack.invoke(type)
        }
    }

    fun isShowing(): Boolean {
        return bottomSheetDialogFragment?.dialog?.isShowing == true
    }

    fun isShareSummary(): Boolean {
        return bottomSheetDialogFragment?.shareRecordDialogFragment?.arguments?.getBoolean(SHARE_TEXT) == false
    }

    fun shareType(): Int {
        return bottomSheetDialogFragment?.shareRecordDialogFragment?.mPreferenceDialog?.type ?: -1
    }

    fun dismiss() {
        bottomSheetDialogFragment?.dismiss()
        bottomSheetDialogFragment = null
    }

    fun setOnDismissListener(listener: DialogInterface.OnDismissListener) {
        DebugUtil.i(TAG, "bottomSheetDialogFragment setOnDismissListener")
        bottomSheetDialogFragment?.setOnDismissListener {
            listener
        }
    }
}