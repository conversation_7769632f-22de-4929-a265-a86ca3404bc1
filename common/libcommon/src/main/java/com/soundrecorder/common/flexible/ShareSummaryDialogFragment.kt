/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConvertTaskAction.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/5/29
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.flexible

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.preference.Preference
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.coui.appcompat.preference.COUIJumpPreference
import com.coui.appcompat.preference.COUIPreferenceFragment
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.R
import com.soundrecorder.common.share.ShareSupportHelper

class ShareSummaryDialogFragment : COUIPanelFragment() {
    companion object {
        const val TAG = "ShareSummaryDialogFragment"
    }
    var mPreferenceDialog = ShareSummaryDialogPreferenceFragment()
    private var mShareSummaryCallBack: ((Int) -> Unit)? = null
    private var isShareText = false
    private var shareSelectType: Int = 0

    override fun initView(panelView: View?) {
        super.initView(panelView)
        initObserves()
        initToolbar()
        initPreference()
        initDialogContent(panelView)
    }

    private fun initObserves() {
        mPreferenceDialog.setCallBack(isShareText = isShareText, shareSelectType) { type ->
            mShareSummaryCallBack?.invoke(type)
        }
    }

    @SuppressLint("CommitTransaction")
    private fun initPreference() {
        childFragmentManager.beginTransaction().replace(contentResId, mPreferenceDialog).commit()
    }

    private fun initDialogContent(panelView: View?) {
        hideDragView()
    }

    private fun initToolbar() {
        val toolbarTitle = if (isShareText) {
            resources.getString(com.soundrecorder.common.R.string.share_asr)
        } else {
            resources.getString(com.soundrecorder.common.R.string.share_summary)
        }
        toolbar = toolbar?.apply {
            title = toolbarTitle
            isTitleCenterStyle = false
            setNavigationIcon(com.support.appcompat.R.drawable.coui_back_arrow)
        }
        toolbar.setNavigationOnClickListener {
            dismissDialog()
        }
    }

    override fun onShow(isShowOnFirstPanel: Boolean?) {
        super.onShow(isShowOnFirstPanel)
        //设置dialogFragment的背景
        ((parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog)?.setPanelBackgroundTintColor(
            COUIContextUtil.getAttrColor(
                context, com.support.appcompat.R.attr.couiColorSurfaceWithCard
            )
        )
    }

    private fun dismissDialog() {
        (parentFragment as? COUIBottomSheetDialogFragment)?.dismiss()
    }

    fun setShareSummaryCallBack(isShareText: Boolean, selectShareType: Int, callBack: (Int) -> Unit) {
        this.isShareText = isShareText
        this.shareSelectType = selectShareType
        mShareSummaryCallBack = callBack
    }

    override fun onDestroy() {
        super.onDestroy()
        if (mShareSummaryCallBack != null) {
            mShareSummaryCallBack = null
        }
    }
}

class ShareSummaryDialogPreferenceFragment : COUIPreferenceFragment() {
    companion object {
        private const val TAG = "ShareSummaryDialogPreferenceFragment"
        private const val SHARE_SUMMARY_WORD_TYPE = "share_summary_word_type"
        private const val SHARE_SUMMARY_TXT_TYPE = "share_summary_txt_type"
        private const val SHARE_SUMMARY_PDF_TYPE = "share_summary_pdf_type"
        private const val SHARE_SUMMARY_BUTTON = "share_summary_dialog_button"
    }

    var type: Int = ShareSupportHelper.SHARE_TYPE_TEXT_TO_DOC
    private var mShareSummaryWord: COUIJumpPreference? = null
    private var mShareSummaryTxt: COUIJumpPreference? = null
    private var mShareSummaryPdf: COUIJumpPreference? = null
    private var mShareSummaryButton: ShareSummaryAndTextPreference? = null
    private val mShareSummaryList = mutableListOf<COUIJumpPreference?>()
    private var isShareText = false
    private var shareSelectType: Int = 0
    private var mCallBack: ((Int) -> Unit)? = null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val onCreateView = super.onCreateView(inflater, container, savedInstanceState)
        //去掉原本的toolbar
        onCreateView?.let {
            (it as? ViewGroup)?.apply {
                removeView(it.findViewById(com.support.preference.R.id.appbar_layout))
            }
        }
        return onCreateView
    }

    override fun onCreatePreferences(savedInstanceState: Bundle?, rootKey: String?) {
        addPreferencesFromResource(R.xml.share_summary_dialog_preference)
        mShareSummaryWord = findPreference(SHARE_SUMMARY_WORD_TYPE)
        mShareSummaryTxt = findPreference(SHARE_SUMMARY_TXT_TYPE)
        mShareSummaryPdf = findPreference(SHARE_SUMMARY_PDF_TYPE)
        mShareSummaryButton = findPreference(SHARE_SUMMARY_BUTTON)
        mShareSummaryList.clear()
        mShareSummaryList.add(mShareSummaryWord)
        mShareSummaryList.add(mShareSummaryTxt)
        mShareSummaryList.add(mShareSummaryPdf)

        mShareSummaryWord?.title = String.format(
            resources.getString(R.string.share_by),
            resources.getString(R.string.word)
        )
        mShareSummaryTxt?.title =
            String.format(resources.getString(R.string.share_by), resources.getString(R.string.txt))
        mShareSummaryPdf?.title =
            String.format(resources.getString(R.string.share_by), resources.getString(R.string.pdf))
        if (isShareText) {
            mShareSummaryPdf?.isVisible = false
            mShareSummaryList.remove(mShareSummaryPdf)
        }

        onPreferenceTreeClick(getSelect())
    }

    private fun getSelect(): COUIJumpPreference? {
        return when (shareSelectType) {
            ShareSupportHelper.SHARE_TYPE_TEXT_TO_DOC -> mShareSummaryWord
            ShareSupportHelper.SHARE_TYPE_ONLY_TEXT -> mShareSummaryTxt
            ShareSupportHelper.SHARE_TYPE_TEXT_TO_PDF -> mShareSummaryPdf
            else -> mShareSummaryWord
        }
    }

    override fun onPreferenceTreeClick(preference: Preference?): Boolean {
        if (preference == null) {
            return super.onPreferenceTreeClick(preference)
        }
        when (preference.key) {
            SHARE_SUMMARY_WORD_TYPE -> {
                type = ShareSupportHelper.SHARE_TYPE_TEXT_TO_DOC
                setChoiceSelected(preference as COUIJumpPreference)
            }

            SHARE_SUMMARY_TXT_TYPE -> {
                type = ShareSupportHelper.SHARE_TYPE_ONLY_TEXT
                setChoiceSelected(preference as COUIJumpPreference)
            }

            SHARE_SUMMARY_PDF_TYPE -> {
                type = ShareSupportHelper.SHARE_TYPE_TEXT_TO_PDF
                setChoiceSelected(preference as COUIJumpPreference)
            }
        }
        mShareSummaryButton?.setListener(object :
            ShareSummaryAndTextPreference.OnBindOrClickListener {
            override fun onClick() {
                mCallBack?.invoke(type)
                type = 0
            }
        })
        return super.onPreferenceTreeClick(preference)
    }

    private fun setChoiceSelected(preference: COUIJumpPreference?) {
        if (preference == null) {
            DebugUtil.d(TAG, "setChoiceSelected preference is null")
            return
        }
        mShareSummaryList.forEach {
            if (it == preference) {
                it.setJump(
                    resources.getDrawable(
                        R.drawable.share_summary_item_selected
                    )
                )
            } else {
                it?.setJump(null)
            }
        }
    }

    fun setCallBack(isShareText: Boolean, selectShareType: Int, callBack: (Int) -> Unit) {
        this.isShareText = isShareText
        this.shareSelectType = selectShareType
        mCallBack = callBack
    }

    override fun onDestroy() {
        super.onDestroy()
        if (mCallBack != null) {
            mCallBack = null
        }
    }
}
