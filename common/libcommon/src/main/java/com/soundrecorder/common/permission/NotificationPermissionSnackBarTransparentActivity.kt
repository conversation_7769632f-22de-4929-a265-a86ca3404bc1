package com.soundrecorder.common.permission

import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import com.coui.appcompat.snackbar.COUISnackBar
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.R
import com.soundrecorder.common.permission.PermissionUtils.POST_NOTIFICATIONS

class NotificationPermissionSnackBarTransparentActivity : BaseActivity() {

    companion object {
        const val TAG = "NotificationPermissionTransparentActivity"

        /**
         * 记录通知引导snackBar 是否在显示中
         * true: 正在显示通知权限snackBar
         * false： 未显示
         */
        @JvmStatic
        var isNotificationSnackBarShowing = false
    }

    private var mSnackBar: COUISnackBar? = null
    private var mOutSideView: View? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        //去掉动画
        overridePendingTransition(0, 0)
        //设置透明界面用于点击空白事件
        setContentView(R.layout.trans_view)
        //不需要重建，直接finis
        if (savedInstanceState != null) {
            finish()
        }
        initListener()
        setNavigationBarColor()
        showSnackBar()
        registerFinishAndRemoveTaskCallback()
    }

    private fun setNavigationBarColor() {
        val color = intent.getIntExtra("color", com.soundrecorder.base.R.color.common_background_color)
        window?.navigationBarColor = color
    }

    private fun initListener() {
        //点击空白消失
        mOutSideView = findViewById<View>(R.id.convert_view_container).apply {
            setOnClickListener {
                checkShouldDismissSnackBarAndFinish()
            }
        }
    }

    private fun showSnackBar() {
        if (PermissionUtils.hasNotificationPermission()) {
            DebugUtil.e(TAG, "已经有通知权限,不需要展示snackBar，finish activity")
            finish()
            return
        }
        mOutSideView?.let {
            mSnackBar = COUISnackBar.make(it, getString(R.string.permission_open_notification_v3), 5000).apply {
                (parent as? ViewGroup)?.clipChildren = false
                setOnAction(R.string.all_file_access_dialog_positive) {
                    PermissionUtils.goToAppSettingConfigurePermissions(this@NotificationPermissionSnackBarTransparentActivity,
                        arrayListOf(POST_NOTIFICATIONS))
                    finish()
                }
                setOnStatusChangeListener(object : COUISnackBar.OnStatusChangeListener {
                    override fun onShown(p0: COUISnackBar?) {}

                    override fun onDismissed(p0: COUISnackBar?) {
                        if (!isFinishing) {
                            finish()
                        }
                    }
                })
                show()
            }
            isNotificationSnackBarShowing = true
        }
    }

    private fun checkShouldDismissSnackBarAndFinish() {
        mSnackBar?.let {
            if (it.isShown) {
                it.dismiss()
                finish()
            }
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        //点击返回按钮消失
        checkShouldDismissSnackBarAndFinish()
    }

    override fun onResume() {
        super.onResume()
        //去设置开启权限后返回页面snackBar需要消失
        if (PermissionUtils.hasNotificationPermission()) {
            checkShouldDismissSnackBarAndFinish()
        }
    }

    override fun finish() {
        super.finish()
        //去掉动画
        overridePendingTransition(0, 0)
    }

    override fun onDestroy() {
        isNotificationSnackBarShowing = false
        unregisterFinishAndRemoveTaskCallback()
        super.onDestroy()
    }
}