package com.soundrecorder.common.utils.sound;


import android.media.AudioAttributes;
import android.media.SoundPool;

import com.soundrecorder.base.utils.DebugUtil;


public class DeleteSoundPoolPlayer implements IDeleteSoundPlayer {

    private static final String TAG = "DeleteSoundPoolPlayer";

    private static final int DEFAULT_LEGACY_STREAM_TYPE = 1;
    private static final int DEFAULT_MAX_STREAMS = 1;

    private static final int DEFAULT_LEFTVOLUM = 1;
    private static final int DEFAULT_RIGHTVOLUM = 1;
    private static final int DEFAULT_PRIOTY = 1;
    private static final int DEFAULT_LOOP = 0;
    private static final int DEFAULT_RATE = 1;

    private SoundPool mSoundPool;
    private int mDeleteId;
    private boolean mPrepared;

    DeleteSoundPoolPlayer() {
        this.initData();
    }

    @Override
    public void initData() {
        DebugUtil.i(TAG, "initData");
        if (mSoundPool == null) {
            SoundPool.Builder poolBuilder = new SoundPool.Builder();
            AudioAttributes attr = (new android.media.AudioAttributes.Builder()).setLegacyStreamType(DEFAULT_LEGACY_STREAM_TYPE).build();
            poolBuilder.setMaxStreams(DEFAULT_MAX_STREAMS);
            poolBuilder.setAudioAttributes(attr);
            mSoundPool = poolBuilder.build();
        }
        if (mSoundPool != null) {
            try {
                mDeleteId = mSoundPool.load(DeleteSoundEffectManager.DELETE_PATH, DEFAULT_PRIOTY);
                DebugUtil.i(TAG, "load id: " + mDeleteId);
                mSoundPool.setOnLoadCompleteListener(new SoundPool.OnLoadCompleteListener() {
                    @Override
                    public void onLoadComplete(SoundPool soundPool, int i, int i1) {
                        DebugUtil.i(TAG, "initData: onLoadComplete ");
                        mPrepared = true;
                    }
                });
                DebugUtil.i(TAG, "initData: mDeleteId: " + mDeleteId + ", mPrepared: " + mPrepared);
            } catch (Exception e) {
                DebugUtil.e(TAG, "init data error", e);
            }
        }
    }

    @Override
    public void playDeleteSound() {
        DebugUtil.i(TAG, "playDeleteSound");
        try {
            if ((mDeleteId != 0) && mPrepared && (mSoundPool != null)) {
                mSoundPool.play(mDeleteId, DEFAULT_LEFTVOLUM, DEFAULT_RIGHTVOLUM, DEFAULT_PRIOTY, DEFAULT_LOOP, DEFAULT_RATE);
            } else {
                DebugUtil.w(TAG, "playDeleteSound deleteId = 0 " + (mDeleteId == 0) + ", mPrepared : " + mPrepared + ", trig initData");
                initData();
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "play delete sound error", e);
        }
    }

    public void release() {
        if (mSoundPool != null) {
            mSoundPool.release();
        }
        mSoundPool = null;
        mDeleteId = 0;
        mPrepared = false;
        DebugUtil.i(TAG, "release");
    }
}
