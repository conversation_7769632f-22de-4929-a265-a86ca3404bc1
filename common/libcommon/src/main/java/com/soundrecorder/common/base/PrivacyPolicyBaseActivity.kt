/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PrivacyPolicyBaseActivity
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/11
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.base

import android.content.res.Configuration
import android.os.Bundle
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.modulerouter.privacyPolicy.IFunctionGuideDelegate
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyDelegate
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyResultListener
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyInterface
import com.soundrecorder.modulerouter.utils.Injector

open class PrivacyPolicyBaseActivity : BaseActivity(), IPrivacyPolicyResultListener {
    companion object {
        /**
         * 多入口用户须知弹窗需保持一致性
         * 记录前台最新显示dialogType,同录音进程生命周期一致
         */
        @JvmStatic
        var syncPrivacyDialogShowingType: Int? = null
    }

    protected var mIPrivacyPolicyDelegate: IPrivacyPolicyDelegate? = null
    protected var iFunctionGuideDelete: IFunctionGuideDelegate? = null

    private val privacyPolicyApi by lazy {
        Injector.injectFactory<PrivacyPolicyInterface>()
    }

    open fun getPrivacyPolicyDelegate(): IPrivacyPolicyDelegate? {
        if (mIPrivacyPolicyDelegate == null) {
            mIPrivacyPolicyDelegate = privacyPolicyApi?.newPrivacyPolicyDelegate(
                context = this,
                type = policyType(),
                resultListener = this
            )
        }

        return mIPrivacyPolicyDelegate
    }

    open fun getFunctionGuideDelegate(): IFunctionGuideDelegate? {
        if (iFunctionGuideDelete == null) {
            iFunctionGuideDelete = privacyPolicyApi?.newFunctionGuideDelegate(this) {
                /*来自用户须知弹窗，走用户须知弹窗同意点击事件*/
                if (it) {
                    onAgreeClick()
                } else {
                    checkAndShowPrivacyPolicyDialog()
                }
            }
        }
        return iFunctionGuideDelete
    }

    override fun onSaveInstanceState(outState: Bundle) {
        onPrivacyPolicySaveInstanceState(outState)
        super.onSaveInstanceState(outState)
    }

    protected open fun onPrivacyPolicySaveInstanceState(outState: Bundle) {
        getPrivacyPolicyDelegate()?.onSaveInstanceState(outState)
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        onPrivacyPolicyRestoreInstanceState(savedInstanceState)
    }

    protected open fun onPrivacyPolicyRestoreInstanceState(savedInstanceState: Bundle) {
        getPrivacyPolicyDelegate()?.onRestoreInstanceState(savedInstanceState)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        onPrivacyPolicyConfigurationChanged(newConfig)
    }

    protected open fun onPrivacyPolicyConfigurationChanged(newConfig: Configuration) {
        getPrivacyPolicyDelegate()?.onConfigurationChanged(newConfig)
    }

    override fun onResume() {
        checkAndDismissPrivacyPolicyDialog()
        super.onResume()
        checkAndShowPrivacyPolicyDialog()
    }

    protected open fun checkAndDismissPrivacyPolicyDialog() {
        getPrivacyPolicyDelegate()?.checkAndDismissDialog()
    }

    protected open fun checkAndShowPrivacyPolicyDialog() {
        when (PermissionUtils.getNextAction()) {
            PermissionUtils.SHOULD_SHOW_USER_NOTICE -> {
                // 优先使用SYNC_PRIVACY_DIALOG_TYPE保持一致性，若为null，则默认值
                getPrivacyPolicyDelegate()?.resumeShowDialog(
                    syncPrivacyDialogShowingType ?: PrivacyPolicyConstant.TYPE_USER_NOTICE_DEFAULT
                )
            }

            PermissionUtils.SHOULD_SHOW_ALL_FILE_PERMISSION -> {
                if (!BaseUtil.isEXP() && !PermissionUtils.checkPermissionUpdateAlreadyApply(this)) {
                    getPrivacyPolicyDelegate()?.resumeShowDialog(PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_UPDATE)
                } else if (getFunctionGuideDelegate()?.canShowFunctionDialog() == true) {
                    // 定向录音 or 摘要 功能介绍弹窗
                    getFunctionGuideDelegate()?.resumeShowFunctionDialog(false)
                } else if (BaseUtil.isAndroidROrLater && canShowOpenAllFilePermissionOnResume()) {
                    afterCheckAndShowPrivacyPolicyDialogOnResume(PermissionUtils.SHOULD_SHOW_ALL_FILE_PERMISSION)
                } else {
                    // 跳过，就直接进入下一步SHOULD_REQUEST_PERMISSIONS步骤
                    afterCheckAndShowPrivacyPolicyDialogOnResume(PermissionUtils.SHOULD_REQUEST_PERMISSIONS)
                }
            }

            PermissionUtils.SHOULD_REQUEST_PERMISSIONS -> {
                if (!BaseUtil.isEXP() && !PermissionUtils.checkPermissionUpdateAlreadyApply(this)) {
                    // 云同步功能升级弹窗
                    getPrivacyPolicyDelegate()?.resumeShowDialog(PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_UPDATE)
                } else if (getFunctionGuideDelegate()?.canShowFunctionDialog() == true) {
                    // 定向录音 or 摘要 功能介绍弹窗
                    getFunctionGuideDelegate()?.resumeShowFunctionDialog(false)
                } else {
                    afterCheckAndShowPrivacyPolicyDialogOnResume(PermissionUtils.SHOULD_REQUEST_PERMISSIONS)
                }
            }
        }
    }

    /**
     * 是否支持onResume所有文件管理权限检查
     * @return true ：onResume-PermissionUtil.nextAction==SHOULD_SHOW_ALL_FILE_PERMISSION,
     * 若无所有文件管理权限，则会去申请所有文件管理权限；
     * @return false:onResume-PermissionUtil.nextAction==SHOULD_SHOW_ALL_FILE_PERMISSION,
     * 若无所有文件管理权限，则不会去申请；
     */
    protected open fun canShowOpenAllFilePermissionOnResume(): Boolean = true

    protected open fun afterCheckAndShowPrivacyPolicyDialogOnResume(nextAction: Int) {}

    protected open fun policyType(): Int = IPrivacyPolicyDelegate.POLICY_TYPE_COMMON

    override fun onPrivacyPolicySuccess(type: Int, pageFrom: Int?) {
        if (type in intArrayOf(
                PrivacyPolicyConstant.TYPE_USER_NOTICE,
                PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC,
                PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC_STILL,
                PrivacyPolicyConstant.TYPE_USER_NOTICE_LIGHT_OS,
                PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_UPDATE,
                PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_FUNCTION
            )
        ) {
            if (getFunctionGuideDelegate()?.canShowFunctionDialog() == true) {
                // 定向录音 or 摘要 功能介绍弹窗
                getFunctionGuideDelegate()?.resumeShowFunctionDialog(true)
            } else {
                onAgreeClick()
            }
        }
    }

    protected open fun onAgreeClick() {}

    protected fun getLastShowPolicyDialogType(): Int? {
        return getPrivacyPolicyDelegate()?.getLastShowPrivacyDialogType()
    }

    fun hasConvertPermission(): Boolean = getPrivacyPolicyDelegate()?.hasConvertPermission() == true

    override fun onDestroy() {
        getPrivacyPolicyDelegate()?.onDestroy()
        super.onDestroy()
    }
}