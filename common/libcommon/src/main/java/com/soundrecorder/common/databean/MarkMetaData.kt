package com.soundrecorder.common.databean

data class MarkMetaData(
    var markText: String = "",
    var imagePath: String = "",
    var currentTimeMillis: Long = -1L,
    var width: Int = -1,
    var height: Int = -1,
) {
    var from: MarkDataSource = MarkDataSource.Converting
}

data class AmpMetaData(
    var x: Float = Float.MIN_VALUE,
    var isRTL: Boolean = false
)

enum class MarkDataSource {
    Normal, Converting
}
