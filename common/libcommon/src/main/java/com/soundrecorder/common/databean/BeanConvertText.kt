/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  BaseConvertText
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.databean

data class BeanConvertText(
    val taskID: String,
    val timecost: String,
    var recogText: String,
    val process: String,
    val sublist: List<SubItem>?,
    val keyWord: List<KeyWord>?,

    var traceId: String? = null,
) {

    fun getSpeakerRoleNumber(): Int {
        if (sublist.isNullOrEmpty()) {
            return 0
        }
        val roleSet = HashSet<Int>()
        for (item in sublist) {
            if (item.roleId != null && item.roleId > 0) {
                roleSet.add(item.roleId)
            }
        }
        return roleSet.size
    }

    data class SubItem(
        val seq: Int,
        val roleId: Int? = null,
        var recgText: String,
        val beginTime: String,
        val endTime: String,
        var timestamp: String? = null,
        var rawText: String? = null
    ) {
        constructor(
            seq: Int,
            roleId: Int,
            recgText: String,
            beginTime: String,
            endTime: String,
        ) : this(seq, roleId, recgText, beginTime, endTime, null, null)

        override fun toString(): String {
            return "SubItem(seq=$seq, roleId=$roleId, recgText.size='${recgText.length}', beginTime='$beginTime', " +
                    "endTime='$endTime', timestamp=$timestamp, rawText=${rawText?.length})"
        }
    }

    override fun toString(): String {
        return "BeanConvertText(taskID='$taskID', timecost='$timecost', recogText.size='${recogText.length}', " +
                "process='$process', sublist=$sublist, keyWord=$keyWord)"
    }
}