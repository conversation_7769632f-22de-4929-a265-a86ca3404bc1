/***********************************************************
 * * Copyright (C), 2010-2030, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  ShareUtil
 * * Description: ShareBottomSheetDialogFragment
 * * Version: 1.0
 * * Date : 2025/7/2
 * * Author: keweiwei
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  keweiwei    2025/7/2   1.0    build this module
 ****************************************************************/
package com.soundrecorder.common.flexible

import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment

class ShareBottomSheetDialogFragment : COUIBottomSheetDialogFragment() {

    var shareRecordDialogFragment: ShareSummaryDialogFragment? = null

    override fun setMainPanelFragment(panelFragment: COUIPanelFragment?) {
        super.setMainPanelFragment(panelFragment)
        shareRecordDialogFragment = panelFragment as? ShareSummaryDialogFragment
    }
}