/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  AutoDiForRecorderLog.kt
 * * Description : AutoDiForRecorderLog
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.oplus.recorderlogx.di

import androidx.annotation.Keep
import com.oplus.recorderlogx.XLogApi
import com.soundrecorder.modulerouter.xlog.RecorderLogInterface
import org.koin.dsl.module

@Keep
object AutoDiForRecorderLog {
    val logModule = module {
        single<RecorderLogInterface>(createdAtStart = true) {
            XLogApi
        }
    }
}