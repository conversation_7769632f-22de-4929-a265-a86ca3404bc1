package com.oplus.recorderlog.util

import android.content.Context
import android.content.pm.ApplicationInfo
import android.os.Build
import android.os.Environment
import com.oplus.compat.os.SystemPropertiesNative
import com.oplus.recorderlog.log.RecorderLogger
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.CALL_RECORDINGS
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.INTERVIEW_RECORDINGS
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.MEETING_RECORDINGS
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.RECORDINGS
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.RECORD_TYPE_CALL
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.RECORD_TYPE_INTERVIEW
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.RECORD_TYPE_MEETING
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.RECORD_TYPE_STANDARD
import com.oplus.recorderlog.log.constants.XLogRecordConstants.Companion.STANDARD_RECORDINGS
import com.oplus.wrapper.os.SystemProperties
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.TimeZone

object BaseUtil {
    const val TAG = "BaseUtil"

    @JvmStatic
    val isAndroidQOrLater by lazy {
        Build.VERSION.SDK_INT > Build.VERSION_CODES.P
    }

    @JvmStatic
    val isAndroidROrLater by lazy {
        Build.VERSION.SDK_INT > Build.VERSION_CODES.Q
    }

    @JvmStatic
    val isAndroidQ by lazy {
        Build.VERSION.SDK_INT == Build.VERSION_CODES.Q
    }

    @JvmStatic
    val isAndroidUOrLater by lazy {
        Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE
    }

    @JvmStatic
    fun getPhoneStorageDir(context: Context?): String? {
        val internalFile: File? = AddonAdapterCompat.getInternalSdDirectory(context)
        return internalFile?.path
    }

    @JvmStatic
    fun getSDCardStorageDir(context: Context?): String {
        val storageSDcardPath: String = (AddonAdapterCompat.getExternalSdDirectory(context)?.path)
            ?: (AddonAdapterCompat.getExternalStorageDirectory()?.path) ?: ""
        return storageSDcardPath
    }

    @JvmStatic
    fun getRelativePathByRecordType(recordType: Int, includeLastSeparator: Boolean): String {
        val storeDir = StringBuffer()
        if (isAndroidQOrLater) {
            storeDir.append(Environment.DIRECTORY_MUSIC + File.separator)
        }
        when (recordType) {
            RECORD_TYPE_STANDARD -> storeDir.append(RECORDINGS + File.separator + STANDARD_RECORDINGS)
            RECORD_TYPE_MEETING -> storeDir.append(RECORDINGS + File.separator + MEETING_RECORDINGS)
            RECORD_TYPE_INTERVIEW -> storeDir.append(RECORDINGS + File.separator + INTERVIEW_RECORDINGS)
            RECORD_TYPE_CALL -> storeDir.append(RECORDINGS + File.separator + CALL_RECORDINGS)
            else -> {
            }
        }
        if (includeLastSeparator) {
            storeDir.append(File.separator)
        }
        return storeDir.toString()
    }

    @JvmStatic
    fun getConfigFromSystem(attr: String): String? {
        val result = getFeatureProperty(attr)
        RecorderLogger.i(TAG, "$attr: $result", false)
        return result
    }

    @JvmStatic
    fun getFormateTime(timeInmillsec: Long): String? {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        dateFormat.timeZone = TimeZone.getTimeZone("GMT+08")
        val result = dateFormat.format(Date(timeInmillsec))
        return result
    }

    @JvmStatic
    fun isApkInDebug(context: Context): Boolean {
        return try {
            val info = context.applicationInfo
            info.flags and ApplicationInfo.FLAG_DEBUGGABLE != 0
        } catch (e: Exception) {
            false
        }
    }

    @JvmStatic
    fun getFeatureProperty(key: String): String {
        kotlin.runCatching {
            return if (isAndroidUOrLater) {
                SystemProperties.get(key)
            } else {
                SystemPropertiesNative.get(key)
            }
        }.onFailure {
            RecorderLogger.i(TAG, it.message, false)
        }
        return ""
    }
}