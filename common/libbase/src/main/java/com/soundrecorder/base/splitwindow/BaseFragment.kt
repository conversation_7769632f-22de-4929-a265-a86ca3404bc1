package com.soundrecorder.base.splitwindow

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.LayoutRes
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.Fragment
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.modulerouter.CommonAction
import com.soundrecorder.modulerouter.utils.Injector

abstract class BaseFragment<V : ViewDataBinding> : Fragment() {

    companion object {
        private const val TAG = "BaseFragment"
    }

    lateinit var mBinding: V

    private val commonAction by lazy {
        Injector.injectFactory<CommonAction>()
    }

    @LayoutRes
    abstract fun layoutId(): Int
    abstract fun onViewCreated(savedInstanceState: Bundle?)
    abstract var logTag: String

    override fun onAttach(context: Context) {
        super.onAttach(context)
        DebugUtil.i(logTag, "onAttach")
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        DebugUtil.i(logTag, "onCreate")
        buryPointStartTime()
    }

    override fun onResume() {
        super.onResume()
        DebugUtil.i(logTag, "onResume")
    }

    override fun onPause() {
        super.onPause()
        DebugUtil.i(logTag, "onPause")
    }

    override fun onStart() {
        super.onStart()
        DebugUtil.i(logTag, "onStart")
    }

    override fun onStop() {
        super.onStop()
        DebugUtil.i(logTag, "onStop")
    }

    override fun onDestroy() {
        super.onDestroy()
        DebugUtil.i(logTag, "onDestroy")
        buryingPointEndTime()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        DebugUtil.i(logTag, "onDestroyView")
    }

    override fun onDetach() {
        super.onDetach()
        DebugUtil.i(logTag, "onDetach")
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        super.onCreateOptionsMenu(menu, inflater)
        DebugUtil.i(logTag, "onCreateOptionsMenu")
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        DebugUtil.i(logTag, "onCreateView")
        mBinding = DataBindingUtil.inflate(inflater, layoutId(), container, false)
        return mBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        DebugUtil.i(logTag, "onViewCreated")
        onViewCreated(savedInstanceState)
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        DebugUtil.i(TAG, "onActivityCreated")
        super.onActivityCreated(savedInstanceState)
    }


    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        val mActivity = activity
        if (mActivity != null) {
            SplitWindowUtil.putWindowParameterIntoSaveInstanceState(outState, mActivity)
            DebugUtil.i(TAG, "onSaveInstanceState put windowParameter")
        }
    }

    private fun buryPointStartTime() {
        /* 初始化启动时间 启动录音app = true */
        BaseUtil.sStartRecordingActivity = true
        if (BaseUtil.sStartTime == 0L) {
            BaseUtil.sStartTime = System.currentTimeMillis()
        }

        /**
         * 启动录音服务 启动类型 进入录音app -> 1 启动录制音频 -> 2
         * 启动录音 进入录音app 落地页是前台
         */
        BaseUtil.sValueEntryType = BaseUtil.VALUE_KEY_ENTRY_TYPE_APP
        commonAction?.useRecordEntryLaunch(BaseUtil.sValueEntryType, BaseUtil.VALUE_KEY_LANDING_TYPE_FRONT)
    }

    private fun buryingPointEndTime() {
        /* 初始化结束时间 并埋点上报 退出录音app = false */
        BaseUtil.sStartRecordingActivity = false
        if (BaseUtil.sEndTime == 0L && !BaseUtil.sStartRecordingService) {
            BaseUtil.sEndTime = System.currentTimeMillis()
            val durationTime = BaseUtil.sEndTime - BaseUtil.sStartTime
            /* 录音使用时长 埋点 */
            commonAction?.useRecordDurationMessage(durationTime)
            BaseUtil.sStartTime = 0L
            BaseUtil.sEndTime = 0L
        }
    }
}