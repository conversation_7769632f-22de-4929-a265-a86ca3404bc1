package com.soundrecorder.base.utils

import android.app.Activity
import android.view.inputmethod.InputMethodManager
import android.widget.TextView
import com.soundrecorder.base.BaseApplication

object KeyboardUtils {
    private val inputMethodManager by lazy {
        BaseApplication.getAppContext().getSystemService(InputMethodManager::class.java)
    }

    @JvmStatic
    fun TextView.showSoftInput() {
        requestFocus()
        inputMethodManager.showSoftInput(this, 0)
    }

    @JvmStatic
    fun hideSoftInput(activity: Activity) {
        val view = activity.window.decorView
        inputMethodManager.hideSoftInputFromWindow(
            view.windowToken,
            0
        )
    }
}