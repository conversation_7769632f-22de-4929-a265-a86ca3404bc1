/************************************************************
 * Copyright 2000-2017 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * FileName       : ToastManagerTest.java
 * Version Number : 1.0
 * Description    :
 * Author         : LiKun
 * Date           : 2019-08-07
 * History        :( ID,     Date,         Author, Description)
 * v1.0, 2019-08-07, <PERSON><PERSON><PERSON>, create
 ************************************************************/

package com.soundrecorder.base.utils;

import android.content.Context;
import android.os.Build;
import android.widget.Toast;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.base.R;
import com.soundrecorder.base.shadows.ShadowFeatureOption;
import com.soundrecorder.base.shadows.ShadowOS12FeatureUtil;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowToast;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class ToastManagerTest {

    private static final String TEST = "test";
    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
    }

    @Test
    public void should_showToast_when_showLongToast_with_resId() {
        ToastManager.showLongToast(null, TEST);
        Assert.assertNull(ShadowToast.getTextOfLatestToast());
        ToastManager.showLongToast(mContext, TEST);
        Assert.assertEquals(TEST, ShadowToast.getTextOfLatestToast());
    }

    @Test
    public void should_showToast_when_showLongToast_with_String() {
        ToastManager.showLongToast(null, R.string.delete);
        Assert.assertNull(ShadowToast.getTextOfLatestToast());
        ToastManager.showLongToast(mContext, R.string.delete);
        Assert.assertEquals(mContext.getString(R.string.delete), ShadowToast.getTextOfLatestToast());
    }

    @Test
    public void should_showToast_when_showShortToast_with_resId() {
        ToastManager.showShortToast(null, TEST);
        Assert.assertNull(ShadowToast.getTextOfLatestToast());
        ToastManager.showShortToast(mContext, TEST);
        Assert.assertEquals(TEST, ShadowToast.getTextOfLatestToast());
    }

    @Test
    public void should_showToast_when_showShortToast_with_String() {
        ToastManager.showShortToast(null, R.string.delete);
        Assert.assertNull(ShadowToast.getTextOfLatestToast());
        ToastManager.showShortToast(mContext, R.string.delete);
        Assert.assertEquals(mContext.getString(R.string.delete), ShadowToast.getTextOfLatestToast());
    }

    @Test
    public void should_showToast_when_showToast_with_resId() {
        ToastManager.showToast(null, R.string.delete, Toast.LENGTH_SHORT);
        Assert.assertNull(ShadowToast.getTextOfLatestToast());
        ToastManager.showToast(mContext, R.string.delete, Toast.LENGTH_SHORT);
        Assert.assertEquals(mContext.getString(R.string.delete), ShadowToast.getTextOfLatestToast());
    }

    @Test
    public void should_showToast_when_showToast_with_String() {
        CharSequence str = mContext.getResources().getText(R.string.delete);
        ToastManager.showToast(null, str, Toast.LENGTH_SHORT);
        Assert.assertNull(ShadowToast.getTextOfLatestToast());
        ToastManager.showToast(mContext, null, Toast.LENGTH_SHORT);
        Assert.assertNull(ShadowToast.getTextOfLatestToast());
        ToastManager.showToast(mContext, str, Toast.LENGTH_SHORT);
        Assert.assertEquals(mContext.getString(R.string.delete), ShadowToast.getTextOfLatestToast());
    }

    @Test
    public void should_showToast_with_resId() {
        ToastManager.showLongToast(R.string.delete);
        Assert.assertEquals(mContext.getString(R.string.delete), ShadowToast.getTextOfLatestToast());
    }

    @Test
    public void should_showToast_with_String() {
        CharSequence str = mContext.getResources().getText(R.string.delete);
        ToastManager.showLongToast(str);
        Assert.assertEquals(mContext.getString(R.string.delete), ShadowToast.getTextOfLatestToast());
    }
}
