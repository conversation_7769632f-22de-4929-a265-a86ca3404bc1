/************************************************************
 * Copyright 2000-2020 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * FileName       : FileRenameUtilTest.java
 * Version Number : 1.0
 * Description    :
 * Author         : LI Kun
 * Date           : 2019/9/27
 * History        :( ID,     Date,         Author, Description)
 * v1.0, 2019/9/27, LI Kun, create
 ************************************************************/

package com.soundrecorder.base.utils;

import android.text.TextUtils;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.File;
import java.util.ArrayList;

@RunWith(PowerMockRunner.class)
@PrepareForTest({TextUtils.class, DebugUtil.class})
public class FileRenameUtilTest {

    private static final String FOLDER_PATH = "testPath" + File.separator + "file";
    private static final String FOLDER_ROOT = "testPath";
    private static final String FILE_NAME_A = "a.txt";
    private static final String FILE_NAME_B = "b.txt";

    @Before
    public void setUp() throws Exception {
        PowerMockito.mockStatic(DebugUtil.class);
    }


    @Test
    public void should_filePath_when_create_with_fileNotExists() {
        File file = FileRenameUtil.create(FOLDER_PATH, FILE_NAME_A);
        String expectPath = FOLDER_PATH + File.separator + FILE_NAME_A;
        Assert.assertEquals(expectPath, file.getPath());
    }

    @Test
    public void should_filePath_when_create_with_fileExists() {
        File makeFile = MakeFileUtils.makeFile(FOLDER_PATH, FILE_NAME_A);
        File file = FileRenameUtil.create(FOLDER_PATH, FILE_NAME_A);
        Assert.assertNotEquals(makeFile.getName(), file.getName());
    }

    @Test
    public void should_null_when_create_with_filePathNull() {
        PowerMockito.mockStatic(TextUtils.class);
        PowerMockito.when(TextUtils.isEmpty((CharSequence) ArgumentMatchers.any())).thenAnswer(new Answer<Boolean>() {
            @Override
            public Boolean answer(InvocationOnMock invocation) {
                CharSequence a = invocation.getArgument(0);
                if (a == null || a.length() == 0) {
                    return true;
                }
                return false;
            }
        });
        File file = FileRenameUtil.create(null, FILE_NAME_A);
        Assert.assertNull(file);
    }

    @Test
    public void should_changeFileName_when_rename() {
        File makeFile = MakeFileUtils.makeFile(FOLDER_PATH, FILE_NAME_A);
        ArrayList<String> list = new ArrayList<>();
        String rename = FileRenameUtil.rename(makeFile, list);
        File file = new File(rename);
        Assert.assertNotEquals(makeFile.getName(), file.getName());
        Assert.assertFalse(makeFile.exists());
    }

    @Test
    public void should_changeFileName_when_renameTo() {
        File makeFile = MakeFileUtils.makeFile(FOLDER_PATH, FILE_NAME_A);
        Assert.assertTrue(makeFile.exists());
        String rename = FOLDER_PATH + File.separator + FILE_NAME_B;
        FileRenameUtil.renameTo(makeFile.getPath(), rename);
        Assert.assertFalse(makeFile.exists());
        Assert.assertTrue(new File(rename).exists());
    }

    @Test
    public void should_false_when_renameTo_with_srcPathIsNull() {
        String rename = FOLDER_PATH + File.separator + FILE_NAME_B;
        PowerMockito.mockStatic(TextUtils.class);
        PowerMockito.when(TextUtils.isEmpty((CharSequence) ArgumentMatchers.any())).thenAnswer(new Answer<Boolean>() {
            @Override
            public Boolean answer(InvocationOnMock invocation) {
                CharSequence a = invocation.getArgument(0);
                if (a == null || a.length() == 0) {
                    return true;
                }
                return false;
            }
        });
        boolean b = FileRenameUtil.renameTo(null, rename);
        Assert.assertFalse(b);
    }

    @After
    public void tearDown() {
        File file = new File(FOLDER_ROOT);
        if (file.exists()) {
            MakeFileUtils.delFolder(FOLDER_ROOT);
        }
    }
}
