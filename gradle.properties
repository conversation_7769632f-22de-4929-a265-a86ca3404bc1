#Configure global properties
#Define APK name
prop_archivesBaseName=NewSoundRecorder
#å¿é¡»ï¼éç½®sdkçGroupIdä¿¡æ¯ï¼å¦ææå¤ä¸ªä¸åç»åï¼
#å¯ç¨prop_archivesGroupName1/prop_archivesGroupName2è¿æ ·çå¼åºå
prop_archivesGroupName=com.oplus.soundrecorder
#å¿é¡»ï¼éç½®åæ¨¡åççæ¬å·ï¼å¨æ¨¡åbuild.gradleä¸­æå
prop_smallCardVersionName=1.0.12
prop_DragonFlyCardVersionName=1.0.4
#å¿é¡»ï¼å£°æSDKççæ¬åç¼ï¼æ¬å°éç½®æ¯å ä½ç¬¦ï¼æå¡å¨ä¼ä½¿ç¨åå¼ç±»å+hashè¦çè¯¥å¼ï¼é¿åéå¤
versionSuffix=-alpha01
#default applicationIDãversioninfoï¼è¥å¢å æ¹å¨ååï¼è¿éè¦æ´æ¹DatabaseConstant.getRecorderPackageNameæ¹æ³
prop_applicationId_oppo=com.coloros.soundrecorder
#oneplus exp applicationIdãversioninfo
prop_applicationId_oneplus=com.oneplus.soundrecorder
# ä¸å çversionCode 30004326 V3.0.26,ALMåä¸åæ¯æ æ³æå»º2ä¸ªçæ¬
# ä¸ºç»ä¸çæ¬å·ï¼osçæ¬åæ¹å¢å 00ï¼ä½¿å¶ï¼å¤§äºä¸å code
mainVersionCode=16003008
mainVersionName=16.3.8
#Define target SDK api level
prop_compileSdkVersion=android-36
prop_compileSdk=36
#Define build tools version
prop_buildToolsVersion=36.0.0
#Define gradle plugin version
#Define java compile version on pipeline
prop_targetCompatibility=17
#Define min sdk version
prop_minSdkVersion=29
#Define target sdk version
prop_targetSdkVersion=36
#support compact version
#prop_versionName=13.2.8
#ç¨æ·é¡»ç¥å¼¹çªæ§ä»¶
#ååèªåçº§çåºç¨ï¼æ¥å¥coui-supportçåºç¨ï¼ï¼éè¦åç¬æ¥å¥å¨å±é¢è²çå¼å®¹å
#kotlin version
#Define the APK Signature Scheme: v1 Scheme, enabled by default
prop_apkSignatureSchemeV1=true
#Define the APK Signature Scheme: v2 Scheme, enabled by default
prop_apkSignatureSchemeV2=true
#configure that increase build speed
#android.enableR8=false
android.injected.testOnly=false
#Define oppo-native-dependencies plugin version
#Define keystore file directory path,should use '\' escape on Windows
prop_keyPath=../keystore
#Define app prebuild SDK api level,for example android-21,android-22,android-23
prop_appPrebuildSdkVersion=android-30,android-31,android-32,android-33,android-U,android-34
#QR app merge add
prop_appTargetPlatformVersion=android-30,android-31,android-32,android-33,android-U,android-34
# Define build-plugin version
prop_obuildVersion=1.7.0
#****(å»ºè®®obuildæä»¶é½åçº§å°1.5.4ï¼gradleæä»¶åçº§å°4.0ä»¥ä¸ï¼javaçæ¬åçº§å°11)
# Gerritæ¨¡åå¿é¡»ï¼ç¨æ¥è¯å«æ¯æ°æ¡æ¶æå»º
#useOBuildPlugin=true
# 3.12.12.222
#Define resource subpacage params,default is empty
#CI Server will introduce the correct params when build
#app can verify locally use params as below
prop_resConfigs=zh_CN,en_US,zh_TW,hi_IN,in_ID,ms_MY,my_MM,tl_PH,ru_RU,th_TH,vi_VN,ja_JP,fr_FR,bn_BD,ko_KR,lo_LA,ne_NP,bo_CN,ug_CN,km_KH,ar_EG,zh_HK,kk_CN,kea_CV,ar_AR,fil_PH,id_ID,it_IT,nl_NL,de_DE,es_MX,pt_BR
#CI service will set xhdpi or xxhdpi
prop_densityResConfigs=
prop_oapmMavenUrl=http://nexus.os.adc.com/nexus/content/groups/public/
#Define app disable resource subpackage,default is comment off!!!!
#prop_disableSubPackage=true

#Define oppo maven repositories url,stable branch should use Maven Stable Url!!!
#Maven Stable Url
prop_oppoMavenUrl=http://maven.scm.adc.com:8081/nexus/content/groups/stable-public/
#Maven Cloud Url
prop_oppoCloudSdkMavenUrl=http://maven.scm.adc.com:8081/nexus/content/groups/public/

#Maven Snapshots Url
#prop_oppoMavenUrl=http://maven.scm.adc.com:8081/nexus/content/groups/public/
prop_sdkMavenUrlRelease=http://mirror-maven.myoas.com/repository/ars-sdk-release/

#Define versionCommit and versionDate,used in AndroidManifest.xml
prop_versionCommit=12345678
prop_versionDate=150908
prop_oppoMavenUrlSnapshot=http://maven.scm.adc.com:8081/nexus/content/groups/snapshots/
#Maven Snapshots Url
prop_oppoMavenUrlRelease=http://maven.scm.adc.com:8081/nexus/content/groups/stable-public/
prop_oppoSmallCardMavenUrlRelease=http://mirror-maven.myoas.com/repository/ars-sdk-release/
prop_oppoMavenUrlMaven=http://maven.scm.adc.com:8081/nexus/content/groups/public/
#cloudkit sdk å¿«ç§çæ¬å°å
prop_mavenUrlCloudKit=http://nexus.os.adc.com/nexus/content/repositories/snapshots/
#cloudkit sdk  æ­£å¼å°åï¼http://nexus.os.adc.com/nexus/content/repositories/releases/
prop_mavenUrlCloudKitRelease=http://nexus.os.adc.com/nexus/content/repositories/iss_official_releases
# ?? channel sdk
# ?? O0002220
MAVEN_USERNAME=newadmin
MAVEN_PASSWORD=3e5x*co#n$dxy1@diBbg
prop_decoupleSdk=true
#å¿é¡»ï¼å£°ææ¯å¦ä½¿ç¨åé¨SDKï¼falseä½¿ç¨Googleåç
prop_internalSdk=false
#Define app disable resource subpackage,default is comment off!!!!
prop_disableSubPackage=false
#æå¡å¨ç«¯ä¼ä¿®æ¹è¿ä¸ªå¼ä¸ºå¤éçææè¯­è¨
prop_exp_resConfig=en_US,es_MX,es_ES,pt_BR,da_DK,de_DE,el_GR,fr_FR,in_ID,it_IT,ja_JP,nl_NL,nb_NO,pt_PT,ru_RU,sv_SE,th_TH,tr_TR,vi_VN,zh_CN,zh_HK,zh_TW,my_MM,my_ZG,hi_IN,ms_MY,tl_PH,cs_CZ,en_GB,hu_HU,pl_PL,ro_RO,uk_UA,fa_IR,ur_PK,ar_EG,bn_BD,ko_KR,mr_IN,ta_IN,gu_IN,lo_LA,km_KH,sw_KE,si_LK,pa_IN,te_IN,kn_IN,ml_IN,or_IN,as_IN,kk_KZ,iw_IL,ne_NP,bo_CN,en_AU,en_NZ,ca_ES,eu_ES,bg_BG,fi_FI,sk_SK,hr_HR,lt_LT,sl_SI,lv_LV,et_EE,gl_ES,fr_CH,it_CH,de_CH,uz_UA,uz_UZ,sr_Latn_RS,kea_CV,ar_AR,fil_PH,id_ID,sr_RS,sq_AL,mk_MK,az_AZ,ka_GE,hy_AM,bs_BA
#å¿é¡»ï¼should enble AndroidX,otherwise sdk will use support,may cause some confirm
android.useAndroidX=true
android.enableJetifier=true
##############################
#######Don't modify below#####
#hdpi resource subpacakge params
#prop_resConfigs=zh_CN,en_US,zh_TW,nodpi,hdpi
#xhdpi resource subpacakge params
#prop_resConfigs=zh_CN,en_US,zh_TW,nodpi,xhdpi
#xxhdpi resource subpacakge params
#prop_resConfigs=zh_CN,en_US,zh_TW,nodpi,xxhdpi
#2.5K resource subpacakge params
#prop_resConfigs=zh_CN,en_US,zh_TW,nodpi,xxxhdpi
#1080p with 2.5K resource subpacakge params
#prop_resConfigs=zh_CN,en_US,zh_TW,nodpi,xxhdpi,xxxhdpi
#######################
prop_keyPassword=android
prop_storePassword=android
sonatypeUsername=swdp
sonatypePassword=swdp
########################
#####Don't modify above#####
############################
# change output dir, use for running AndroidJunt4Test in AndroidStudio
enable_androidJunit4Test_outputDir=false
#oppo unit test
oppo_unit_test=unitTestTask
#codeScanTask codeScanTask #å¿é¡»ï¼ä»£ç æ«æçä»»å¡
codeScanCommand=codeScanTask
#gradleéç½®ä¼å ---------åè start-------------------------
#å¼å¯gradleç¼å­ï¼é»è®¤ä¸ä¼å¼å¯ï¼demoç¬¬äºæ¬¡cleanç¼è¯æµè¯æ¶é´åå°50%
org.gradle.caching=true
#android.enableBuildCache=true
#éè¿éç¨ä»¥åæå»ºä¸­çè®¡ç®æ¥æé«æå»ºéåº¦ãç¶èï¼å®çå¥½å¤æ¯å·¨å¤§çï¼æä»¬éå¸¸ä¼å¨éåçæå»ºä¸­åå°15-75%çæå»ºæ¶é´ã
#Gradleå®æ¤è¿ç¨å¨é»è®¤æåµä¸æ¯å¯ç¨ç
org.gradle.daemon=true
#å¹¶åçº¿ç¨æ°éï¼é»è®¤ä¸ºcpuæ ¸å¿æ°
org.gradle.workers.max=16
# éè¿éç½® Gradle æç¨çæä½³ JVM åå¾åæ¶å¨ï¼å¯ä»¥æåæå»ºæ§è½ã
org.gradle.jvmargs=-Xmx12288m -Dfile.encoding=UTF-8 -XX:+UseParallelGC
# When configured, Gradle will run in incubating parallel mode.ï¼éç½®åï¼Gradleå°ä»¥å¹¶è¡æ¨¡å¼è¿è¡ãï¼
# This option should only be used with decoupled projects. More details, visitï¼è¯¥éç½®ä»ä»ç¨ä¸ºå®ç°ç¼è¯éç¦»çå¤é¡¹ç®ï¼åªéå¯¹ä½¿ç¨ç»ä»¶åæææï¼å®ç°ç¼è¯éç¦»çåºç¨çæææå¥½ï¼
org.gradle.parallel=true
#gradleéç½®ä¼å ---------end-------------------------

blackProjects=modulerouter,Questionnaire,PhotoViewer,common,component,page,page:card
sourceDirs=src/main/java
android.suppressUnsupportedCompileSdk=33
kotlin.daemon.jvmargs=-Xmx8192m
android.nonTransitiveRClass=true