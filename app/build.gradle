apply plugin: 'com.android.application'
apply plugin: 'obuildplugin'
apply plugin: 'kotlin-android'
apply plugin: 'oapm-perf'
apply plugin: 'kotlin-kapt'
apply plugin: 'osigner'
apply plugin: 'kotlin-parcelize'
apply plugin: 'com.autotest.opasm.CoverageInstPlugin'

android {

    lint {
        abortOnError false
        checkDependencies true
        checkReleaseBuilds false
        ignoreWarnings true
        lintConfig file('lint.xml')
    }
    compileSdkVersion = prop_compileSdkVersion
    buildToolsVersion = prop_buildToolsVersion

    dataBinding {
        enabled = true
    }

    buildFeatures {
        viewBinding true
        buildConfig true
    }

    kotlinOptions {
        jvmTarget = "${prop_targetCompatibility}"

        // Enable Coroutines and Flow APIs
        freeCompilerArgs += "-Xopt-in=kotlinx.coroutines.ExperimentalCoroutinesApi"
        freeCompilerArgs += "-Xopt-in=kotlinx.coroutines.FlowPreview"
    }

    signingConfigs {

        platform {
            storeFile file("${prop_keyPath}/platform.keystore")
            keyAlias 'androidplatformkey'
            keyPassword prop_keyPassword
            storePassword prop_storePassword
            v1SigningEnabled prop_apkSignatureSchemeV1.toBoolean()
            v2SigningEnabled prop_apkSignatureSchemeV2.toBoolean()
        }

        media {
            storeFile file("${prop_keyPath}/media.keystore")
            keyAlias 'androidmediakey'
            keyPassword prop_keyPassword
            storePassword prop_storePassword
            v1SigningEnabled prop_apkSignatureSchemeV1.toBoolean()
            v2SigningEnabled prop_apkSignatureSchemeV2.toBoolean()
        }

        shared {
            storeFile file("${prop_keyPath}/shared.keystore")
            keyAlias 'androidsharedkey'
            keyPassword prop_keyPassword
            storePassword prop_storePassword
            v1SigningEnabled prop_apkSignatureSchemeV1.toBoolean()
            v2SigningEnabled prop_apkSignatureSchemeV2.toBoolean()
        }

        testkey {
            storeFile file("${prop_keyPath}/testkey.keystore")
            keyAlias 'androidtestkeykey'
            keyPassword prop_keyPassword
            storePassword prop_storePassword
            v1SigningEnabled prop_apkSignatureSchemeV1.toBoolean()
            v2SigningEnabled prop_apkSignatureSchemeV2.toBoolean()
        }
    }

    namespace "com.coloros.soundrecorder"

    defaultConfig {
        applicationId "com.coloros.soundrecorder"
        minSdkVersion prop_minSdkVersion
        targetSdkVersion prop_targetSdkVersion
        versionCode mainVersionCode.toInteger()
        versionName mainVersionName.toString()

        archivesBaseName = prop_archivesBaseName

        ndk {
            abiFilters 'arm64-v8a'
        }

        if (project.hasProperty('prop_disableSubPackage') && prop_disableSubPackage.toBoolean()) {
            println("app disable resource subpacakge")
        } else {
            if (project.hasProperty('prop_resConfigs') && !prop_resConfigs.toString().isEmpty()) {
                resConfigs prop_resConfigs
            } else {
                println("subpacakge config is empty, no subpackage")
            }
        }
        manifestPlaceholders = [versionCommit: "${prop_versionCommit}", versionDate: "${prop_versionDate}"]

        testInstrumentationRunner "com.oppo.autotest.olt.testlib.common.OPPORunner"
    }

    buildTypes {
        debug {
            buildConfigField "String", "signture", "\"#E DvE&DtC'\u0014-\u0019/M.J,M}\u0019.K~\u001Fy\u001D/JzN\""
            buildConfigField "String", "flytekUrl", '"z\\u000Ez\\nyClC4C4\\u001Ab\\u0004}\\bfH+Ej\\u000Ea\\u0002-]2^7T-\\u0002r\\u0000i\\u001F~\\u001DdJ\\"V;W"'
            //config signing type

            buildConfigField "String", "enbackUrl", "\"7a0e7a0a79436c432256394d60137604721765482b456b0a660a6b1c720663006846254a27087e4f\""
            buildConfigField "String", "sec", "\"2a4e77432743731024122a4e7a4e781e26472217744725117446751370142515\""
            buildConfigField "String", "pub", "\"5f165f1d543e7f3173147f0e660d64231a6d5d1f5e0f4a0c4d0c430041102869246d24662542094a0b5a1f5e6c3c4f0370482b1a680e7619284122660461345a3b59695e1a570160241c762e784019286e086f2b793e0e3972440563194c0a491a4d0471136b1d5a1e643c0b6d035b377e4b0e5c39610f63172171365561235b11703c73065c6d5b095c3d6d28650c45374f1b51033b7e197b1060105c047e2c451527431972451566256910576f21685a394f2217650922670a623571400c7a1d4a077e1753264271027714583566236a0d394f3a4f03440a7f3002532157653047107f195e6e45725929115b7439402e476c1443750f4c34600a46084432512360364e16217330410676027a1b7c3f083d0a6e20591b50386f227817254e79085004314201420e7d0a650e655002771670247e0a72204477426d5b2d74115e0c5a1270064b117a49007303625513410b734220751e7c314b6420650970387d1e5319743e6f3a793f4e3b700930086f29710534034f077f147c3e4c024f2c6512430a4e0f5e1f5d\""
        }

        release {
            buildConfigField "String", "signture", "\"#E DvE&DtC'\u0014-\u0019/M.J,M}\u0019.K~\u001Fy\u001D/JzN\""
            buildConfigField "String", "flytekUrl", '"z\\u000Ez\\nyClC4C4\\u001Ab\\u0004}\\bfH+Ej\\u000Ea\\u0002-]2^7T-\\u0002r\\u0000i\\u001F~\\u001DdJ\\"V;W"'

            buildConfigField "String", "enbackUrl", "\"7a0e7a0a79436c432256394d60137604721765482b456b0a660a6b1c720663006846254a27087e4f\""
            buildConfigField "String", "sec", "\"2a4e77432743731024122a4e7a4e781e26472217744725117446751370142515\""
            buildConfigField "String", "pub", "\"5f165f1d543e7f3173147f0e660d64231a6d5d1f5e0f4a0c4d0c430041102869246d24662542094a0b5a1f5e6c3c4f0370482b1a680e7619284122660461345a3b59695e1a570160241c762e784019286e086f2b793e0e3972440563194c0a491a4d0471136b1d5a1e643c0b6d035b377e4b0e5c39610f63172171365561235b11703c73065c6d5b095c3d6d28650c45374f1b51033b7e197b1060105c047e2c451527431972451566256910576f21685a394f2217650922670a623571400c7a1d4a077e1753264271027714583566236a0d394f3a4f03440a7f3002532157653047107f195e6e45725929115b7439402e476c1443750f4c34600a46084432512360364e16217330410676027a1b7c3f083d0a6e20591b50386f227817254e79085004314201420e7d0a650e655002771670247e0a72204477426d5b2d74115e0c5a1270064b117a49007303625513410b734220751e7c314b6420650970387d1e5319743e6f3a793f4e3b700930086f29710534034f077f147c3e4c024f2c6512430a4e0f5e1f5d\""

            //config enable proGuard
            minifyEnabled true
            //config enable shrink unused resources
            shrinkResources true
            //proGuard rules files
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            zipAlignEnabled true
        }

        oapm {
            initWith(buildTypes.release)
            matchingFallbacks = ['release']
            minifyEnabled true
            shrinkResources true
            //不能直接copy，每个项目的混淆配置文件，可能命名不一样
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }

        coverage {
            //按照 release 的配置构建
            initWith(buildTypes.release)
            //其他模块没有配置 coverage 的情况下默认 release
            matchingFallbacks = ['release']
            //避免混淆，以免影响覆盖率执行率
            minifyEnabled false
            shrinkResources false
            //此处影响流水线出包
            manifestPlaceholders = [ area:"" ]
        }
    }

    //apk打包压缩
    packagingOptions {
        dex.useLegacyPackaging true
        //manifest的android:extractNativeLibs="true"
        jniLibs.useLegacyPackaging true
        resources {
            excludes += ['META-INF/*.version']
        }
    }

    flavorDimensions "B", "P", "region", "apilevel"
    productFlavors {
        //apk名称需要重命名为：“OPPO”
        oppo {
            dimension "B"
        }
        //apk名称需要重命名为：“OnePlus”
        oneplus {
            dimension "B"
        }
        domestic {
            dimension "region"
        }
        export {
            dimension "region"
            //默认情况，只编译核心语言，但是服务器编译，会动态修改，编译外销需要的所有语言,如没有配置，会是全语言
            if (prop_disableSubPackage.toBoolean()) {
                println("app disable resource subpacakge")
            } else {
                if (!prop_exp_resConfig.toString().isEmpty()) {
                    resConfigs prop_exp_resConfig
                } else {
                    println("subpacakge config is empty, no subpackage")
                }
            }
        }
        //apk名称需要重命名为：“GDPR”
        gdpr {
            dimension "region"
            //默认情况，只编译核心语言，但是服务器编译，会动态修改，编译外销需要的所有语言,如没有配置，会是全语言
            if (prop_disableSubPackage.toBoolean()) {
                println("app disable resource subpacakge")
            } else {
                if (!prop_exp_resConfig.toString().isEmpty()) {
                    resConfigs prop_exp_resConfig
                } else {
                    println("subpacakge config is empty, no subpackage")
                }
            }
        }
        aall {
            dimension "apilevel"
        }
        pall {
            dimension "P"
        }
    }

    sourceSets {
        main {
            aidl.srcDirs = ['src/main/java']
            renderscript.srcDirs = ['src/main/java']
            res.srcDirs = ['src/main/res', '../res-strings']
//            assets.srcDirs = ['../assets']

            //if apply plugin: 'oppo-native-dependencies' and libs as the default jniLibs folder,libs will be clean up
            jniLibs.srcDirs = ['libs']
        }
        oneplus {
            res.srcDirs = ['src/main/res-oneplus']
            java.srcDirs = ['src/main/build-types/oneplus/src']
        }
        oppo {
            java.srcDirs = ['src/main/build-types/main/src']
        }
        oapm {
            java.srcDirs = ['src/main/build-types/main/src']
        }
        // Move the tests to tests/java, tests/res, etc...
        //androidTest.setRoot('tests')
        domestic {
            java.srcDirs = ['src/main/build-types/domestic/src']
            assets.srcDirs = ['src/main/build-types/domestic/assets']
            jniLibs.srcDirs = ['src/main/build-types/domestic/libs']
            res.srcDirs += ['../res-strings-domestic']
        }

        export {
            java.srcDirs = ['src/main/build-types/export/src']
            assets.srcDirs = ['src/main/build-types/export/assets']
            jniLibs.srcDirs = ['src/main/build-types/export/libs']
            res.srcDirs += ['../res-strings-export']
        }

        gdpr {
            java.srcDirs = ['src/main/build-types/export/src']
            assets.srcDirs = ['src/main/build-types/export/assets']
            jniLibs.srcDirs = ['src/main/build-types/export/libs']
        }

        androidTest.setRoot('src/androidTest')
        androidTest.jniLibs.srcDirs = ['src/androidTest/libs']
        androidTest.resources.srcDirs = ['src/androidTest/res']
        test.setRoot('src/test')
    }

    configurations {
        oneplusPallDomesticAallImplementation
        oneplusPallExportAallImplementation
        oneplusPallGdprAallImplementation
    }


    compileOptions {
        encoding 'UTF-8'
        sourceCompatibility prop_targetCompatibility
        targetCompatibility prop_targetCompatibility
    }
}

android.applicationVariants.configureEach { variant ->
    def name = variant.name.toLowerCase()
    def mergeFlavor = variant.mergedFlavor

    def applicationId = prop_applicationId_oppo
    if (name.contains("oneplus")) {
        // 一加外销使用录音机包名
        if ((name.contains("export")) || (name.contains("gdpr"))) {
            applicationId = prop_applicationId_oneplus
            variant.outputs.each { output ->
                def overrideVersion = "3" + mainVersionCode.substring(1)
                println("$name override version:" + mainVersionCode + "->" + overrideVersion)
                output.versionCodeOverride = overrideVersion.toInteger()
            }
        }
    }
    mergeFlavor.setApplicationId(applicationId)
}

// 如果不配置osign 则对应参数使用默认值
osign {
    brand = 'PSW'  // 默认值 PSW
    project = '20111'  // 默认值 Oplus_prj
    platform = 'MSM1902_11.0'  // 默认值 Oplus_key
    signType = 'oppo_data_app_std'  // 默认值 oplus_app
    signVersion = 'V3_single'  // 默认值 V3_single
}

OApmConfig {
    defaultConfig {
        autoDependencies {
            variantFilters 'oapmImplementation'
        }
        logLevel 5
        iconEnabled true
        soFilters 'arm64-v8a'
    }
    //Monitor startup speed
    startupSpeed {
        //startup speed switch, default is true
        enabled true
        //1 Activity 2 Service 3 Self-Defined
        type 1
        //set startup page
        launchActivity 'com.soundrecorder.browsefile.BrowseFile'
    }
}

OBuildConfig {
    outputType = "aab,apk"
    //不能直接复制，需要根据应用的渠道配置，分不同的编译组。
    buildTask = "Oppo,Oneplus"
    codeScanVariants = "OppoPallDomesticAallDebug"
    //接口测试的变体，一般是oppo内销最新版本单变体即可
    androidTestVariants = "OppoPallDomesticAallDebug"
    //单元测试的变体，一般是oppo内销最新版本单变体即可
    unitTestVariants = "OppoPallDomesticAallDebug"
}

android.sourceSets.all { sourceSet ->
    if (sourceSet.name.contains('androidTest')) {
        return
    }
    def manifestXml = 'src/main/manifest/coloros/AndroidManifest.xml'
    def name = sourceSet.name.toLowerCase()
    if (name.contains('oneplus')) {
        // 一加外销单独设置androidmanifest
        if ((name.contains('export')) || (name.contains('gdpr'))) {
            manifestXml = 'src/main/manifest/oneplus/AndroidManifest.xml'
        }
    }
    sourceSet.manifest.srcFile manifestXml
}

//有些版本编译没有意义，这里过滤掉，提升编译速度
//这段代码不能直接抄，需要自己思考
android.variantFilter { variant ->
    def buildTypeName = variant.buildType.name
    def flavor = variant.getFlavors().name
    def fullFlavorName = flavor + ":" + buildTypeName

    //只输出oapm OPPO全量版本
    if (fullFlavorName.contains("oapm")) {
        if (!fullFlavorName.contains("oppo")) {
            variant.setIgnore(true)
        }
    }
    //其他可以忽略的变体，应用自己梳理

    //只输出 coverage 品牌内销版本
    if (fullFlavorName.contains("coverage")) {
        //如果没有轻量os，需要删除full判断；如果没有版本裂化，需要删除aall判断的
        if (!fullFlavorName.contains("oppo") || !fullFlavorName.contains("domestic")) {
            variant.setIgnore(true)
        }
    }
}

dependencies {
    implementation fileTree(include: ['*.so'], dir: 'libs')
    //原生————————————————————————————————————————————————————————————————————————————
    implementation libs.androidx.support
    implementation libs.androidx.appcompat

    // base包为必须引用的包，prop_versionName需保持一致
    implementation (libs.oplus.coui.core) {
        exclude group: 'com.squareup.okio', module: 'okio'
    }
    implementation libs.oplus.coui.calendar

    implementation(libs.oplus.material)
    // fragment
    implementation libs.androidx.fragment.ktx
    //kotlin
    implementation libs.org.kotlin.stdlib
    implementation libs.androidx.lifecycle.livedata
    implementation libs.androidx.lifecycle.viewmodel
    implementation libs.androidx.lifecycle.extensions

    implementation libs.oplus.coui.recyclerview
    implementation libs.gson
    //内部依赖————————————————————————————————————————————————————————————————————————————
    //opendId sdk
    implementation libs.oplus.stdid.sdk
    //add for AES\RSA
    implementation libs.commons.codec
    //品牌差异------------------------------------------
    // 一加外销版本则需要使用纯净版
    //api-adapter-oplus 发布版本至少 OS11.3
    oppoImplementation libs.oplus.support.adapter
    oneplusPallDomesticAallImplementation libs.oplus.support.adapter
    oneplusPallExportAallImplementation libs.oplus.support.adapter.plus
    oneplusPallGdprAallImplementation libs.oplus.support.adapter.plus

    compileOnly libs.oplus.addon
    testImplementation libs.oplus.addon

    // 一加外销版本则需要使用纯净版
    oppoImplementation project(':component:move')
    oneplusPallDomesticAallImplementation project(':component:move')
    oneplusPallExportAallImplementation project(':component:movepure')
    oneplusPallGdprAallImplementation project(':component:movepure')

    // Koin for Android
    implementation(libs.koin)

    implementation project(':component:recorderService')
    implementation project(':component:Questionnaire')
    domesticImplementation project(':common:RecorderLogX')
    domesticImplementation project(':component:cloudkit')
    domesticImplementation project(':component:ConvertService')
    implementation project(':component:recorderService')
    implementation project(':component:summary')
    exportImplementation project(':component:cloudkit')
    testImplementation project(':component:cloudkit')
    androidTestImplementation project(':component:cloudkit')
    implementation project(':common:libcommon')
    implementation project(':common:libbase')
    implementation project(':common:libimageload')
    implementation project(':common:modulerouter')
    implementation project(':common:RecorderLogBase')
    implementation project(':component:player')
    implementation project(':component:notification')
    implementation project(':component:wavemark')
    implementation project(':component:PhotoViewer')
    implementation project(':component:privacypolicy')
    implementation project(':component:sellMode')
    implementation project(':component:share')
    implementation project(':component:semantic-ssa:impl')
    implementation project(':component:semantic-ssa:interface')
    implementation project(':page:setting')
    implementation project(':page:browsefile')
    implementation project(':page:playback')
    implementation project(':page:editRecord')
    implementation project(':page:record')
    implementation project(':page:card')
    implementation project(':page:miniapp')

    coverageImplementation 'otestPlatform:coverageLibDisk:2.1.6@aar'
    oapmImplementation 'otestPlatform:coverageLibDisk:2.1.6@aar'
}
