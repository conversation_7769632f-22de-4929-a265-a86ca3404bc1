/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  AutoDiForHome.kt
 * * Description : AutoDiForHome
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package oplus.multimedia.soundrecorder.di

import com.soundrecorder.modulerouter.HomeInterface
import oplus.multimedia.soundrecorder.api.HomeApi
import org.koin.dsl.module

object AutoDiForHome {

    val homeModule = module {
        single<HomeInterface>(createdAtStart = true) {
            HomeApi
        }
    }
}