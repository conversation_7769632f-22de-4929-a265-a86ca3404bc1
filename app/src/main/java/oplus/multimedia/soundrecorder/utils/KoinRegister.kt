/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : KoinRegister
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/6/11 10:33
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  keweiwei        2024/6/11       1.0      create
 ***********************************************************************/
package oplus.multimedia.soundrecorder.utils

import android.util.Log
import com.oplus.soundrecorder.semantic.ssa.di.AutoDiForSemantic
import com.photoviewer.di.AutoDiForPhotoViewer
import com.soundrecorder.browsefile.di.AutoDiForBrowse
import com.soundrecorder.common.card.di.AutoDiForCard
import com.soundrecorder.common.card.di.AutoDiForSeeding
import com.soundrecorder.common.di.AutoDiForCommon
import com.soundrecorder.editrecord.di.AutoDiForEditRecord
import com.soundrecorder.miniapp.di.AutoDiForMiniApp
import com.soundrecorder.notification.di.AutoDiForNotification
import com.soundrecorder.playback.di.AutoDiForPlayBack
import com.soundrecorder.privacypolicy.di.AutoDiForPrivacyPolicy
import com.soundrecorder.record.di.AutoDiForRecord
import com.soundrecorder.recorderservice.di.AutoDiForRecorderService
import com.soundrecorder.sellmode.di.AutoDiForSellMode
import com.soundrecorder.setting.di.AutoDiForSetting
import com.soundrecorder.share.di.AutoDiForShare
import com.soundrecorder.summary.di.AutoDiForSummary
import com.soundrecorder.wavemark.di.AutoDiForWaveMark
import oplus.multimedia.soundrecorder.di.AutoDiForAppCard
import oplus.multimedia.soundrecorder.di.AutoDiForHome
import org.koin.core.module.Module
import java.lang.reflect.Field

object KoinRegister {

    private const val TAG = "KoinRegister"

    /**
     * 全渠道的module注册
     */
    private val MODULE_OMNICHANNEL_REGISTER = mutableListOf(
        AutoDiForHome.homeModule,
        AutoDiForAppCard.appCardModule,
        AutoDiForCard.cardModule,
        AutoDiForBrowse.browseModule,
        AutoDiForEditRecord.editRecordModule,
        AutoDiForSeeding.seedingModule,
        AutoDiForSetting.settingModule,
        AutoDiForWaveMark.waveMarkModule,
        AutoDiForRecorderService.recorderServiceModule,
        AutoDiForMiniApp.miniAppModule,
        AutoDiForNotification.notificationModule,
        AutoDiForPhotoViewer.photoViewerModule,
        AutoDiForPlayBack.playBackModule,
        AutoDiForPrivacyPolicy.privacyPolicyModule,
        AutoDiForSellMode.sellModeModule,
        AutoDiForSummary.summaryModule,
        AutoDiForRecord.recordModule,
        AutoDiForShare.shareModule,
        AutoDiForCommon.commonModule,
        AutoDiForSemantic.semanticModule
    )

    /**
     * 分渠道的注册，要利用反射，对应的类必须不进行混淆
     */
    private val MODULE_DIVIDED_CHANNELS_REGISTER = HashMap<String, String>().apply {
        put("com.soundrecorder.convertservice.di.AutoDiForConvertService", "convertServiceModule")
        put("com.oplus.recorderlogx.di.AutoDiForRecorderLog", "logModule")
        put("com.recorder.cloudkit.di.AutoDiForCloudKit", "cloudKitModule")
        put("com.oplus.recorder.questionnaire.di.AutoDiForQuestionnaire", "questionnaireModule")
        put("com.oplus.recorder.feedback.di.AutoDiForFeedBack", "feedBackModule")
        put("com.soundrecorder.translate.di.AutoDiForTranslate", "translateModule")
    }

    @JvmStatic
    fun getKoinModules(): List<Module> {
        val modules: MutableList<Module> = mutableListOf()
        //1,先添加全渠道module
        modules.addAll(MODULE_OMNICHANNEL_REGISTER)

        //2,根据反射获取分渠道module
        MODULE_DIVIDED_CHANNELS_REGISTER.forEach { moduleRegister ->
            val className = moduleRegister.key
            val moduleName = moduleRegister.value

            val module = runCatching {
                val clazz = Class.forName(className)
                val instance = clazz.getDeclaredConstructor().newInstance()
                val api: Field? = clazz.getDeclaredField(moduleName)
                api?.isAccessible = true
                api?.get(instance) as? Module
            }.onFailure { error ->
                Log.i(TAG, error.message.toString())
                error.printStackTrace()
            }.getOrDefault(null)
            Log.i(TAG, "module = $module, className = $className")
            module?.let {
                modules.add(module)
            }
        }
        return modules
    }
}
