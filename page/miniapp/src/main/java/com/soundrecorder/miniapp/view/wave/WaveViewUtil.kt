/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: WaveViewUtil
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: W9013204(v-wangyin<PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013204 2022/8/29 1.0 create
 */
package com.soundrecorder.miniapp.view.wave

import android.content.Context
import android.view.animation.PathInterpolator
import com.soundrecorder.miniapp.R
import kotlin.math.PI
import kotlin.math.pow

internal object WaveViewUtil {
    const val DEFAULT_SCROLL_X = -1
    const val DURATION_560 = 560
    const val DURATION_6 = 6
    const val DURATION_267 = 267
    const val DURATION_283 = 283
    const val INT_2 = 2
    const val INT_3 = 3
    const val INT_4 = 4
    const val INT_8 = 8
    const val INT_10 = 10
    const val INT_100 = 100

    const val FLOAT_32768 = 32768f
    const val FLOAT_0_5 = 0.5f

    const val MAX_SCALE_HEIGHT = 0.35f
    const val MID_SCALE_HEIGHT = 0.25f
    const val MIN_SCALE_HEIGHT = 0.1f
    private const val FLOAT_0_7 = 0.7f
    private const val FLOAT_1_5 = 1.5f
    private const val FLOAT_2 = 2f
    private const val FLOAT_3_3 = 3.3f
    private const val FLOAT_0_51 = 0.51f
    private const val FLOAT_0_52 = 0.52f
    private const val FLOAT_0_03 = 0.03f
    private const val FLOAT_1_02 = 1.02f
    private const val FLOAT_0_48 = 0.48f
    private const val FLOAT_0_02 = 0.02f
    private const val FLOAT_0_33 = 0.33f
    private val upInterpolator = PathInterpolator(FLOAT_0_51, FLOAT_0_03, FLOAT_0_52, FLOAT_1_02)
    private val downInterpolator = PathInterpolator(FLOAT_0_48, FLOAT_0_02, FLOAT_0_33, 1f)
    private var oneWaveItemWidth: Float? = null

    /**
     * 波形寬度必須是3的倍數
     */
    @JvmStatic
    fun Context.getOneWaveWidth(): Float {
        return oneWaveItemWidth ?: (resources.getDimension(R.dimen.px6)
            .apply { oneWaveItemWidth = this })
    }

    /**
     * 波形高度差值計算方式
     */
    @JvmStatic
    fun getLineScale(percent: Float): Float {
        return (FLOAT_2.pow(-INT_10 * percent) * kotlin.math.sin((percent - FLOAT_0_7 / FLOAT_3_3) * (FLOAT_1_5 * PI) / FLOAT_0_7) + 1).toFloat()
    }

    /**
     * 根据当前执行动画的时间决定当前波形的高度 默认高度 - 最大值 - 默认高度
     */

    @JvmStatic
    fun getEnterHeightByTime(time: Long, defaultHeight: Float, changeHeight: Float): Float {
        val dScale = if (time < DURATION_267) {
            upInterpolator.getInterpolation(time.toFloat() / DURATION_267)
        } else if (time < DURATION_267 + DURATION_283) {
            1f - downInterpolator.getInterpolation((time.toFloat() - DURATION_267) / DURATION_283)
        } else {
            0f
        }
        return (defaultHeight + dScale * changeHeight)
    }
}