<?xml version="1.0" encoding="utf-8"?>
<com.soundrecorder.playback.view.SegmentSeekBar xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/seek_bar"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:focusable="true"
    android:layoutDirection="locale"
    android:visibility="gone"
    app:couiSeekBarBackGroundEnlargeScale="6"
    app:couiSeekBarProgressPaddingHorizontal="@dimen/dp16"
    app:couiSeekBarBackgroundHeight="@dimen/dp4"
    app:couiSeekBarMaxWidth="@dimen/max_seekbar_width"
    app:couiSeekBarProgressColor="@color/seek_bar_progress"
    app:thumbBordersColor="@color/seek_bar_thumb_border"
    app:thumbBordersSize="@dimen/dp2"
    app:couiSeekBarThumbShadowSize="@dimen/dp2"
    app:couiSeekBarShadowColor="@color/seek_bar_thumb_shadow"
    app:couiSeekBarProgressHeight="@dimen/dp4"
    app:couiSeekBarThumbColor="@color/seek_bar_thumb" />