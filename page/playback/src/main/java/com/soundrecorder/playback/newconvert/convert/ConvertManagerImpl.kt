/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.convert


import android.content.DialogInterface
import android.content.Intent
import android.content.res.Configuration
import android.view.View
import androidx.annotation.StringRes
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStoreOwner
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.isFlexibleWindow
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.common.base.PrivacyPolicyBaseActivity
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.common.flexible.FollowDialogRestoreUtils
import com.soundrecorder.common.flexible.FollowRestoreCallBack
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.common.utils.ViewUtils
import com.soundrecorder.convertservice.ConvertSupportManager
import com.soundrecorder.modulerouter.BrowseFileInterface
import com.soundrecorder.modulerouter.privacyPolicy.IFunctionPrivacyCallback
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.FUNC_TYPE_SMART_SHORTHAND
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyInterface
import com.soundrecorder.modulerouter.smartname.SmartNameAction
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.playback.PlaybackActivityViewModel
import com.soundrecorder.playback.cloudconfig.CloudConfigUtils
import com.soundrecorder.playback.convert.IConvertManager
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel
import com.soundrecorder.playback.newconvert.exportconvert.ExportHelper
import com.soundrecorder.playback.newconvert.exportconvert.txt.ShareWithTxtActivity
import com.soundrecorder.playback.newconvert.keyword.ExtractKeyWordManager
import com.soundrecorder.playback.newconvert.ui.ConvertViewContainer
import java.util.function.Supplier

class ConvertManagerImpl : IConvertManager, ExportHelper.ExportHelperListener {

    companion object {
        private const val TAG = "ConvertManagerImpl"
    }

    var activity: AppCompatActivity? = null
    var lifecycleOwner: LifecycleOwner? = null
    var playbackConvertViewModel: PlaybackConvertViewModel? = null
    var mExportHelper: ExportHelper? = null
    private var convertServiceManager: ConvertServiceManager? = null
    private var extractKeyWordManager: ExtractKeyWordManager? = null

    private var mConvertViewController: ConvertViewController? = null
    private var mUserTimeOutDialog: AlertDialog? = null
    private var mRecordDuration: Long? = null
    private var convertAbility: Int? = null
    private val browseFileAction by lazy {
        Injector.injectFactory<BrowseFileInterface>()
    }

    override fun release() {
        mConvertViewController?.mRenameSpeakerDialog?.dismiss()
        mConvertViewController?.mRenameSpeakerDialog = null
        mConvertViewController?.mContinueTranDialog?.dismiss()
        mConvertViewController?.mContinueTranDialog = null
        mConvertViewController?.release()
        mConvertViewController = null
        mExportHelper?.release()
        mExportHelper = null
        convertServiceManager?.release()
        convertServiceManager = null
        mUserTimeOutDialog?.dismiss()
        mUserTimeOutDialog = null
        activity = null
        lifecycleOwner = null

        extractKeyWordManager?.release()
        extractKeyWordManager = null
        playbackConvertViewModel = null
        mRecordDuration = null
    }

    override fun releaseView() {
        FollowDialogRestoreUtils.releaseFollowDialogRunnable(activity?.window?.decorView)
    }

    override fun releaseAnimView() {
        DebugUtil.d(TAG, "releaseAnimView")
        mConvertViewController?.cancelConvertAnim()
    }

    override fun register(viewModelStoreOwner: Fragment, rootView: View, mediaId: Long, convertAbility: Int) {
        if (convertAbility == ConvertSupportManager.CONVERT_DISABLE) return
        this.convertAbility = convertAbility
        this.lifecycleOwner = viewModelStoreOwner
        this.activity = viewModelStoreOwner.requireActivity() as AppCompatActivity
        initViewModel(viewModelStoreOwner, mediaId)
        initServiceManager()
        initViews(rootView)
        initViewModelObservers(viewModelStoreOwner)
        convertServiceManager?.startConvertTextService(activity)
        convertServiceManager?.bindConvertTextService(activity, mediaId)
    }

    override fun setExportMenuItem() {
        mExportHelper = ExportHelper(this, this)
    }

    override fun convertStartClickHandle(convertAiTitle: Boolean) {
        if (!BaseUtil.isEXP() && !PermissionUtils.isStatementConvertGranted(BaseApplication.getAppContext())) {
            showStatementWithFirstUseConvert { agree ->
                DebugUtil.i(TAG, "convertStartClickHandle agree $agree")
                if (agree) {
                    convertServiceManager?.startConvertTextService(activity)
                    convertServiceManager?.startOrResumeConvert(convertAiTitle)
                }
            }
        } else {
            convertServiceManager?.startConvertTextService(activity)
            convertServiceManager?.startOrResumeConvert(convertAiTitle)
        }
    }

    private fun initViews(rootView: View) {
        val convertViewContainer: ConvertViewContainer? = rootView.findViewById(com.soundrecorder.common.R.id.convert_view_container)
        mConvertViewController = ConvertViewController(activity, convertViewContainer, lifecycleOwner)
        mConvertViewController?.setConvertViewModel(playbackConvertViewModel)
        mConvertViewController?.onConvertStartClickListener = object : ConvertViewController.OnConvertStartClickListener {
            override fun onClick() {
                convertStartClickHandle(false)
            }
        }

        mConvertViewController?.keyWordSupplier = object : Supplier<Boolean> {
            override fun get(): Boolean {
                // 首次进入，弹出网络权限声明
                if (!PermissionUtils.isStatementConvertGranted(BaseApplication.getAppContext())) {
                    showStatementWithFirstUseExtract(true)
                    return false
                } else {
                    val recordId = playbackConvertViewModel?.mediaRecordId ?: return false
                    return extractKeyWordManager?.extractKeyWord(recordId) ?: false
                }
            }
        }
    }

    override fun setViewModel(viewModel: PlaybackActivityViewModel?) {
        mConvertViewController?.setViewModel(viewModel)
        DebugUtil.i(TAG, "setViewModel markReadReadyCallback")
        viewModel?.markReadReadyCallback = playbackConvertViewModel
    }

    private fun initViewModel(viewModelStoreOwner: ViewModelStoreOwner, mediaId: Long) {
        if (playbackConvertViewModel == null) {
            playbackConvertViewModel = ViewModelProvider(viewModelStoreOwner)[PlaybackConvertViewModel::class.java]
        }
        DebugUtil.i(TAG, "initViewModel: $playbackConvertViewModel")
        playbackConvertViewModel?.setMediaRecordId(mediaId)
    }

    private fun initServiceManager() {
        if (convertServiceManager == null) {
            convertServiceManager = ConvertServiceManager(playbackConvertViewModel, convertAbility)
            playbackConvertViewModel?.mConvertLoadedCallback = convertServiceManager
            extractKeyWordManager = playbackConvertViewModel?.let { ExtractKeyWordManager(it) }
        }
    }

    private fun initViewModelObservers(viewModelStoreOwner: Fragment) {
        if (this.activity == null) {
            return
        }
        observeConvertStatus(viewModelStoreOwner)
        observeConvertProgress(viewModelStoreOwner)
        playbackConvertViewModel?.mShowSwitchInConvertModel?.observe(viewModelStoreOwner) {
            mConvertViewController?.postShowSwitch(playbackConvertViewModel?.mShowSwitchInConvertModel?.value == true)
        }
        playbackConvertViewModel?.mOriginalRoleNumber?.observe(viewModelStoreOwner) {
            mConvertViewController?.postRoleNumber(
                playbackConvertViewModel?.mOriginalRoleNumber?.value ?: 0
            )
        }
        // 关键词
        playbackConvertViewModel?.keyWords?.observe(viewModelStoreOwner) {
            if (FunctionOption.IS_SUPPORT_EXTRACT_KEY_WORDS) {
                mConvertViewController?.setKeyWords(it)
            }
        }
    }

    private fun observeConvertStatus(viewModelStoreOwner: Fragment) {
        playbackConvertViewModel?.mConvertStatus?.observe(viewModelStoreOwner) {
            DebugUtil.i(TAG, "convertViewModelChange mConvertStatus changed: $it")
            when (it) {
                PlaybackConvertViewModel.CONVERT_STATUS_INIT -> {
                    updateCancelConvertUI()
                    isNeedStartTrans()
                }

                PlaybackConvertViewModel.CONVERT_STATUS_CANCEL -> {
                    updateCancelConvertUI(true)
                    isNeedStartTrans()
                }

                PlaybackConvertViewModel.CONVERT_STATUS_COMPLETE -> updateConvertCompletedUI(playbackConvertViewModel?.convertContentData)
                PlaybackConvertViewModel.CONVERT_STATUS_SUMMARY_NONE_COMPLETE -> updateCancelConvertUI()
                PlaybackConvertViewModel.CONVERT_STATUS_PROGRESS -> attachConvertingView()
                PlaybackConvertViewModel.CONVERT_STATUS_USERTIMEOUT -> {
                    showUserTimeOutDialog()
                    playbackConvertViewModel?.mConvertStatus?.value = PlaybackConvertViewModel.CONVERT_STATUS_INIT
                }
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        DebugUtil.d(TAG, "onConfigurationChanged, config:$newConfig")
        if (FeatureOption.IS_PAD && isFlexibleWindow(activity)) {
            refreshConvertInitStatus()
        }
    }

    override fun onSmartNameStatusChange(display: Boolean?, resultName: String?) {
        val isSmartNaming = display ?: playbackConvertViewModel?.mediaRecordId?.let { browseFileAction?.isSmartNaming(it) }
        mConvertViewController?.updateSmartNameStatus(isSmartNaming, resultName)
    }

    /**
     * 平板且处于浮窗，配置变更并且处于转文本初始页面时，刷新初始页面
     */
    fun refreshConvertInitStatus() {
        val convertStatus = playbackConvertViewModel?.mConvertStatus?.value
        if (convertStatus == PlaybackConvertViewModel.CONVERT_STATUS_INIT
            || convertStatus == PlaybackConvertViewModel.CONVERT_STATUS_CANCEL
        ) {
            updateCancelConvertUI()
            isNeedStartTrans()
        }
    }

    private fun observeConvertProgress(viewModelStoreOwner: Fragment) {
        playbackConvertViewModel?.mConvertProgress?.observe(
            viewModelStoreOwner,
            androidx.lifecycle.Observer<Int> {
                DebugUtil.i(TAG, "convertViewModelChange mConvertProgress changed: $it")
                if (it > 0) {
                    mConvertViewController?.updateProgress(
                        it,
                        playbackConvertViewModel!!.mServerPlanCode
                    )
                }
            })
    }

    private fun isNeedStartTrans() {
        if (playbackConvertViewModel?.mNeedTransConvert == true) {
            convertStartClickHandle()
            playbackConvertViewModel?.mNeedTransConvert = false
        }
    }

    override fun cancelConvert(@StringRes resId: Int) {
        convertServiceManager?.cancelConvert(playbackConvertViewModel?.mediaRecordId ?: -1)
    }

    override fun update(currentTimeMillis: Long) {
        mConvertViewController?.update(currentTimeMillis)
    }

    override fun isContentExpanded(): Boolean {
        return false
    }

    override fun setNeedShowAnimView(isNeedShowAnimView: Boolean) {
        DebugUtil.i(TAG, "======setNeedShowAnimView: isNeedShowAnimView = $isNeedShowAnimView")
        mConvertViewController?.setIsNeedShowAnimView(isNeedShowAnimView)
    }

    private fun attachConvertingView() {
        mConvertViewController?.updateProgress(0)
        mConvertViewController?.onConvertCancelClickListener =
            object : ConvertViewController.OnConvertCancelClickListener {
                override fun onClick() {
                    DebugUtil.i(TAG, "<<< onConvertCancelClick")
                    cancelConvert(com.soundrecorder.common.R.string.convert_text_stopped)
                }
            }
        mConvertViewController?.switchConvertingView()
    }

    private fun updateCancelConvertUI(needAnimation: Boolean = false) {
        DebugUtil.i(TAG, "<<< updateCancelConvertUI, needAnimation = $needAnimation")
        mConvertViewController?.switchConvertInitView(needAnimation)
    }

    private fun updateConvertCompletedUI(contents: List<ConvertContentItem>?) {
        DebugUtil.d(TAG, "======= updateConvertCompletedUI, contents size = ${contents?.size} =======")
        if (contents.isNullOrEmpty()) {
            mConvertViewController?.switchConvertEmptyView()
        } else {
            //这里不需要设置了，在转文本成功或从数据库加载完毕之后已经设置过
            mConvertViewController?.switchConvertContentView()
        }
        convertServiceManager?.unbindConvertService(activity)
    }

    /**
     * 初次使用提取关键词，需要显示声明弹窗
     */
    private fun showStatementWithFirstUseExtract(forceShow: Boolean = false) {
        (activity as? PrivacyPolicyBaseActivity)?.apply {
            lifecycleScope.launchWhenResumed {
                /*
                    此方法必须在onResume中执行，onRestoreInstanceState重建机制在onResume之前执行
                 */
                if (!PermissionUtils.isStatementConvertGranted(BaseApplication.getAppContext())) {
                    getPrivacyPolicyDelegate()?.resumeShowDialog(
                        PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_SEARCH, forceShow,
                        PrivacyPolicyConstant.PAGE_FROM_PLAYBACK
                    )
                }
            }
        }
    }

    /**
     * 初次使用转文本，需要显示声明弹窗
     */
    private fun showStatementWithFirstUseConvert(callback: ((agree: Boolean) -> Unit)) {
        (activity as? PrivacyPolicyBaseActivity)?.let {
            it.lifecycleScope.launchWhenResumed {
                val hasFunction = PermissionUtils.hasFuncTypePermission(FUNC_TYPE_SMART_SHORTHAND)
                DebugUtil.d(TAG, "showStatementWithFirstUseConvert hasFunction = $hasFunction")
                if (hasFunction.not()) {
                    val privacyAction = Injector.injectFactory<PrivacyPolicyInterface>()
                    val functionPrivacy = privacyAction?.newFunctionPrivacyDelegate(FUNC_TYPE_SMART_SHORTHAND) ?: run {
                        callback.invoke(false)
                        return@launchWhenResumed
                    }

                    val smartNameAction = Injector.injectFactory<SmartNameAction>()
                    functionPrivacy.showFunctionPrivacyDialog(it, object : IFunctionPrivacyCallback {
                        override fun onPrivacyAgreed() {
                            callback.invoke(true)
                            smartNameAction?.setSmartNameSwitchStatus(BaseApplication.getAppContext(), true, false)
                        }

                        override fun onPrivacyRejected() {
                            smartNameAction?.setSmartNameSwitchStatus(BaseApplication.getAppContext(), false, false)
                            callback.invoke(false)
                        }
                    })
                } else {
                    callback.invoke(true)
                }
            }
        } ?: run {
            callback.invoke(false)
        }
    }

    override fun doClickPermissionConvertOK() {
        convertServiceManager?.startConvertTextService(activity)
        convertServiceManager?.startOrResumeConvert(false)
        PermissionUtils.setConvertGrantedStatus(BaseApplication.getAppContext())
        PermissionUtils.setNetWorkGrantedStatus(BaseApplication.getAppContext(), true)
        CloudConfigUtils.updateConvertConfig()
    }

    override fun doClickPermissionConvertSearchOK() {
        val recordId = playbackConvertViewModel?.mediaRecordId ?: return
        extractKeyWordManager?.extractKeyWord(recordId)
    }

    private fun showUserTimeOutDialog() {
        DebugUtil.e(TAG, "showUserTimeOutDialog show")
        if (mUserTimeOutDialog?.isShowing == true) {
            return
        }
        val message = activity?.resources?.getQuantityString(
            com.soundrecorder.common.R.plurals.quota_used_up_msg_v2, playbackConvertViewModel?.mUserConvertTimePerDay?.toIntOrNull()
                ?: 2, playbackConvertViewModel?.mUserConvertTimePerDay?.toIntOrNull() ?: 2
        )
        val titleString = activity?.resources?.getString(com.soundrecorder.common.R.string.quota_used_up_title)
        val positive = activity?.resources?.getString(com.soundrecorder.common.R.string.button_ok)
        val builder = COUIAlertDialogBuilder(activity!!)
        builder.setBlurBackgroundDrawable(true)
        builder.setTitle(titleString)
        builder.setMessage(message)
        builder.setPositiveButton(positive) { dialog, which ->
            {}
        }
        mUserTimeOutDialog = builder.create()
        mUserTimeOutDialog?.setOnCancelListener {
        }
        mUserTimeOutDialog?.setCanceledOnTouchOutside(false)
        mUserTimeOutDialog?.show()
        ViewUtils.updateWindowLayoutParams(mUserTimeOutDialog?.window)
    }

    override fun getConvertViewController(): ConvertViewController? {
        return mConvertViewController
    }

    override fun roleControl(needShowRole: Boolean) {
        mConvertViewController?.roleControl(needShowRole, false)
    }

    override fun stopScroll() {
        mConvertViewController?.stopScroll()
    }

    override fun addSpeakerTipTask(isOnConvertWhenViewPager2IDLE: () -> Boolean) {
        mConvertViewController?.addSpeakerTipTask(isOnConvertWhenViewPager2IDLE)
    }

    override fun removeSpeakerTipTask() {
        mConvertViewController?.removeSpeakerTipTask()
    }

    override fun updatePlayName() {
        mConvertViewController?.updatePlayName()
    }

    override fun processExport(anchor: View?, dismissListener: DialogInterface.OnDismissListener?) {
        DebugUtil.d(TAG, "processExport show")
        mExportHelper?.showExportDialog(anchor, dismissListener)
    }

    override fun processExport2(
        anchor: View?,
        dismissListener: DialogInterface.OnDismissListener?,
        childFragmentManager: FragmentManager?
    ) {
        DebugUtil.d(TAG, "processExport2 mExportHelper = $mExportHelper")
        mExportHelper?.showExportDialog(anchor, dismissListener, childFragmentManager)
    }

    override fun processExportText(anchor: View?) {
        //mExportHelper?.showExportTextDialog(null, anchor)
    }

    override fun showOrUnShowRoles(show: Boolean) {}

    override fun cacheWindowShowing() {
        playbackConvertViewModel?.isUserTimeOutDialogShowing = mUserTimeOutDialog?.isShowing == true
        playbackConvertViewModel?.isExportDialogShowing =
            mExportHelper?.getExportDialogIsShowing() == true
        playbackConvertViewModel?.isExportDialogSupportFollow =
            mExportHelper?.getExportDialogSupportFollow() == true
        playbackConvertViewModel?.isExportTextDialogShowing =
            mExportHelper?.getExportTextDialogIsShowing() == true
        playbackConvertViewModel?.isExportTextDialogSupportFollow =
            mExportHelper?.getExportTextDialogSupportFollow() == true
        if (mConvertViewController?.mRenameSpeakerDialog?.isShowing() == true) {
            playbackConvertViewModel?.isRenameSpeakerDialogShowing = true
            playbackConvertViewModel?.contentInRenameSpeakerDialog =
                mConvertViewController?.mRenameSpeakerDialog?.getEditText()?.text.toString()
            playbackConvertViewModel?.isModifyAllInRenameSpeakerDialog =
                mConvertViewController?.mRenameSpeakerDialog?.mIsModifyAll == true
        } else {
            playbackConvertViewModel?.isRenameSpeakerDialogShowing = false
        }
    }

    override fun checkNeedWindowShow(shareTxtAnchor: View?) {
        if (playbackConvertViewModel?.isUserTimeOutDialogShowing == true) {
            showUserTimeOutDialog()
        }

        if (playbackConvertViewModel?.isExportDialogShowing == true) {
            if (playbackConvertViewModel?.isExportDialogSupportFollow == true) {
                FollowDialogRestoreUtils.followDialogRestore(
                    activity?.window?.decorView,
                    true,
                    object : FollowRestoreCallBack {
                        override fun restoreCallBack() {
                            mExportHelper?.showExportDialog(shareTxtAnchor, null)
                        }
                    }
                )
            } else {
                mExportHelper?.showExportDialog(null, null)
            }
        }

        if (playbackConvertViewModel?.isExportTextDialogShowing == true) {
            if (playbackConvertViewModel?.isExportTextDialogSupportFollow == true) {
                FollowDialogRestoreUtils.followDialogRestore(
                    activity?.window?.decorView,
                    true,
                    object : FollowRestoreCallBack {
                        override fun restoreCallBack() {
                            // mExportHelper?.showExportTextDialog(null, shareTxtAnchor)
                        }
                    }
                )
            } else {
                // mExportHelper?.showExportTextDialog(null, null)
            }
        }

        showStatementWithFirstUseConvert {
            DebugUtil.d(TAG, "it = $it")
        }
        showStatementWithFirstUseExtract()

        if (playbackConvertViewModel?.isRenameSpeakerDialogShowing == true) {
            mConvertViewController?.showRenameSpeakerDialog(
                playbackConvertViewModel?.contentInRenameSpeakerDialog
                    ?: "",
                playbackConvertViewModel?.isModifyAllInRenameSpeakerDialog == true,
                playbackConvertViewModel?.originContent ?: ""
            )
        }
    }

    override fun getTimeString(): String {
        return mConvertViewController?.getTimeString() ?: ""
    }

    override fun getConvertContentData(): List<ConvertContentItem>? {
        return mConvertViewController?.getConvertContentData()
    }

    override fun getConvertStatus(): MutableLiveData<Int>? {
        return playbackConvertViewModel?.mConvertStatus
    }

    override fun isSpeakerRoleShowing(): Boolean? = getConvertViewController()?.getConvertViewModel()?.isSpeakerRoleShowing?.value

    override fun startWithSharedTextActivity(summaryFilePath: String, mediaId: Long, shareType: Int, fileType: String) {
        //获取文件的创建时间，避免进入预览界面后，删除录音文件，获取的时间异常
        getConvertViewController()?.getViewModel()?.let { viewModel ->
            val playFileName = viewModel.playName.value ?: ""
            val playFilePath = viewModel.playPath.value ?: ""
            val isShowSpeaker = playbackConvertViewModel?.isSpeakerRoleShowing?.value ?: false
            val createTimeByPath = RecorderDBUtil.getInstance(BaseApplication.getAppContext())
                .getCreateTimeByPath(mediaId, playFileName.endsWith(".amr"))
            val canShowSpeaker = playbackConvertViewModel?.mShowSwitchInConvertModel?.value ?: false
            activity?.run {
                val intent = Intent(this, ShareWithTxtActivity::class.java)
                intent.putExtra("mediaRecordId", mediaId)
                intent.putExtra("canShowSpeaker", canShowSpeaker)
                intent.putExtra("isShowSpeaker", isShowSpeaker)
                intent.putExtra("createTime", createTimeByPath)
                intent.putExtra("playFileName", playFileName)
                intent.putExtra("playFilePath", playFilePath)
                intent.putExtra("shareType", shareType)
                intent.putExtra("fileType", fileType)
                intent.putExtra("summaryFilePath", summaryFilePath)
                //暂停播放
                viewModel.playerController.pausePlay()
                startActivityForResult(intent, Constants.REQUEST_CODE_SHARE_TXT)
            }
        }
    }

    override fun provideLifeCycleOwner(): LifecycleOwner? = lifecycleOwner
}