/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.ui

import android.content.Context
import android.graphics.drawable.Drawable
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.style.CharacterStyle
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isGone
import androidx.core.view.updateLayoutParams
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.google.android.material.imageview.ShapeableImageView
import com.oplus.anim.EffectiveAnimationView
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.durationInMsFormatTimeExclusive
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.common.utils.PathInterpolatorHelper
import com.soundrecorder.common.utils.ViewUtils
import com.soundrecorder.common.utils.cancelAnimationExt
import com.soundrecorder.common.utils.gone
import com.soundrecorder.common.utils.invisible
import com.soundrecorder.common.utils.isGone
import com.soundrecorder.common.utils.playAnimationExt
import com.soundrecorder.common.utils.visible
import com.soundrecorder.common.widget.COUIAnimateTextView
import com.soundrecorder.convertservice.util.BeanConvertTextUtil
import com.soundrecorder.imageload.ImageLoadData
import com.soundrecorder.imageload.ImageLoaderUtils
import com.soundrecorder.modulerouter.photoviewer.PhotoViewerInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.playback.R
import com.soundrecorder.playback.convert.search.ConvertSearchBean
import com.soundrecorder.playback.newconvert.keyword.KeyWordChipClickListener
import com.soundrecorder.playback.newconvert.keyword.KeyWordChipGroup
import com.soundrecorder.playback.newconvert.search.ConvertSearchHelper
import com.soundrecorder.playback.newconvert.search.anim.ConvertSearchAnim
import com.soundrecorder.playback.newconvert.util.ListUtil
import com.soundrecorder.playback.newconvert.util.ResUtil
import com.soundrecorder.playback.newconvert.view.AnimateSpeakerLayout
import com.soundrecorder.playback.newconvert.view.BackGroundTextViewSetupHelper
import com.soundrecorder.playback.newconvert.view.BackgroundTextView
import com.soundrecorder.playback.newconvert.view.ShapableImageViewSetupHelper
import com.soundrecorder.playback.newconvert.view.TextImageMixLayout
import com.soundrecorder.playback.newconvert.view.TextImageMixLayoutHelper
import com.soundrecorder.player.WavePlayerController
import java.util.Objects
import java.util.function.Consumer

open class TextImageItemAdapter(var context: Context?) :
    RecyclerView.Adapter<ViewHolder>() {

    private var mNeedShowRole: Boolean = false
    private var mPlayName: String = ""
    private var mCreateTime: CharSequence = ""
    private var mSubject: String = ""
    private var mRoles: String = ""
    private var distinctList: MutableList<String> = ArrayList()
    private var drawableList = ArrayList<Drawable>()
    private var mDurationTime: Long = 0

    var mOnSpeakerNameClick: OnSpeakerNameClick? = null
    private val viewHolders = mutableListOf<AbsContentViewHolder>()

    var mContentScrollHelper: ContentScrollHelper? = null

    var searchHelper: ConvertSearchHelper? = null

    // 当前选中的第几个搜索结果
    protected var mConvertSearchCurrentFocus: Int = -1
    protected val foregroundColorSpanForSearchResult =
        ForegroundColorSpan(BaseApplication.getAppContext().resources.getColor(R.color.convert_search_result_highlight_foreground))

    // 是否是搜索模式
    protected var searchMode = false

    // 提取关键词
    private var keyWords: MutableList<String> = mutableListOf()
    var keyWordClickListener: KeyWordChipClickListener? = null
    var speakerSelectListener: ISelectSpeakerListener? = null
    private var loadingState: Int = KeyWordChipGroup.DEFAULT_STATE
    private var convertHeaderVH: ConvertHeaderViewHolder? = null

    // 实际绑定的数据
    var data: List<ConvertContentItem.ItemMetaData>? = null
        private set
    // 之前的mContentList
    var mContentItemList: List<ConvertContentItem>? = null
        private set

    /**
     * 是否显示动效
     */
    private var showSearchAnim: Boolean = false

    /**
     * 搜索动效的监听
     */
    var searchAnimListener: Consumer<Boolean>? = null
    /*讲话人选择弹窗相关*/
    var selectSpeakersData = mutableListOf<String>()
    var selectSpeakersHelper: SelectSpeakerHelper? = null

    private var smartNaming: Boolean? = null
    private var showTextAnim: Boolean? = null

    var mDrawattr: TextImageMixLayoutHelper.TextImageMixLayoutDrawAttr =
        TextImageMixLayoutHelper.getDefaultDrawAttr(context)

    //点击文字的相关Callback
    private var mTextClickCallback =
        object : BackGroundTextViewSetupHelper.OnBlackGroundTextClickListenner {

            override fun onTextViewClick(
                view: View,
                convertContentItem: ConvertContentItem?,
                currentItemIndex: Int
            ) {
                if (view is BackgroundTextView) {
                    DebugUtil.i(
                        TAG,
                        "OnClick, off = ${view.offset}, len=${convertContentItem?.textContent?.length}, currentItemIndex $currentItemIndex"
                    )
                    if ((view.offset > 0) && (convertContentItem != null)) {
                        val realOffset =
                            convertContentItem.getTextStringLengthBeforeTextImageItemIndex(
                                currentItemIndex
                            ) + view.offset
                        val seekTime =
                            BeanConvertTextUtil.genStartPlayTime(convertContentItem, realOffset, TextImageMixLayout.SPACE.length)
                        if (seekTime < 0) {
                            DebugUtil.e(TAG, "===>seekTime < 0!")
                            return
                        }
                        DebugUtil.i(TAG, "===>seekTime = $seekTime")
                        if (mPlayer?.hasPaused() == true) {
                            mPlayer?.seekTime(seekTime)
                        } else {
                            mPlayer?.seekToPlay(seekTime)
                        }
                        mContentScrollHelper?.hideBackButtonValue()
                    }
                }
            }
        }

    private val photoViewerApi by lazy {
        Injector.injectFactory<PhotoViewerInterface>()
    }

    //点击ImageView的相关点击事件Callback
    private var mImageClickCallback =
        object : ShapableImageViewSetupHelper.OnShapableImageViewClickListener {

            override fun onImageViewClick(
                view: ImageView,
                convertContentItem: ConvertContentItem?,
                currentDataBean: MarkDataBean
            ) {
                val pictures = getAllPictureMarks()
                val index = pictures.indexOf(currentDataBean)
                photoViewerApi?.startWithBigImage(
                    view.context,
                    pictures,
                    index,
                    view,
                    null
                )
            }
        }


    private fun getAllPictureMarks(): List<MarkDataBean> {
        val pictures: MutableList<MarkDataBean> = ArrayList()
        mContentItemList?.forEach(Consumer { mark: ConvertContentItem ->
            pictures.addAll(mark.getAllPictureMarks())
        })
        pictures.sortBy {
            it.timeInMills
        }
        return pictures
    }

    var mPlayer: WavePlayerController? = null

    init {
        drawableList = ResUtil.getDrawableList()
    }

    /**
     * 设置转文本内容列表，非adapter实际使用数据，adapter绑定是data
     * @param contentItemList 全部数据or筛选讲话人对应的数据
     * @param loadAllSpeaker 是否从列表中load所有讲话人，仅全局数据才会加载所有讲话人
     */
    fun setContentData(contentItemList: List<ConvertContentItem>?, dataList: List<ConvertContentItem.ItemMetaData>?) {
        this.mContentItemList = contentItemList
        this.data = dataList
    }

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        recyclerView.recycledViewPool.setMaxRecycledViews(TYPE_HEADER, 1)
        recyclerView.recycledViewPool.setMaxRecycledViews(TYPE_FOOTER, 1)
        recyclerView.itemAnimator = null
    }

    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        viewHolders.forEach { it.release() }
        viewHolders.clear()
    }


    override fun onViewAttachedToWindow(holder: ViewHolder) {
        super.onViewAttachedToWindow(holder)
        /**
         * Android Bug , RecycleView lead to lose textView selection ability
         */
        if (holder is ConvertContentTextViewHolder) {
            holder.textView.isEnabled = false
            holder.textView.isEnabled = true
        }
    }

    override fun onViewDetachedFromWindow(holder: ViewHolder) {
        super.onViewDetachedFromWindow(holder)
        /*
        DebugUtil.e(
            TAG,
            "onViewDetachedFromWindow holder $holder holder.absoluteAdapterPosition ${holder.absoluteAdapterPosition}" +
                    ", holder.layoutPosition ${holder.layoutPosition}"
        )
         */
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val result: ViewHolder = when (viewType) {
            TYPE_TIME_DIVIDER -> {
                val vh = ConvertContentTimerViewHolder(parent)
                viewHolders.add(vh)
                vh
            }
            TYPE_IMAGE -> {
                val vh = ConvertContentImageViewHolder(parent)
                viewHolders.add(vh)
                vh
            }
            TYPE_TEXT -> {
                val vh = ConvertContentTextViewHolder(parent)
                viewHolders.add(vh)
                vh
            }
            TYPE_FOOTER -> {
                val itemView = View(parent.context).apply {
                    layoutParams = ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        BaseApplication.getAppContext().resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp40)
                    )
                }
                val vh = object : ViewHolder(itemView) {}
                vh
            }
            else -> {
                var vh: ViewHolder
                if (Objects.isNull(convertHeaderVH)) { //进入退出搜索状态时，viewHolder会重新创建，导致动画状态不正确
                    convertHeaderVH = ConvertHeaderViewHolder(parent)
                    vh = convertHeaderVH!!
                } else {
                    vh = convertHeaderVH!!
                }
                return vh
            }
        }
        //DebugUtil.e(TAG, "onCreateViewHolder viewType ${viewType} result ${result}")
        return result
    }

    override fun onViewRecycled(holder: ViewHolder) {
        /*
        DebugUtil.e(
            TAG,
            "onViewRecycled holder $holder holder.absoluteAdapterPosition ${holder.absoluteAdapterPosition}" +
                    ", holder.layoutPosition ${holder.layoutPosition}"
        )
         */
        super.onViewRecycled(holder)
        if (holder is ConvertHeaderViewHolder) {
            holder.releaseAnim()
        }
        if (holder is ConvertContentImageViewHolder) {
//            holder.releaseBitmapCache()
        }
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        if (data == null) return
        //DebugUtil.e(TAG, "onBindViewHolder holder : $holder, postion : $position")
        when (holder) {

            is ConvertHeaderViewHolder -> {
                holder.bindData(mPlayName.title(), mCreateTime, keyWords)
                holder.showSearchAnim()
            }
            is ConvertContentTextViewHolder -> {
                val itemDataPositoin = getMetaItemPostionForViewHolderPostion(position)
                val itemMetadata = data?.get(itemDataPositoin)
                val convertItemIndex = getConvertIndexPositionForViewHolderPosition(position)
                val convertContentItem = mContentItemList?.get(convertItemIndex)

                if (convertContentItem != null && itemMetadata != null && itemMetadata is ConvertContentItem.TextItemMetaData) {
                    holder.setData(itemMetadata, convertContentItem)
                    holder.setKeyWordSpannable(itemMetadata, itemDataPositoin)
                }
                holder.refreshSpeaker()
            }

            is ConvertContentImageViewHolder -> {
                val itemDataPositoin = getMetaItemPostionForViewHolderPostion(position)
                val itemMetadata = data?.get(itemDataPositoin)
                val convertItemIndex = getConvertIndexPositionForViewHolderPosition(position)
                val convertContentItem = mContentItemList?.get(convertItemIndex)
                if (convertContentItem != null && itemMetadata != null && itemMetadata is ConvertContentItem.ImageMetaData) {
                    holder.setData(itemMetadata, convertContentItem)
                }
                holder.refreshSpeaker()
            }

            is ConvertContentTimerViewHolder -> {
                val itemDataPositoin = getMetaItemPostionForViewHolderPostion(position)
                val itemMetadata = data?.get(itemDataPositoin)
                val convertItemIndex = getConvertIndexPositionForViewHolderPosition(position)
                val convertContentItem = mContentItemList?.get(convertItemIndex)
                if (convertContentItem != null && itemMetadata != null && itemMetadata is ConvertContentItem.TimerDividerMetaData) {
                    holder.setData(itemMetadata, convertItemIndex)
                }
                holder.refreshSpeaker()
            }
        }
    }

    private fun getConvertIndexPositionForViewHolderPosition(viewHolderPosition: Int): Int {
        var result = -1
        val dataPosition = getMetaItemPostionForViewHolderPostion(viewHolderPosition)
        if (dataPosition == -1) {
            return -1
        }
        val metaItem = data!![dataPosition]
        run {
            mContentItemList?.forEach { convertItem: ConvertContentItem ->
                if (convertItem.mTextOrImageItems?.contains(metaItem) == true) {
                    result = mContentItemList?.indexOf(convertItem) ?: -1
                    return@run
                }
            }
        }
        /*DebugUtil.i(
            TAG,
            "getConvertIndexPositionForViewHolderPosition inputViewHolderPosition $viewHolderPosition, outResultPosition $result"
        )*/
        return result
    }

    private fun getMetaItemPostionForViewHolderPostion(viewHolderPosition: Int): Int {
        val dataPosition = viewHolderPosition - 1
        if ((dataPosition < 0) || dataPosition > data!!.size - 1) {
            DebugUtil.e(TAG, "getConvertIndexPositionForViewHolderPosition dataPosition is wrong!")
            return -1
        }
        return dataPosition
    }

    /**
     * 设置TextView的内容
     */
    private fun setTextItemContent(
        textView: BackgroundTextView,
        textItemMetaData: ConvertContentItem.TextItemMetaData,
        searchList: MutableList<ConvertSearchBean>
    ) {

        val textContent = textItemMetaData.getTextString()
        if (TextUtils.isEmpty(textContent)) {
            DebugUtil.e(
                TAG,
                "setTextItemContent textItemMetaData:$textItemMetaData content is empty"
            )
            return
        }
        val builder = SpannableStringBuilder(textContent)
        searchList.forEach { bean ->
            highlightKeyword(builder, bean) //高亮搜索词
            if (bean.focus) { //高亮当前选中的
                highlightSearchFocus(builder, bean, textView)
            }
        }
        textView.setItemContentSpannableForTextItemMetaData(builder, textItemMetaData, searchMode)
    }

    /**
     * 高亮搜索词
     */
    open fun highlightKeyword(builder: SpannableStringBuilder, element: ConvertSearchBean) {
        val keyWord = element.keyWord
        val keyWordIndex = element.keyWordIndex
        builder.setSpan(
            CharacterStyle.wrap(foregroundColorSpanForSearchResult),
            keyWordIndex, keyWordIndex + keyWord.length,
            Spannable.SPAN_INCLUSIVE_EXCLUSIVE
        )
    }

    /**
     * 高亮当前选中的搜索词
     */
    open fun highlightSearchFocus(
        builder: SpannableStringBuilder,
        element: ConvertSearchBean,
        itemTextContent: BackgroundTextView
    ) { }

    /*
     * @param refresh true -> notifyDataChange()  false -> speaker animate
     */
    fun roleControl(needShowRole: Boolean, refresh: Boolean) {
        mNeedShowRole = needShowRole
        if (refresh) {
            notifyDataSetChanged()
        } else {
            if (needShowRole) {
                viewHolders.forEach { it.showAnimate() }
            } else {
                viewHolders.forEach { it.dismissAnimate() }
            }
            convertHeaderVH?.refreshSelectSpeakerState()
        }
    }

    fun refreshSmartNameStatusChange(smartNaming: Boolean?, resultName: String?, showTextAnim: Boolean = true) {
        DebugUtil.d(TAG, "refreshSmartNameStatusChange, smartNaming:$smartNaming, resultName:$resultName, showTextAnim:$showTextAnim")
        this.smartNaming = smartNaming
        this.showTextAnim = showTextAnim
        if (!resultName.isNullOrBlank()) {
            mPlayName = resultName
        }
        if (showTextAnim) {
            convertHeaderVH?.refreshSmartNameStatus(resultName)
        }
    }

    fun resetSmartNaming() {
        smartNaming = null
    }

    fun setHeaderData(name: String, time: CharSequence, subject: String = "", roles: String = "", duration: Long? = 0) {
        mPlayName = name
        mCreateTime = time
        mSubject = subject
        mRoles = roles
        if (duration != null) {
            mDurationTime = duration
        }
    }

    /**
     * 添加关键词
     */
    fun setKeyWords(list: List<String>, state: Int) {
        this.loadingState = state
        keyWords.clear()
        keyWords.addAll(list)
        notifyItemChanged(0)
    }

    /**
     * 触发一次搜索动效
     */
    fun triggerSearchAnim() {
        DebugUtil.d(TAG, "触发搜索动效")
        showSearchAnim = true
        notifyItemChanged(0)
    }

    fun loadAllSpeakers(allContentItem: List<ConvertContentItem>?) {
        distinctList = ListUtil.getDistinctRoleName(allContentItem)
        DebugUtil.d(TAG, "loadAllSpeakers, distinctList:${distinctList.size}")
    }

    fun refreshAllViewHolderContentBackground() {
        viewHolders.forEach {
            if (it is ConvertContentTextViewHolder) {
                it.switchSentenceBackground()
            } else if (it is ConvertContentTimerViewHolder) {
                it.switchSentenceBackground()
            }
        }
    }

    private fun refreshAllViewHolderSpeaker() {
        viewHolders.forEach {
            it.refreshSpeaker()
        }
    }

    /**
     * 是否分别勾选了所有讲话人，非默认逻辑
     */
    fun isRealSelectAllSpeaker(): Boolean {
        return selectSpeakersData.isNotEmpty() && selectSpeakersData.size == distinctList.size
    }

    /**
     * 重命名讲话人成功
     * 1.更新讲话人UI
     * 2.更新选择讲话人数据
     */
    fun renameSpeakerSuccess(newName: String, oldName: String, isSelectAllSpeaker: Boolean) {
        refreshAllViewHolderSpeaker()
        if (selectSpeakersData.isEmpty() || !selectSpeakersData.contains(oldName)) {
            DebugUtil.i(TAG, "renameSpeakerSuccess return select=${selectSpeakersData.size}")
            // 1.未勾选中oldName，不做任何处理
            return
        }
        DebugUtil.i(TAG, "renameSpeakerSuccess,selectAll=$isSelectAllSpeaker")
        /*修改讲话人前是否分别勾选了所有讲话人, 产品修改交互，目前只支持单选，将这部分代码屏蔽，后续若支持多个直接放开即可*/
        /*if (isSelectAllSpeaker) {
            *//* 2.选中全部讲话人，则更新选中数据
            * - 所有讲话人中还有oldName：选中列表加上newName（选中无newName前提下）
            * - 所有讲话人没有oldName：
            *   - 选中列表newName不存在：替换oldName为newName;
            *   - 选中列表newName存在：从select中移除
            * *//*
            if (!distinctList.contains(oldName)) {
                if (!selectSpeakersData.contains(newName)) {
                    selectSpeakersData[selectSpeakersData.indexOf(oldName)] = newName
                } else {
                    selectSpeakersData.remove(oldName)
                }
            } else if (!selectSpeakersData.contains(newName)) {
                selectSpeakersData.add(newName)
            }
        } else*/ if (!distinctList.contains(oldName)) {
            /**
             * 3.勾选部分讲话人
             * - 之前选中了oldName：
             *   - 所有是否包含oldName
             *      - 包含-单条替换：不需要处理
             *      - 不包含-所有替换：select将oldNew替换为newName（select不含newName）
             * - 之前未勾选oldName：不关心--不需要处理
             */
            if (!selectSpeakersData.contains(newName)) {
                selectSpeakersData[selectSpeakersData.indexOf(oldName)] = newName
            } else {
                selectSpeakersData.remove(oldName)
            }
        }
        DebugUtil.i(TAG, "renameSpeakerSuccess after,select=${selectSpeakersData.size},all=${distinctList.size}")

        speakerSelectListener?.onSpeakerSelect(selectSpeakersData, selectSpeakersData.size == distinctList.size)
        convertHeaderVH?.refreshSelectSpeakerState()
    }


    override fun getItemCount(): Int = (data?.size ?: 0) + 1 + 1

    fun getHeaderSize(): Int = 1

    override fun getItemViewType(position: Int): Int {
        return when {
            position < 1 -> {
                TYPE_HEADER
            }
            position + 1 == itemCount -> {
                TYPE_FOOTER
            }

            data?.get(position - 1) is ConvertContentItem.TextItemMetaData -> {
                TYPE_TEXT
            }

            data?.get(position - 1) is ConvertContentItem.ImageMetaData -> {
                TYPE_IMAGE
            }

            else -> {
                TYPE_TIME_DIVIDER
            }
        }
    }


    interface OnSpeakerNameClick {
        fun onClick(pos: Int)
    }

    inner class ConvertHeaderViewHolder(parent: ViewGroup) : ViewHolder(
        LayoutInflater.from(parent.context).inflate(R.layout.header_convert_content, parent, false)
    ) {
        val playName: COUIAnimateTextView = itemView.findViewById(R.id.tv_play_name)
        val smartNameLoading: EffectiveAnimationView = itemView.findViewById(R.id.smart_name_loading)
        val createTime: TextView = itemView.findViewById(R.id.tv_create_time)
        val keyWordGroup: KeyWordChipGroup = itemView.findViewById(R.id.key_word_group)
        val selectSpeaker: TextView = itemView.findViewById(R.id.tv_speakers)

        init {
            selectSpeaker.setOnClickListener {
                val context = context ?: return@setOnClickListener
                if (selectSpeakersHelper == null) {
                    selectSpeakersHelper = SelectSpeakerHelper(speakerSelectListener)
                }
                selectSpeakersHelper?.showChooseSpeakerDialog(
                    context,
                    selectSpeaker,
                    selectSpeakersData,
                    distinctList,
                    object : OnDismissListener {
                    override fun onDismiss() {
                        refreshExpandIcon(false)
                    }
                })
                refreshExpandIcon(true)
            }
        }

        private fun refreshExpandIcon(isIconUp: Boolean) {
            if (isIconUp) {
                context?.let {
                    selectSpeaker.setCompoundDrawablesRelativeWithIntrinsicBounds(
                        null,
                        null,
                        ContextCompat.getDrawable(it, R.drawable.ic_speaker_select_collapsed),
                        null
                    )
                }
            } else {
                context?.let {
                    selectSpeaker.setCompoundDrawablesRelativeWithIntrinsicBounds(
                        null,
                        null,
                        ContextCompat.getDrawable(it, R.drawable.ic_speaker_select_expand),
                        null
                    )
                }
            }
        }

        private val anim: ConvertSearchAnim by lazy {
            ConvertSearchAnim(keyWordGroup, searchAnimListener)
        }

        /**
         * 绑定数据
         * @param name 播放的录音名称
         * @param time 录音文件的创建时间
         * @param keyWords 转文本提取的关键词
         */
        fun bindData(name: String?, time: CharSequence, keyWords: List<String>) {
            DebugUtil.i(
                TAG,
                "bindData $name  time: $time keyWords:${keyWords.size} loadingState:$loadingState" +
                        ", supportExactKeyWords: ${FunctionOption.IS_SUPPORT_EXTRACT_KEY_WORDS}, searchMode $searchMode"
            )
            refreshSmartNameStatus(name)
            val markTime = TimeUtils.getFormatTimeExclusiveMillLeastHour(mDurationTime, true)
            val targetTime = time.toString() + DIVIDER_WITH_SPACE + markTime
            createTime.text = targetTime
            refreshSelectSpeakerState()
            if (!FunctionOption.IS_SUPPORT_EXTRACT_KEY_WORDS) { // 不支持提取关键词
                keyWordGroup.visibility = View.GONE
                return
            }
            keyWordGroup.setKeyWords(keyWords, loadingState)
            if (searchMode) { //搜索模式，则不显示显示关键词
                keyWordGroup.visibility = View.GONE
            } else { //非搜索模式，则显示关键词
                keyWordGroup.visibility = View.VISIBLE
                updateKeyWordHeight(keyWords.size)
                keyWordGroup.setKeyWordChipClickListener(keyWordClickListener)
            }
        }

        fun refreshSmartNameStatus(name: String?) {
            DebugUtil.d(TAG, "refreshSmartNameStatus, smartNaming:$smartNaming, name:$name")
            if (smartNaming == null) {
                playName.text = name ?: mPlayName.title()
            } else {
                if (smartNaming == true) {
                    playName.invisible()
                    smartNameLoading.playAnimationExt()
                } else {
                    playName.visible()
                    smartNameLoading.cancelAnimationExt()
                    if (!name.isNullOrBlank() && showTextAnim == true) {
                        if (!smartNameLoading.isGone()) {
                            smartNameLoading.gone()
                        }
                        mPlayName = name
                        playName.setAnimateText(name, true)
                        playName.setAnimationListener {
                            resetSmartNaming()
                        }
                    } else {
                        playName.text = mPlayName.title()
                    }
                }
            }
        }

        fun refreshSelectSpeakerState() {
            selectSpeaker.isGone = !mNeedShowRole
            if (mNeedShowRole) {
                selectSpeaker.text = SelectSpeakerHelper.getSelectSpeakerName(selectSpeakersData)
            } else {
                selectSpeaker.text = ""
            }
        }


        /**
         * 显示搜索的动效
         */
        fun showSearchAnim() {
            if (!FunctionOption.IS_SUPPORT_EXTRACT_KEY_WORDS) { // 不支持提取关键词
                return
            }
            DebugUtil.d(TAG, "showSearchAnim show:$showSearchAnim")
            if (showSearchAnim) {
                showSearchAnim = false
                if (searchMode) {
                    anim.animateSearchIn()
                } else {
                    anim.animateSearchOut()
                }
            }
        }

        /**
         * 更新关键词列表的高度
         * 通过设置height = wrap_content，来更新列表的高度
         * @param size 关键词的个数
         */
        private fun updateKeyWordHeight(size: Int) {
            DebugUtil.d(TAG, "updateKeyWordHeight size:$size")
            if (size == 0) { //没有关键词，就不更新列表高度
                return
            }
            if (!showSearchAnim) { //在动效执行期间，关键词数据请求下来了，需要取消动效
                anim.release()
            }
            keyWordGroup.updateLayoutParams { //设置wrap_content 更新高度
                height = ViewGroup.LayoutParams.WRAP_CONTENT
            }
            saveKeyWordViewHeight() // 更新完成后，更新动效执行的高度
        }

        /**
         * 释放动效
         */
        fun releaseAnim() {
            anim.release()
        }

        /**
         * 通过设置height=0,来隐藏关键词view
         */
        fun hideKeyWordView() {
            val viewHeight = keyWordGroup.height
            DebugUtil.d(TAG, "hideKeyWordView height:$viewHeight")
            if (viewHeight == 0) {
                return
            }
            keyWordGroup.updateLayoutParams<ViewGroup.MarginLayoutParams> {
                height = 0
                topMargin = 0
            }
        }

        /**
         * 重新设置height来显示关键词view
         */
        fun showKeyWordView() {
            val animHeight = ConvertSearchAnim.animHeight
            DebugUtil.d(TAG, "showKeyWordView height:$animHeight")
            if (animHeight <= 0) {
                return
            }
            keyWordGroup.updateLayoutParams<ViewGroup.MarginLayoutParams> {
                height = animHeight
                topMargin = ViewUtils.dp2px(ConvertSearchAnim.MARGIN_TOP).toInt()
            }
        }

        /**
         * 保存关键词View的高度
         */
        fun saveKeyWordViewHeight() {
            keyWordGroup.post {
                ConvertSearchAnim.animHeight = keyWordGroup.height
                DebugUtil.d(TAG, "saveKeyWordViewHeight height:${ConvertSearchAnim.animHeight}")
            }
        }

        /**
         * 进入。退出动画是否正在执行
         */
        fun isSearchAnimRunning(): Boolean {
            return anim.isRunning()
        }
    }


    abstract class AbsContentViewHolder(itemView: View) : ViewHolder(itemView) {

        /**
         * show speaker view animator
         */
        abstract fun showAnimate()

        /**
         * dismiss speaker view animator
         */
        abstract fun dismissAnimate()

        /**
         * refreshSpeaker
         */
        abstract fun refreshSpeaker()

        abstract fun release()

        abstract fun releaseBitmapCache()
    }


    inner class ConvertContentTimerViewHolder(parent: ViewGroup) : AbsContentViewHolder(
        LayoutInflater.from(parent.context)
            .inflate(R.layout.item_convert_content_time_divider, parent, false)
    ) {

        var data: ConvertContentItem.TimerDividerMetaData? = null

        val startTime: TextView = itemView.findViewById(R.id.start_time)
        val llSpeaker: LinearLayout = itemView.findViewById(R.id.ll_speaker)
        val animatorSpeakerView: AnimateSpeakerLayout = itemView.findViewById(R.id.animator_speaker)
        private val tvSpeaker: TextView = itemView.findViewById(R.id.tv_speaker)

        fun switchSentenceBackground() {
            if (data?.isFocuse() == true) {
                startTime.setTextAppearance(R.style.convert_text_time_appearance_focused)
            } else {
                startTime.setTextAppearance(R.style.convert_text_time_appearance_not_focused)
            }
        }

        fun setData(data: ConvertContentItem.ItemMetaData, convertItemIndex: Int) {
            if (data is ConvertContentItem.TimerDividerMetaData) {
                this.data = data
                startTime.contentDescription =
                    TimeUtils.getContentDescriptionForTimeDuration(data.startTime)
                startTime.text = data.startTime.durationInMsFormatTimeExclusive()

                llSpeaker.setOnClickListener {
                    mOnSpeakerNameClick?.onClick(convertItemIndex)
                }
                switchSentenceBackground()
            }
        }


        /**
         * show speaker view animator
         */
        override fun showAnimate() {
            animatorSpeakerView.showAnimate()
        }

        /**
         * dismiss speaker view animator
         */
        override fun dismissAnimate() {
            animatorSpeakerView.dismissAnimate()
        }

        override fun refreshSpeaker() {
            if (data == null) return
            val dataval = data
            val name = dataval?.roleName
            tvSpeaker.text = name
            /*val index = distinctList.indexOf(name)
            val pos = index % COLOR_SIZE
            if (pos >= 0 && pos < drawableList.size) {
                ivSpeaker.setImageDrawable(drawableList[pos])
            }*/
            switchSpeaker(mNeedShowRole)
        }

        /*
         * switch speaker view state
         */
        private fun switchSpeaker(flag: Boolean) {
            animatorSpeakerView.switchSpeaker(flag)
        }

        override fun release() {
            animatorSpeakerView.release()
        }

        override fun releaseBitmapCache() {}
    }


    inner class ConvertContentImageViewHolder(parent: ViewGroup) : AbsContentViewHolder(
        LayoutInflater.from(parent.context)
            .inflate(R.layout.item_convert_content_image, parent, false)
    ) {

        val mImageView: ShapeableImageView = itemView.findViewById(R.id.thumbImage)

        var setupHelper: ShapableImageViewSetupHelper = ShapableImageViewSetupHelper()

        var mSubItemData: ConvertContentItem.ImageMetaData? = null
        var mParentData: ConvertContentItem? = null

        var mImageLoadData: ImageLoadData? = null


        fun setData(data: ConvertContentItem.ItemMetaData, parentData: ConvertContentItem) {
            if (data is ConvertContentItem.ImageMetaData) {
                this.mSubItemData = data
                this.mParentData = parentData
                generateLayoutParagrameForImage(data, parentData)
            }
        }


        private fun generateLayoutParagrameForImage(
            subItemData: ConvertContentItem.ImageMetaData,
            parentData: ConvertContentItem
        ) {
            val subItemIndex = parentData.mTextOrImageItems?.indexOf(subItemData) ?: -1
            val subItemListSize = parentData.mTextOrImageItems?.size ?: -1
            val isFirstOne =
                (subItemListSize != -1) && (subItemIndex == 1) //第0个item为固定的TIME_DIVIDER类型的item，所以这个判定为index=1
            val isLastOne = (subItemListSize != -1) && (subItemIndex == (subItemListSize - 1))
            val lastItem = parentData.mTextOrImageItems?.get(subItemIndex - 1)
            val lastItemType =
                if (lastItem is ConvertContentItem.ImageMetaData) ConvertContentItem.ItemMetaData.TYPE_IMAGE
                else if (lastItem is ConvertContentItem.TextItemMetaData) ConvertContentItem.ItemMetaData.TYPE_TEXT
                else ConvertContentItem.ItemMetaData.TYPE_TIME_DIVIDER
            val viewPair = setupHelper.setUpImageView(
                mImageView.context,
                subItemData,
                parentData,
                mImageView,
                isFirstOne,
                isLastOne,
                lastItemType,
                mImageClickCallback,
                mDrawattr
            )
            mImageLoadData = viewPair?.second
            DebugUtil.i(
                TAG,
                "generateLayoutParagrameForText subItemIndex $subItemIndex subItemListSize $subItemListSize" +
                        " isFirstOne $isFirstOne isLastOne $isLastOne, lastItemType: $lastItemType, currentItem $subItemData, lastItem $lastItem"
            )
        }

        /**
         * show speaker view animator
         */
        override fun showAnimate() {
            showItemTextContentAnimation(itemView)
        }

        /**
         * dismiss speaker view animator
         */
        override fun dismissAnimate() {
            dismissItemTextContentAnimation(itemView)
        }


        override fun refreshSpeaker() {
            switchSpeaker(mNeedShowRole)
        }

        /*
         * switch speaker view state
         */
        private fun switchSpeaker(flag: Boolean) {
            if (flag) {
                val y = BaseApplication.getAppContext().resources.getDimension(com.soundrecorder.common.R.dimen.dp4)
                itemView.translationY = y
            } else {
                itemView.translationY = 0f
            }
        }

        override fun release() {
            itemView.animate().cancel()
        }

        override fun releaseBitmapCache() {
            DebugUtil.i(TAG, "releaseBitmapCache for viewHolder")
            if (mImageLoadData != null) {
                ImageLoaderUtils.clearMemoryCacheByKey(mImageLoadData!!)
            }
        }
    }


    inner class ConvertContentTextViewHolder(parent: ViewGroup) : AbsContentViewHolder(
        LayoutInflater.from(parent.context)
            .inflate(R.layout.item_convert_content_text, parent, false)
    ) {
        var textView: BackgroundTextView = itemView.findViewById(R.id.contentTextView)

        var setupHelper: BackGroundTextViewSetupHelper = BackGroundTextViewSetupHelper()

        var mSubItemData: ConvertContentItem.TextItemMetaData? = null
        var mParentData: ConvertContentItem? = null

        //长按文字的相关callback
        private var actionModeCallback = SeekPlayActionModeCallback()

        init {
            textView.setHighlightType(getTextHighlightType())
        }

        fun setData(subItemData: ConvertContentItem.ItemMetaData, parentData: ConvertContentItem) {
            if (subItemData is ConvertContentItem.TextItemMetaData) {
                mSubItemData = subItemData
                mParentData = parentData
                generateLayoutParagrameForText(subItemData, parentData)
            }
        }

        private fun generateLayoutParagrameForText(
            subItemData: ConvertContentItem.TextItemMetaData,
            parentData: ConvertContentItem
        ) {
            val subItemIndex = parentData.mTextOrImageItems?.indexOf(subItemData) ?: -1
            val subItemListSize = parentData.mTextOrImageItems?.size ?: -1
            val isFirstOne =
                (subItemListSize != -1) && (subItemIndex == 1) //第0个item为固定的TIME_DIVIDER类型的item，所以这个判定为index=1
            val isLastOne = (subItemListSize != -1) && (subItemIndex == (subItemListSize - 1))
            DebugUtil.i(
                TAG, "generateLayoutParagrameForText subItemIndex $subItemIndex" +
                        " subItemListSize $subItemListSize isFirstOne $isFirstOne isLastOne $isLastOne"
            )
            setupHelper.setUpBackgroundTextView(
                subItemIndex,
                subItemData,
                parentData,
                textView,
                isFirstOne,
                isLastOne,
                searchMode,
                actionModeCallback,
                mTextClickCallback,
                mDrawattr
            )
        }

        fun setKeyWordSpannable(data: ConvertContentItem.TextItemMetaData, position: Int) {
            // 先设置数据
            if (!FunctionOption.IS_SUPPORT_CONVERT_SEARCH) {
                return
            }
            // 设置searchBeans中的isFocus参数
            val itemSearchResult = searchHelper?.filterCurrentPositionSearchResult(
                position,
                mConvertSearchCurrentFocus
            )
            //DebugUtil.e(TAG, "setKeyWordSpannable position $position, itemSearchResult $itemSearchResult")
            if (itemSearchResult != null) {
                //根据searchBeans中刷新TextView高亮
                setTextItemContent(textView, data, itemSearchResult)
            }
        }

        fun switchSentenceBackground() {
            val parentData = mParentData
            val subItemData = mSubItemData
            if (parentData == null || subItemData == null) {
                DebugUtil.i(
                    TAG,
                    "switchSentenceBackground parentData $parentData, subItemData $subItemData, null return"
                )
                return
            }
            val itemIndex = parentData.mTextOrImageItems?.indexOf(subItemData) ?: -1
            setupHelper.switchSentenceBackground(
                textView,
                parentData,
                subItemData,
                itemIndex,
                searchMode
            )
        }

        /**
         * show speaker view animator
         */
        override fun showAnimate() {
            showItemTextContentAnimation(itemView)
        }

        /**
         * dismiss speaker view animator
         */
        override fun dismissAnimate() {
            dismissItemTextContentAnimation(itemView)
        }


        override fun refreshSpeaker() {
            switchSpeaker(mNeedShowRole)
        }

        /*
         * switch speaker view state
         */
        private fun switchSpeaker(flag: Boolean) {
            if (flag) {
                val y = BaseApplication.getAppContext().resources.getDimension(com.soundrecorder.common.R.dimen.dp4)
                itemView.translationY = y
            } else {
                itemView.translationY = 0f
            }
        }

        override fun release() {
            itemView.animate().cancel()
        }


        override fun releaseBitmapCache() {}
    }

    private fun dismissItemTextContentAnimation(itemTextContent: View) {
        DebugUtil.d(TAG, "dismissItemTextContentAnimation")
        val y = BaseApplication.getAppContext().resources.getDimension(com.soundrecorder.common.R.dimen.dp4)
        itemTextContent.translationY = y
        itemTextContent.animate()
            .setDuration(360L)
            .translationY(0f)
            .setInterpolator(PathInterpolatorHelper.couiEaseInterpolator)
            .start()
    }

    private fun showItemTextContentAnimation(itemTextContent: View) {
        DebugUtil.d(TAG, "showItemTextContentAnimation")
        itemTextContent.translationY = 0f
        val y = BaseApplication.getAppContext().resources.getDimension(com.soundrecorder.common.R.dimen.dp4)
        itemTextContent.animate()
            .setDuration(360L)
            .translationY(y)
            .setInterpolator(PathInterpolatorHelper.couiEaseInterpolator)
            .start()
    }

    /**
     * 搜索动效是否正在执行
     */
    fun isSearchAnimRunning(): Boolean {
        return convertHeaderVH?.isSearchAnimRunning() == true
    }

    fun release() {
        selectSpeakersHelper?.release()
        selectSpeakersHelper = null
        keyWordClickListener = null
        searchAnimListener = null
    }

    open fun getTextHighlightType(): Int = BackgroundTextView.HIGHLIGHT_TYPE_FOREGROUND

    companion object {
        const val TYPE_HEADER = -1
        const val TYPE_FOOTER = -2
        const val TYPE_TIME_DIVIDER = 1
        const val TYPE_TEXT = 2
        const val TYPE_IMAGE = 3
        const val DIVIDER_WITH_SPACE = " ｜ "


        const val COLOR_SIZE: Int = 11

        const val TAG = "TextImageItemAdapter"
    }
}