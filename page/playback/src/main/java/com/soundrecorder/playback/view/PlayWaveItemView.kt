/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: PlayWaveItemView
 * Description:
 * Version: 1.0
 * Date: 2023/6/26
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/6/26 1.0 create
 */

package com.soundrecorder.playback.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.RectF
import android.util.AttributeSet
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.playback.audio.PlayWaveRecyclerView
import com.soundrecorder.wavemark.wave.WaveViewUtil
import com.soundrecorder.wavemark.wave.view.WaveItemView
import kotlin.math.ceil

class PlayWaveItemView(context: Context, attrs: AttributeSet?, defStyle: Int) : WaveItemView(context, attrs, defStyle) {

    companion object {
        private const val TAG = "PlayWaveItemView"
        // 透明度常量定义
        private const val ALPHA_100_PERCENT = 255
    }

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    override fun drawDottedLineWhenNoData(): Boolean {
        return true
    }

    override fun onDraw(canvas: Canvas) {
        //绘制刻度尺和时间文字
        drawRuler(canvas)
        //绘制录制/播放波形
        drawAmplitude(canvas)
        //绘制标记（重写此方法确保标记动画正常工作）
        drawBookmark(canvas)
        //绘制定向录音背景
        if (isDrawDirectAmpBg()) {
            drawDirectAmpBg(canvas)
        }
    }

    /**
     * 重写波形绘制方法，添加定向录音音柱绘制功能
     * 确保回放页面的定向录音波形显示与录制页面保持一致
     */
    override fun drawAmplitude(canvas: Canvas) {
        // 先调用父类方法绘制基础波形
        super.drawAmplitude(canvas)

        // 如果启用了定向录音，则绘制定向录音音柱
        if (isEnhance && directTimeList?.isNotEmpty() == true) {
            drawDirectAmplitude(canvas)
        }
    }

    private fun isDrawDirectAmpBg(): Boolean {
        return isEnhance || directTime?.isNotEmpty() == true
    }

    private fun drawDirectAmpBg(canvas: Canvas) {
        if (mViewIndex == 0 || parent == null || directTimeList == null) {
            return
        }

        if (parent is PlayWaveRecyclerView && directTimeList.isNotEmpty()) {
            /*0-6000,6000-12000,12000-18000*/
            val waveStartTime = (mViewIndex - 1) * WaveViewUtil.ONE_WAVE_VIEW_DURATION //ms
            val waveEndTime = mViewIndex * WaveViewUtil.ONE_WAVE_VIEW_DURATION //ms

            /*[1761-5417, 8513-14070, 19269-31326]*/
            var directStartTime = 0L
            var directEndTime = 0L
            for (directTime in directTimeList) {
                val startTime = directTime.startTime
                val endTime = directTime.endTime

                directStartTime = if (startTime >= waveStartTime) {
                    startTime
                } else { // startTime < waveStartTime
                    waveStartTime
                }
                directEndTime = if (endTime <= waveEndTime) {
                    endTime
                } else {
                    waveEndTime
                }

                drawDirectPlayAmpBg(directStartTime, directEndTime, waveStartTime, waveEndTime, canvas)
            }
        }
    }

    private fun drawDirectPlayAmpBg(
        directStartTime: Long,
        directEndTime: Long,
        waveStartTime: Long,
        waveEndTime: Long,
        canvas: Canvas
    ) {
        if (directStartTime in 0..directEndTime && directEndTime > 0) {
            var startX = if (directStartTime <= waveStartTime) getXByTime(waveStartTime) else getXByTime(directStartTime)
            var endX = if ((mViewIndex == mTotalCount - 1) || directEndTime <= waveEndTime) {
                getXByTime(directEndTime)
            } else {
                getXByTime(waveEndTime)
            }

            if (isReverseLayout) {
                startX = width - startX
                endX = width - endX
            }
//          DebugUtil.d(TAG, "drawDirectAmpBg directStartTime:$directStartTime, directEndTime:$directEndTime")
            mBackgroundPaint?.let {
                canvas.drawRect(
                    startX, mMarkViewHeight.toFloat(), endX,
                    mViewHeight, mBackgroundPaint
                )
            }
        }
    }

    private fun drawDirectAmplitude(canvas: Canvas) {

    }
}