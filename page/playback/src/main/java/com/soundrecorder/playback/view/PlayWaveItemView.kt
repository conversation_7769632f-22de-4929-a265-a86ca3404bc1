/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: PlayWaveItemView
 * Description:
 * Version: 1.0
 * Date: 2023/6/26
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/6/26 1.0 create
 */

package com.soundrecorder.playback.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.RectF
import android.util.AttributeSet
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.playback.audio.PlayWaveRecyclerView
import com.soundrecorder.wavemark.wave.WaveViewUtil
import com.soundrecorder.wavemark.wave.view.WaveItemView
import kotlin.math.ceil

class PlayWaveItemView(context: Context, attrs: AttributeSet?, defStyle: Int) : WaveItemView(context, attrs, defStyle) {

    companion object {
        private const val TAG = "PlayWaveItemView"
        // 透明度常量定义
        private const val ALPHA_100_PERCENT = 255
    }

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    override fun drawDottedLineWhenNoData(): Boolean {
        return true
    }

    override fun onDraw(canvas: Canvas) {
        //绘制刻度尺和时间文字
        drawRuler(canvas)
        //绘制录制/播放波形
        drawAmplitude(canvas)
        //绘制标记（重写此方法确保标记动画正常工作）
        drawBookmark(canvas)
        //绘制定向录音背景
        if (isDrawDirectAmpBg()) {
            drawDirectAmpBg(canvas)
        }
    }

    /**
     * 重写波形绘制方法，添加定向录音音柱绘制功能
     * 确保回放页面的定向录音波形显示与录制页面保持一致
     */
    override fun drawAmplitude(canvas: Canvas) {
        // 先调用父类方法绘制基础波形
        super.drawAmplitude(canvas)

        // 如果启用了定向录音，则绘制定向录音音柱
        if (isEnhance && directTimeList?.isNotEmpty() == true) {
            drawDirectAmplitude(canvas)
        }
    }

    private fun isDrawDirectAmpBg(): Boolean {
        return isEnhance || directTime?.isNotEmpty() == true
    }

    private fun drawDirectAmpBg(canvas: Canvas) {
        if (mViewIndex == 0 || parent == null || directTimeList == null) {
            return
        }

        if (parent is PlayWaveRecyclerView && directTimeList.isNotEmpty()) {
            /*0-6000,6000-12000,12000-18000*/
            val waveStartTime = (mViewIndex - 1) * WaveViewUtil.ONE_WAVE_VIEW_DURATION //ms
            val waveEndTime = mViewIndex * WaveViewUtil.ONE_WAVE_VIEW_DURATION //ms

            /*[1761-5417, 8513-14070, 19269-31326]*/
            var directStartTime = 0L
            var directEndTime = 0L
            for (directTime in directTimeList) {
                val startTime = directTime.startTime
                val endTime = directTime.endTime

                directStartTime = if (startTime >= waveStartTime) {
                    startTime
                } else { // startTime < waveStartTime
                    waveStartTime
                }
                directEndTime = if (endTime <= waveEndTime) {
                    endTime
                } else {
                    waveEndTime
                }

                drawDirectPlayAmpBg(directStartTime, directEndTime, waveStartTime, waveEndTime, canvas)
            }
        }
    }

    private fun drawDirectPlayAmpBg(
        directStartTime: Long,
        directEndTime: Long,
        waveStartTime: Long,
        waveEndTime: Long,
        canvas: Canvas
    ) {
        if (directStartTime in 0..directEndTime && directEndTime > 0) {
            var startX = if (directStartTime <= waveStartTime) getXByTime(waveStartTime) else getXByTime(directStartTime)
            var endX = if ((mViewIndex == mTotalCount - 1) || directEndTime <= waveEndTime) {
                getXByTime(directEndTime)
            } else {
                getXByTime(waveEndTime)
            }

            if (isReverseLayout) {
                startX = width - startX
                endX = width - endX
            }
//          DebugUtil.d(TAG, "drawDirectAmpBg directStartTime:$directStartTime, directEndTime:$directEndTime")
            mBackgroundPaint?.let {
                canvas.drawRect(
                    startX, mMarkViewHeight.toFloat(), endX,
                    mViewHeight, mBackgroundPaint
                )
            }
        }
    }

    /**
     * 绘制定向录音音柱
     * 参考 drawDirectAmpBg 方法的实现思路，在指定的定向录音时间段内绘制红色音柱
     * @param canvas 画布对象
     */
    private fun drawDirectAmplitude(canvas: Canvas) {
        // 参考 drawDirectAmpBg 的前置检查逻辑
        if (mViewIndex == 0 || parent == null || directTimeList == null || amplitudes == null || amplitudes.isEmpty()) {
            return
        }

        if (parent is PlayWaveRecyclerView && directTimeList.isNotEmpty()) {
            // 参考 drawDirectAmpBg 的时间范围计算逻辑
            val waveStartTime = (mViewIndex - 1) * WaveViewUtil.ONE_WAVE_VIEW_DURATION //ms
            val waveEndTime = mViewIndex * WaveViewUtil.ONE_WAVE_VIEW_DURATION //ms

            // 参考 drawDirectAmpBg 的时间段遍历逻辑
            var directStartTime = 0L
            var directEndTime = 0L
            for (directTime in directTimeList) {
                val startTime = directTime.startTime
                val endTime = directTime.endTime

                // 计算当前波形项内需要绘制定向录音音柱的实际时间范围
                directStartTime = if (startTime >= waveStartTime) {
                    startTime
                } else { // startTime < waveStartTime
                    waveStartTime
                }
                directEndTime = if (endTime <= waveEndTime) {
                    endTime
                } else {
                    waveEndTime
                }

                // 绘制指定时间段内的定向录音音柱
                drawDirectPlayAmplitude(directStartTime, directEndTime, waveStartTime, waveEndTime, canvas)
            }
        }
    }

    /**
     * 绘制指定时间段内的定向录音音柱
     * 参考 drawDirectPlayAmpBg 方法的坐标计算和RTL布局处理逻辑
     * @param directStartTime 定向录音开始时间
     * @param directEndTime 定向录音结束时间
     * @param waveStartTime 当前波形项开始时间
     * @param waveEndTime 当前波形项结束时间
     * @param canvas 画布对象
     */
    private fun drawDirectPlayAmplitude(
        directStartTime: Long,
        directEndTime: Long,
        waveStartTime: Long,
        waveEndTime: Long,
        canvas: Canvas
    ) {
        // 参考 drawDirectPlayAmpBg 的时间有效性检查
        if (directStartTime in 0..directEndTime && directEndTime > 0) {
            // 参考 drawDirectPlayAmpBg 的坐标计算逻辑
            var startX = if (directStartTime <= waveStartTime) getXByTime(waveStartTime) else getXByTime(directStartTime)
            var endX = if ((mViewIndex == mTotalCount - 1) || directEndTime <= waveEndTime) {
                getXByTime(directEndTime)
            } else {
                getXByTime(waveEndTime)
            }

            // 参考 drawDirectPlayAmpBg 的RTL布局处理逻辑
            if (isReverseLayout) {
                startX = width - startX
                endX = width - endX
            }

            // 在指定的X坐标范围内绘制定向录音音柱
            drawAmplitudeInRange(startX, endX, canvas)
        }
    }

    /**
     * 在指定的X坐标范围内绘制定向录音音柱
     * 遍历该范围内的所有音柱位置，使用定向录音专用画笔绘制
     * @param startX 开始X坐标
     * @param endX 结束X坐标
     * @param canvas 画布对象
     */
    private fun drawAmplitudeInRange(startX: Float, endX: Float, canvas: Canvas) {
        // 计算波形绘制的起始参数
        var amplitudeIndex = calculateAmpStartIndex()
        val ampStartX = calculateAmpStartX(amplitudeIndex.toLong())
        var currentX = ampStartX

        // 确保坐标范围正确（处理RTL布局可能导致的startX > endX情况）
        val minX = kotlin.math.min(startX, endX)
        val maxX = kotlin.math.max(startX, endX)

        // 遍历音柱，在指定范围内绘制定向录音音柱
        while (currentX <= width && amplitudeIndex < amplitudes.size) {
            // 检查当前音柱是否在定向录音时间段范围内
            if (currentX >= minX && currentX <= maxX) {
                // 获取当前位置的波形高度
                val amplitudeValue = amplitudes[amplitudeIndex] ?: 0
                val preAmplitudeValue = if (amplitudeIndex > 0) amplitudes[amplitudeIndex - 1] ?: 0 else 0
                val lineHeight = getWaveLineHeight(preAmplitudeValue, amplitudeValue)

                // 计算音柱矩形坐标
                val waveStartY = getStartYByHeight(lineHeight)
                val waveEndY = getEndYByHeight(lineHeight)
                var newCurrentX = currentX
                var newCurrentXLatter = currentX + mDirectAmpWidth

                // 处理RTL布局的坐标转换
                if (isReverseLayout) {
                    newCurrentX = width - currentX - mDirectAmpWidth
                    newCurrentXLatter = width - currentX
                }

                // 使用定向录音专用画笔绘制音柱
                mEnhanceAmplitudePaint?.let { paint ->
                    paint.alpha = ALPHA_100_PERCENT
                    val ampRectF = RectF(newCurrentX, waveStartY.toFloat(), newCurrentXLatter, waveEndY.toFloat())
                    canvas.drawRoundRect(ampRectF, mAmplitudeWidth, mAmplitudeWidth, paint)
                }
            }

            currentX += mVirtualAmpGap
            amplitudeIndex++
        }
    }
}