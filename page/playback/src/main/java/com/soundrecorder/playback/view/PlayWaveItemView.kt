/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: PlayWaveItemView
 * Description:
 * Version: 1.0
 * Date: 2023/6/26
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/6/26 1.0 create
 */

package com.soundrecorder.playback.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.RectF
import android.util.AttributeSet
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.wavemark.wave.WaveViewUtil
import com.soundrecorder.wavemark.wave.view.WaveItemView

class PlayWaveItemView(context: Context, attrs: AttributeSet?, defStyle: Int) : WaveItemView(context, attrs, defStyle) {

    companion object {
        private const val TAG = "PlayWaveItemView"
    }

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    override fun drawDottedLineWhenNoData(): Boolean {
        return true
    }

    override fun drawAmplitude(canvas: Canvas) {
        // 先调用父类方法绘制基础波形
        super.drawAmplitude(canvas)

        // 如果启用了定向录音，则绘制定向录音音柱
        if (isEnhance && directTimeList?.isNotEmpty() == true) {
            drawEnhanceAmplitude(canvas)
        }
    }

    private fun drawEnhanceAmplitude(canvas: Canvas) {
        if (mViewIndex == 0 || amplitudes == null || amplitudes.isEmpty()) {
            return
        }
        val viewWidth = width
        // 计算当前波形项对应的时间
        val waveStartTime = (mViewIndex - 1) * WaveViewUtil.ONE_WAVE_VIEW_DURATION
        // 计算波形绘制的起始参数
        var amplitudeIndex = calculateAmpStartIndex()
        val startX = calculateAmpStartX(amplitudeIndex.toLong())
        var currentX = startX
        // 遍历绘制定向录音音柱
        while (currentX <= viewWidth && amplitudeIndex < amplitudes.size) {
            var currentTime = waveStartTime + ((currentX - startX) / pxPerMs).toLong()
            val (isInDirectRecordTime, startTime, endTime) = isInDirectRecordTimeRange(currentTime)
            DebugUtil.i(TAG, "drawEnhanceAmplitude, currentTime:$currentTime, isInDirectRecordTime:$isInDirectRecordTime" +
                    ", startTime:$startTime, endTime:$endTime")
            if (!isInDirectRecordTime) {
                val timeGap = startTime - currentTime
                val count = (timeGap * pxPerMs / mVirtualAmpGap).toInt()
                DebugUtil.d(TAG, "drawEnhanceAmplitude, timeGap:$timeGap, count:$count")
                if (timeGap < 0 || count <= 1) {
                    currentX += mVirtualAmpGap
                    amplitudeIndex++
                    continue
                }
                currentX += mVirtualAmpGap * count
                amplitudeIndex += count
                continue
            }
            while (currentX <= viewWidth && amplitudeIndex < amplitudes.size && currentTime <= endTime) {
                // 获取当前位置的波形高度
                val amplitudeValue = amplitudes[amplitudeIndex] ?: 0
                val preAmplitudeValue = if (amplitudeIndex > 0) amplitudes[amplitudeIndex - 1] ?: 0 else 0
                val lineHeight = getWaveLineHeight(preAmplitudeValue, amplitudeValue)
                // 绘制定向录音音柱
                drawEnhanceAmpColumn(canvas, currentX, lineHeight)
                currentX += mVirtualAmpGap
                amplitudeIndex++
                currentTime = waveStartTime + ((currentX - startX) / pxPerMs).toLong()
            }
        }
    }

    /**
     * 获取指定时间点是否在定向录音范围
     * @param time 要检查的时间点
     * @return Triple<Boolean, Long, Long> 第一个元素表示是否在定向录音时间段内
     * 若第一个元素为 true，则第二个和第三个元素表示定向录音时间段的起始和结束时间
     * 若第一个元素为 false，则第二个和第三个元素表示当前时间点的后面一段定向录音时间段的起始和结束时间
     */
    private fun isInDirectRecordTimeRange(time: Long): Triple<Boolean, Long, Long> {
        // 若当前时间在定向录音时间段内，则返回true，并返回定向录音时间段
        directTimeList?.forEach { directTime ->
            if (time >= directTime.startTime && time <= directTime.endTime) {
                return Triple(true, directTime.startTime, directTime.endTime)
            }
        }
        // 计算当前时间点的后面一段定向录音时间段
        directTimeList?.forEach { directTime ->
            if (time < directTime.startTime) {
                return Triple(false, directTime.startTime, directTime.endTime)
            }
        }
        // 当前时间点后面没有定向录音时间段，则返回false，并返回 0，0
        return Triple(false, 0, 0)
    }

    /**
     * 绘制单个定向录音音柱
     * @param canvas 画布对象
     * @param currentX 音柱的X坐标位置
     * @param lineHeight 音柱的高度
     */
    private fun drawEnhanceAmpColumn(canvas: Canvas, currentX: Float, lineHeight: Float) {
        val waveStartY = getStartYByHeight(lineHeight)
        val waveEndY = getEndYByHeight(lineHeight)
        var newCurrentX = currentX
        var newCurrentXLatter = currentX + mDirectAmpWidth
        // 处理RTL布局
        if (isReverseLayout) {
            newCurrentX = width - currentX
            newCurrentXLatter = width - currentX - mDirectAmpWidth
        }

        // 使用定向录音专用画笔绘制音柱
        mEnhanceAmplitudePaint?.let { paint ->
            val ampRectF = RectF(newCurrentX, waveStartY.toFloat(), newCurrentXLatter, waveEndY.toFloat())
            canvas.drawRoundRect(ampRectF, mAmplitudeWidth, mAmplitudeWidth, paint)
        }
    }
}