/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ShareWithSummaryFragment.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/5/29
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.exportconvert.summary

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import com.coui.appcompat.bottomnavigation.COUINavigationView
import com.coui.appcompat.material.navigation.NavigationBarView
import com.coui.appcompat.snackbar.COUISnackBar
import com.coui.appcompat.toolbar.COUIToolbar
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.backpressed.OnBackPressedListener
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.dialog.LoadingDialog
import com.soundrecorder.modulerouter.share.IShareListener
import com.soundrecorder.modulerouter.share.ShareType
import com.soundrecorder.playback.R
import com.soundrecorder.playback.databinding.FragmentShareWithSummaryBinding
import com.soundrecorder.playback.newconvert.exportconvert.txt.SaveToLocalCallback

class ShareWithSummaryFragment : Fragment(), OnBackPressedListener {
    companion object {
        private const val TAG = "ShareWithSummaryFragment"
        const val DELAY_MILLIS = 5000
    }

    private var mRootView: View? = null
    private lateinit var binding: FragmentShareWithSummaryBinding
    private var mToolbar: COUIToolbar? = null
    private var mediaRecordId = 0L

    //保存文件 查看 文件管理还需要一个监听
    private var mBottomNavigationView: COUINavigationView? = null

    private val mViewModel by activityViewModels<ShareWithSummaryViewModel>()

    //保存TXT到本地成功的提示
    private var mCOUISnackBar: COUISnackBar? = null

    private var mTxtLoadingDialog: LoadingDialog? = null

    /**
     * 保存文本到本地结果的callback
     */
    private val saveTxtToLocalCallback = object : SaveToLocalCallback {

        override fun onShowSaveFileWaitingDialog() {
            DebugUtil.d(TAG, "onShowSaveFileWaitingDialog...")
            showWaitingDialog()
        }

        override fun onGetFileName(fileName: String, fileAbsPath: String) {
            DebugUtil.d(TAG, "save local ，fileName >> $fileName， fileAbsPath >> $fileAbsPath")
        }

        override fun onSaveSuccess(fileName: String, fileAbsPath: String) {
            if (fileAbsPath.isBlank() || fileName.isBlank()) {
                DebugUtil.e(TAG, "save local error，path or file name is null!!")
                dismissDialogShowSnackBar(false, "")
            } else {
                dismissDialogShowSnackBar(true, fileAbsPath)
            }
        }

        override fun onSaveFailed(message: String) {
            DebugUtil.e(TAG, "save local error，message >> $message")
            dismissDialogShowSnackBar(false, "")
        }
    }

    /**
     * 分享文本前置流程callback
     */
    private val shareListener = object : IShareListener {

        override fun onShowShareWaitingDialog(mediaId: Long, type: ShareType) {
            DebugUtil.d(TAG, "onShowShareWaitingDialog type:$type")
            showWaitingDialog()
        }

        override fun onShareSuccess(mediaId: Long, type: ShareType) {
            DebugUtil.d(TAG, "onShareSuccess，type >> $type")
            dismissDialog()
        }

        override fun onShareFailed(mediaId: Long, type: ShareType, error: Int, message: String) {
            DebugUtil.d(TAG, "onShareFailed，type >> $type  error:$error  message:$message")
            dismissDialog()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        mRootView = inflater.inflate(R.layout.fragment_share_with_summary, container, false)
        initArgFromBundle(arguments)
        mRootView?.let {
            binding = FragmentShareWithSummaryBinding.bind(it)
            initActionBar()
            ensureNavigationView()
        }
        return mRootView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initObservers()
        initListener()
        initData()
        (activity as? BaseActivity)?.registerBackPressed()
    }

    private fun initData() {
        showLoading()
        mViewModel.initArgFromBundle(arguments)
        mViewModel.loadData()
    }

    private fun initObservers() {
        mViewModel.mSummaryDate.observe(viewLifecycleOwner) {
            binding.tvShareSummaryContent.text = it
            hideLoading()
        }
    }

    private fun showLoading() {
        binding.bodySummary.visibility = View.GONE
        binding.colorLoadView.visibility = View.VISIBLE
    }

    private fun hideLoading() {
        binding.colorLoadView.visibility = View.GONE
        binding.bodySummary.visibility = View.VISIBLE
    }

    private fun initListener() {
        //保存文本到本地结果的listener
        mViewModel.setOnSaveTxtToLocalResultCallback(saveTxtToLocalCallback)
        mViewModel.setOnShareTxtCallbackResultCallback(shareListener)
    }

    private fun initActionBar() {
        mToolbar = mRootView?.findViewById(R.id.toolbar)
        mToolbar?.setNavigationIcon(R.drawable.ic_home_back_arrow)
        mToolbar?.title = getString(com.soundrecorder.common.R.string.export_share_preview_title)
        mToolbar?.setNavigationOnClickListener {
            onBackPressed()
        }
    }

    /**
     * 保存TXT到本地流程结束，关闭waitingDialog，执行弹出snackbar操作，重置参数
     */
    private fun dismissDialogShowSnackBar(success: Boolean, fileAbsPath: String) {
        if (mTxtLoadingDialog?.isShowing() == true) {
            mTxtLoadingDialog?.dismiss()
        }
        if (success) {
            context?.resources?.let {
                showSnackBar(
                    it.getString(
                        com.soundrecorder.common.R.string.export_store_loacl_tips,
                        fileAbsPath
                    ),
                    it.getString(com.soundrecorder.common.R.string.export_view_look),
                    fileAbsPath
                )
            }
        } else {
            context?.let {
                ToastManager.showShortToast(it, com.soundrecorder.common.R.string.save_failed)
            }
        }
        mViewModel.setAlreadyShowSnackBar()
    }

    /**
     * 保存到本地文件成功之后显示提示
     * 点击“查看”可以跳转到文件管理对应的目录
     */
    @Suppress("TooGenericExceptionCaught")
    private fun showSnackBar(message: String, actionText: String, fileAbsPath: String) {
        mCOUISnackBar?.dismiss()
        mBottomNavigationView?.let {
            it.post {
                mCOUISnackBar = COUISnackBar.make(it, message, DELAY_MILLIS).apply {
                    (parent as? ViewGroup)?.clipChildren = false
                    setOnAction(actionText) {
                        DebugUtil.d(
                            TAG,
                            "save snackBar action click...fileAbsPath >> $fileAbsPath"
                        )
                        if (fileAbsPath.isNotBlank()) {
                            activity?.let { activity ->
                                try {
                                    val intent =
                                        Intent("oppo.filemanager.intent.action.BROWSER_FILE")
                                    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                                    intent.putExtra("CurrentDir", SaveSummaryTextUtil.getSaveTxtFolderPath())
                                    activity.startActivity(intent)
                                } catch (e: Exception) {
                                    DebugUtil.w(TAG, "showSnackBar error: $e")
                                }
                            }
                        }
                    }
                    show()
                    /**
                     * 防止转写文本选中状态时，复制分享全选弹框遮盖 snackBar
                     * 通过snackBar中textView抢走焦点。使转写文本退出选中状态。
                     */
                    contentView.focusable = View.FOCUSABLE
                    contentView.requestFocusFromTouch()
                    contentView.focusable = View.FOCUSABLE_AUTO
                }
            }
        }
        mViewModel.setAlreadyShowSnackBar()
    }

    override fun onBackPressed(): Boolean {
        DebugUtil.d(TAG, "ShareWithTxt activity onBackPressed...")
        if (mCOUISnackBar?.isShown == true) {
            mCOUISnackBar?.dismiss()
            return true
        }
        activity?.finish()
        return true
    }

    private fun ensureNavigationView() {
        mBottomNavigationView = mRootView?.findViewById(R.id.navi_menu_tool_share_summary)

        mBottomNavigationView?.setOnItemSelectedListener(
            NavigationBarView.OnItemSelectedListener OnItemSelectedListener@{ item ->
                if (ClickUtils.isFastDoubleClick()) {
                    return@OnItemSelectedListener true
                }
                onItemSelected(item)
                true
            })
    }

    /**
     * 底部菜单栏点击事件
     */
    private fun onItemSelected(item: MenuItem) {
        if (ClickUtils.isQuickClick()) {
            return
        }
        //保存成功还需要一个提示
        if (mCOUISnackBar?.isShown == true) {
            mCOUISnackBar?.dismiss()
            return
        }
        when (item.itemId) {
            //保存到本地
            R.id.menu_share_save -> {
                DebugUtil.d(TAG, "menu_share_save click")
                mViewModel.saveTxtToLocal()
            }
            //分享文档 pdf word txt
            R.id.menu_share_txt -> {
                DebugUtil.d(TAG, "menu_share_txt click")
                activity?.let {
                    mViewModel.shareTxt(it)
                }
            }
        }
    }

    private fun initArgFromBundle(arguments: Bundle?) {
        arguments?.let {
            mediaRecordId = it.getLong("mediaRecordId")
            DebugUtil.d(TAG, "mediaRecordId >> $mediaRecordId")
        }
    }

    override fun onDestroyView() {
        DebugUtil.d(TAG, "onDestroyView......")
        super.onDestroyView()
        dismissWaitingDialog()
        releaseListener()
        SaveSummaryTextUtil.clearCallback()
        (activity as? BaseActivity)?.unRegisterBackPressed()
    }

    private fun showWaitingDialog() {
        if (mTxtLoadingDialog == null) {
            mTxtLoadingDialog = LoadingDialog(activity)
        }
        //修复loading dialog只显示一次的问题
        if (mTxtLoadingDialog?.isActivityNull() == true) {
            mTxtLoadingDialog?.resetActivity(activity)
        }
        mTxtLoadingDialog?.show(com.soundrecorder.common.R.string.waiting)
        mViewModel.setNeedRestoreDialog(false)
    }

    private fun dismissWaitingDialog() {
        if (mTxtLoadingDialog?.isShowing() == true) {
            mViewModel.setNeedRestoreDialog(true)
            mTxtLoadingDialog?.dismiss()
        }
        mTxtLoadingDialog = null
    }

    /**
     * 分享前置流程结束，关闭waitingDialog，执行弹出分享面板操作，重置参数
     */
    private fun dismissDialog() {
        if (mTxtLoadingDialog?.isShowing() == true) {
            mTxtLoadingDialog?.dismiss()
        }
    }

    private fun releaseListener() {
        mViewModel.setOnSaveTxtToLocalResultCallback(null)
        mViewModel.setOnShareTxtCallbackResultCallback(null)
    }
}