/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.search

import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.SearchView
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import androidx.recyclerview.widget.COUIRecyclerView
import com.coui.appcompat.searchview.COUISearchViewAnimate
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.backpressed.OnBackPressedListener
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.view.DeDuplicateInsetsCallback
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.utils.CoroutineUtils
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.playback.PlaybackActivityViewModel
import com.soundrecorder.playback.R
import com.soundrecorder.playback.convert.search.ConvertSearchBean
import com.soundrecorder.playback.databinding.FragmentConvertSearchBinding
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel.Companion.CONVERT_STATUS_COMPLETE
import com.soundrecorder.playback.newconvert.keyword.KeyWordChipGroup
import com.soundrecorder.playback.newconvert.search.view.ConvertSearchBottomArea
import com.soundrecorder.playback.newconvert.util.ListUtil
import com.soundrecorder.playback.newconvert.view.CustomLinearLayoutManager
import kotlinx.coroutines.launch
import java.util.function.Consumer

/**
 * 1.进入搜索，讲话人区域有动效闪烁
 * 2.进入页面、退出页面的文本列表滑动位置保持一致(设计稿有修改，待确认)
 * 3.进入搜索状态，搜索框需要有出现动画(待动效输出)
 * */
class ConvertSearchFragment : Fragment(),
    COUISearchViewAnimate.OnCancelButtonClickListener,
    SearchView.OnQueryTextListener,
    OnBackPressedListener {

    companion object {
        const val  TAG = "ConvertSearchFragment"
    }

    private lateinit var mBinding: FragmentConvertSearchBinding
    private lateinit var mAdapter: ConvertSearchAdapter
    private lateinit var mLayoutManager: CustomLinearLayoutManager
    private lateinit var mSearchScrollHelper: SearchScrollHelper
    private lateinit var mRvConvertContent: COUIRecyclerView
    private lateinit var mSearchBottomArea: ConvertSearchBottomArea
    private lateinit var bgMask: View

    private lateinit var mViewModel: PlaybackActivityViewModel
    private lateinit var mConvertViewModel: PlaybackConvertViewModel

    private var searchHelper: ConvertSearchHelper? = null

    private var mTimeString: String = ""

    private var isRelease = false

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        BuryingPoint.addRecordSearchDurRecords(RecorderUserAction.VALUE_SEARCH_INFO_PAGE, 0, 0)
        DebugUtil.d(TAG, "onCreateView")
        mBinding = FragmentConvertSearchBinding.bind(
            inflater.inflate(
                R.layout.fragment_convert_search,
                container,
                false
            )
        )
        mBinding.root.isClickable = true    // 透传到下方ConvertFragment
        return mBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        DebugUtil.d(TAG, "onViewCreated")
        mViewModel = ViewModelProvider(requireParentFragment()).get(PlaybackActivityViewModel::class.java)
        mConvertViewModel = ViewModelProvider(requireParentFragment()).get(PlaybackConvertViewModel::class.java)
        initView()
        initData(savedInstanceState != null)
        initObservers()
        (activity as? BaseActivity)?.registerBackPressed()
    }

    private fun initView() {
        initiateWindowInsets()
        mSearchBottomArea = mBinding.root.findViewById(R.id.ll_bottom_area)
        mSearchBottomArea.positionChangeListener = object : ConvertSearchBottomArea.SearchPositionChangeListener {
            override fun onPositionChanged(position: Int, imgBtnPos: Int) {
                DebugUtil.i(TAG, "onPositionChanged inputPosition $position, imgBtnPos $imgBtnPos")
                mConvertViewModel.currentPos = position
                mAdapter.setConvertSearchFocus(position, mConvertViewModel.lastPos)
                mConvertViewModel.lastPos = mConvertViewModel.currentPos
                when (imgBtnPos) {
                    ConvertSearchBottomArea.POS_IMG_PREVIOUS -> BuryingPoint.addContentSearchPosChange(RecorderUserAction.VALUE_POS_CHANGE_PREVIOUS)
                    ConvertSearchBottomArea.POS_IMG_NEXT -> BuryingPoint.addContentSearchPosChange(RecorderUserAction.VALUE_POS_CHANGE_NEXT)
                }
            }
        }

        mRvConvertContent = mBinding.root.findViewById<COUIRecyclerView>(R.id.convert_content)
        bgMask = mBinding.root.findViewById(R.id.background_mask)
        setMaskBgVisible(mConvertViewModel.mConvertSearchValue)
        activity?.let {
            mLayoutManager = CustomLinearLayoutManager(it)
            mAdapter = ConvertSearchAdapter(activity)
            mAdapter.searchAnimListener = Consumer { outAnim ->
                mConvertViewModel.searchAnimEnd.value = outAnim
            }
            mRvConvertContent.apply {
                layoutManager = mLayoutManager
                adapter = mAdapter
            }
            mSearchScrollHelper = SearchScrollHelper(mBinding.root as ViewGroup, mConvertViewModel, mRvConvertContent)
            mAdapter.mSearchScrollHelper = mSearchScrollHelper
            mSearchScrollHelper.addOnScrollListener()

            ensureSearchHelper()
            mAdapter.searchHelper = searchHelper
        }

        bgMask.setOnClickListener {
            // 点击蒙层时，退出搜索模式
            onClickCancel()
        }
        //搜索界面不需要此分割线
        mBinding.root.findViewById<View>(R.id.divider_line).visibility = View.GONE
    }

    private fun initiateWindowInsets() {
        val callback = object : DeDuplicateInsetsCallback() {
            override fun onApplyInsets(v: View, insets: WindowInsetsCompat) {
                val stableStatusBarInsets = insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.systemBars())
                mBinding.llBottomArea.apply {
                    val padding = resources.getDimensionPixelSize(R.dimen.play_convert_search_bottom_area_padding)
                    updatePadding(left = padding, top = padding, right = padding, bottom = (stableStatusBarInsets.bottom + padding))
                    val imeHeight = insets.getInsets(WindowInsetsCompat.Type.ime()).bottom
                    updateLayoutParams<ViewGroup.MarginLayoutParams> {
                        height = stableStatusBarInsets.bottom + resources.getDimensionPixelSize(R.dimen.play_convert_search_bottom_area_height)
                    }
                    setLayoutWithSoftInput(imeHeight, stableStatusBarInsets.bottom)
                }
            }
        }
        ViewCompat.setOnApplyWindowInsetsListener(mBinding.root, callback)
    }

    /**
     * 创建searchHelper，避免界面重建时SearchHelper对象丢失
     */
    private fun ensureSearchHelper() {
        searchHelper = mConvertViewModel.searchHelper
        if (searchHelper == null) {
            searchHelper = ConvertSearchHelper(mConvertViewModel.convertContentData)
            mConvertViewModel.searchHelper = searchHelper
        }
    }

    private fun initData(fromRestore: Boolean) {
        if (!fromRestore && mViewModel.playerController.isWholePlaying()) {
            mViewModel.playerController.playBtnClick()
        }
        isRelease = false
        mTimeString = ListUtil.getTimeString(mViewModel.recordId, mViewModel.playName.value)
        mAdapter.apply {
            roleControl(mConvertViewModel.isSpeakerRoleShowing.value == true, true) //TODO: 进入页面讲话人位置动效闪现
            setHeaderData(mViewModel.playName.value ?: "", mTimeString, "", "", mViewModel.duration)
            setContentData(mConvertViewModel.convertContentData, ListUtil.getMetaDataList(mContentItemList))
            notifyDataSetChanged()

            animSearchIn(this)
        }
    }

    /**
     * 进入搜索的动效
     */
    private fun animSearchIn(adapter: ConvertSearchAdapter) {
        // 如果是header可见，则设置搜索动画
        val inScreen = mConvertViewModel.isHeaderInScreen()
        DebugUtil.d(TAG, "关键词View 是否在当前屏幕：$inScreen")
        if (!inScreen) {
            return
        }
        // 进入搜索动画，需要设置关键词
        if (FunctionOption.IS_SUPPORT_EXTRACT_KEY_WORDS) {
            val keyWords = mConvertViewModel.keyWords.value ?: emptyList()
            val state = mConvertViewModel.extractState ?: KeyWordChipGroup.DEFAULT_STATE
            adapter.setKeyWords(keyWords, state)
        }
        // 进入搜索的动效
        adapter.triggerSearchAnim()
    }

    private fun initObservers() {
        mConvertViewModel.apply {
            mConvertSearchResult.observe(viewLifecycleOwner) { result ->
                DebugUtil.i(
                    TAG,
                    "mConvertSearchResult observe executed. result's size=${result.size} release:$isRelease"
                )
                if (isRelease) {
                    return@observe
                }
                mAdapter.notifyDataSetChanged()
                // 更新底部操作结果条
                mSearchBottomArea.setSearchText(mConvertSearchValue)
                mSearchBottomArea.setCurrentPosition(currentPos, result.size)
            }

            visibleItemLocation.observe(viewLifecycleOwner) {
                //开始滚动
                restoreScrollPosition(mLayoutManager)
            }

            if (autoSearch) { // 开启自动搜索
                autoSearch = false
                onQueryTextSubmit(mConvertSearchValue)
            }
        }

        mConvertViewModel.mConvertStatus.observe(viewLifecycleOwner) {
            if (it == CONVERT_STATUS_COMPLETE) {
                searchHelper?.convertTextItems = mConvertViewModel.convertContentData
                initData(false)
                onQuery(mConvertViewModel.mConvertSearchValue, true)
            }
        }
    }

    /**
     * 设置蒙层是否可见
     * 当有输入内容时，蒙层消失
     */
    private fun setMaskBgVisible(text: String?) {
        if (this::bgMask.isInitialized) {
            bgMask.isVisible = TextUtils.isEmpty(text)
        }
    }

    override fun onQueryTextSubmit(query: String?): Boolean {
        onQueryTextChange(query)
        return false
    }

    override fun onQueryTextChange(newText: String?): Boolean {
        onQuery(newText)
        return false
    }

    private fun onQuery(newText: String?, recreated: Boolean = false) {
        setMaskBgVisible(newText)
        val searchHelper = searchHelper ?: return
        mConvertViewModel.mConvertSearchValue = newText
        if (!recreated) {
            mConvertViewModel.currentPos = 0
            mConvertViewModel.lastPos = 0
        }
        var result = mutableListOf<ConvertSearchBean>()
        mConvertViewModel.viewModelScope.launch {
            CoroutineUtils.ioToMain({
                result = searchHelper.queryByKeyWord(newText ?: "")
            }, {
                mConvertViewModel.mConvertSearchResult.value = result
            })
        }
    }

    override fun onBackPressed(): Boolean {
        onClickCancel()
        return true
    }

    override fun onClickCancel(): Boolean {
        if (isConvertSearchAnimRunning()) {
            DebugUtil.e(TAG, "onClickCancel, but last searchAnim is running")
            return true
        }
        release()
        if (::mConvertViewModel.isInitialized) {
            mConvertViewModel.mIsInConvertSearch.value = false
        }
        BuryingPoint.addClickCancelContentSearch()
        return true
    }

    /**
     * 判断进入，退出搜索动画，是否正在执行
     */
    private fun isConvertSearchAnimRunning(): Boolean {
        if (!this::mAdapter.isInitialized) {
            return false
        }
        return mAdapter.isSearchAnimRunning()
    }

    private fun release() {
        DebugUtil.d(TAG, "release")
        isRelease = true
        // 记录滚动的位置
        if (::mConvertViewModel.isInitialized) {
            mConvertViewModel.apply {
                saveScrollPosition("convert_search", mLayoutManager)
                currentPos = 0
                lastPos = 0
                mConvertSearchResult.value = emptyList()
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        DebugUtil.e(TAG, "onDestroyView $isRelease")
        mSearchScrollHelper.removeOnScrollListener()
        if (isRelease) {
            return
        }
        // 记录滚动的位置
        mConvertViewModel.saveScrollPosition("convert_search", mLayoutManager)
        (activity as? BaseActivity)?.unRegisterBackPressed()
    }

    fun setLayoutWithSoftInput(softInputHeight: Int, navigationHeight: Int) {
        if (this::mSearchBottomArea.isInitialized) {
            DebugUtil.d(TAG, "setLayoutWithSoftInput bottomMargin = $softInputHeight")
            mAdapter.scrollToKeyWordPosition(mConvertViewModel.currentPos)
            mSearchBottomArea.updateLayoutParams<ViewGroup.MarginLayoutParams> {
                // 软键盘弹起，margin减去navigationBar的高度，让软键盘弹起来底部区域没有白色间隔块
                bottomMargin = if (softInputHeight > 0) (softInputHeight - navigationHeight) else softInputHeight
            }
        }
    }
}