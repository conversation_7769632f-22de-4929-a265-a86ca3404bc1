/*
 Copyright (C), 2008-2024 OPLUS Mobile Comm Corp., Ltd.
 File: RecordSeedlingSmallCardWidgetProvider
 Description:
 Version: 1.0
 Date: 2024/9/26
 Author: <EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 80319089 2024/9/26 1.0 create
 */

package com.soundrecorder.common.card

import android.content.Context
import android.content.pm.ProviderInfo
import android.os.Bundle
import com.oplus.pantanal.seedling.SeedlingCardWidgetProvider
import com.oplus.pantanal.seedling.bean.SeedlingCard
import com.oplus.pantanal.seedling.bean.SeedlingHostEnum
import com.oplus.pantanal.seedling.util.SeedlingTool
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.modulerouter.SeedingInterface
import com.soundrecorder.modulerouter.recorder.INIT
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.utils.Injector
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.json.JSONObject

class RecordSeedlingSmallCardWidgetProvider : SeedlingCardWidgetProvider() {
    companion object {
        private const val TAG = "RecordSeedlingSmallCardWidgetProvider"
        private val smallCardMap: MutableMap<SeedlingHostEnum, SeedlingCard> = mutableMapOf()
        private const val ANIMATION_TIME_MILLIS = 600L
        const val DONE_DELAY_MILLIS = 800L
        private const val INDEX_INIT_OR_RESUME = 0
        private const val INDEX_PAUSE = 1
        private const val INDEX_DONE = 2

        @Volatile
        var animationInProgress = false
        var saveClick = false
        @Volatile
        var nextIndex = 0
        @Volatile
        var currentIndex = 0
        @Volatile
        var isShowingDone = false

        @JvmStatic
        fun getSeedlingCard(): SeedlingCard? {
            return smallCardMap.values.firstOrNull()
        }

        @JvmStatic
        @Synchronized
        fun refreshSeedlingData(
            seedlingCard: SeedlingCard? = getSeedlingCard(),
            jsonData: JSONObject? = Injector.injectFactory<RecorderServiceInterface>()?.getSeedlingData(),
            updateAll: Boolean = true,
            unRegister: Boolean = false
        ) {
            val card = seedlingCard ?: return
            val curStatus = Injector.injectFactory<RecorderServiceInterface>()?.getCurrentStatus() ?: INIT
            JsonUtils.updateJsonData(jsonData, curStatus)

            switchIndexOrNot(card, jsonData, unRegister)
            JsonUtils.parseTimeTxtClass(jsonData)
            DebugUtil.d(TAG, "Animation in progress: $animationInProgress")
            currentIndex = nextIndex
            nextIndex = jsonData?.getInt(JsonConstants.INDEX) ?: -1
            DebugUtil.d(TAG, "currentIndex: $currentIndex, nextIndex: $nextIndex")
            if (currentIndex == INDEX_DONE && nextIndex == INDEX_PAUSE) {
                DebugUtil.e(TAG, "Status illegal, stay done status")
                JsonUtils.updateJsonDataForDoneState(jsonData)
                currentIndex = INDEX_DONE
                nextIndex = INDEX_DONE
            }

            if (currentIndex == INDEX_INIT_OR_RESUME && nextIndex == INDEX_DONE && curStatus <= 0) {
                DebugUtil.e(TAG, "Status illegal, stay init status")
                JsonUtils.updateJsonDataForInitState(jsonData)
                currentIndex = INDEX_INIT_OR_RESUME
                nextIndex = INDEX_INIT_OR_RESUME
            }

            val scope = CoroutineScope(Dispatchers.Main)
            if (currentIndex == INDEX_DONE && nextIndex == INDEX_INIT_OR_RESUME) {
                isShowingDone = true
                scope.launch {
                    delay(DONE_DELAY_MILLIS)
                    isShowingDone = false
                }
            }
            val delayTime = if (isShowingDone) DONE_DELAY_MILLIS else 0L
            scope.launch {
                DebugUtil.d(TAG, "delayTime: $delayTime")
                delay(delayTime)
                if (jsonData?.has(JsonConstants.INDEX) == true && !animationInProgress) {
                    handleAnimation(card, jsonData, updateAll)
                } else {
                    sendDataToApi(card, jsonData, updateAll)
                }
            }
        }

        private val recorderViewModelApi by lazy {
            Injector.injectFactory<RecorderServiceInterface>()
        }

        private val seedingApi by lazy {
            Injector.injectFactory<SeedingInterface>()
        }

        private fun handleAnimation(card: SeedlingCard, jsonData: JSONObject?, updateAll: Boolean) {
            DebugUtil.d(TAG, "switchIndex in progress.")
            animationInProgress = true
            GlobalScope.launch(Dispatchers.Main) {
                delay(ANIMATION_TIME_MILLIS)
                animationInProgress = false
            }
            updateCardData(card, jsonData, updateAll)
        }

        private fun updateCardData(card: SeedlingCard, jsonData: JSONObject?, updateAll: Boolean) {
            if (seedingApi?.hasReleased() == true) {
                SeedlingTool.updateAllCardData(card, jsonData)
            } else {
                jsonData?.let { seedingApi?.sendUpdateCardData(card, it, updateAll) }
            }
        }

        /**
         * 仅更新数据，不切换index
         * */
        private fun sendDataToApi(card: SeedlingCard, jsonData: JSONObject?, updateAll: Boolean) {
            if (jsonData != null && jsonData.length() > 0) {
                if (seedingApi?.hasReleased() == true) {
                    SeedlingTool.updateAllCardData(card, jsonData)
                    DebugUtil.i(TAG, "Card process not exists, updated by SeedlingTool")
                } else {
                    seedingApi?.sendUpdateCardData(card, jsonData, updateAll)
                    DebugUtil.d(TAG, "Card process exists, updated by SeedlingApi")
                }
            } else {
                DebugUtil.w(TAG, "refreshSeedlingData: jsonData is null or empty")
            }
        }

        /**
         * 通过切换index控制卡片状态
         *     0--初始/录制状态
         *     1--暂停状态
         *     2--保存状态
         * */
        private fun switchIndexOrNot(card: SeedlingCard?, jsonData: JSONObject?, unRegister: Boolean) {
            val curStatus = recorderViewModelApi?.getCurrentStatus()
            val lastStatus = recorderViewModelApi?.getLastStatus()
            val saving = saveClick || jsonData?.optString(JsonConstants.KEY_SMALL_CARD_SAVE_LOTTIE) == JsonConstants.SMALL_CARD_SAVING

            DebugUtil.d(TAG, "curState:$curStatus lastStatus:$lastStatus saving:$saving")
            when {
                saving -> JsonUtils.updateJsonDataForDoneState(jsonData)
                (curStatus == 0 || curStatus == -1 || unRegister) -> JsonUtils.updateJsonDataForInitState(jsonData)
                curStatus == 2 -> JsonUtils.updateJsonDataForPauseState(jsonData)
                curStatus == 1 -> JsonUtils.updateJsonDataToResumeState(jsonData)
            }
        }

        /**
         * 添加卡片的时候同步卡片状态
         * */
        private fun resetCardData(seedlingCard: SeedlingCard? = getSeedlingCard()) {
            val curStatus = recorderViewModelApi?.getCurrentStatus()
            val hasAllPermission = PermissionUtils.hasReadAudioPermission() && PermissionUtils.hasRecordAudioPermission()
            val jsonData = recorderViewModelApi?.getSeedlingData() ?: JSONObject()

            DebugUtil.d(TAG, "curStatus:$curStatus ")

            when (curStatus) {
                -1 -> {
                    JsonUtils.initializeJsonData(jsonData, hasAllPermission)
                    seedlingCard?.let { SeedlingTool.updateAllCardData(it, jsonData) }
                }

                2 -> {
                    JsonUtils.updateJsonDataForPauseStateInReset(jsonData)
                    seedlingCard?.let { SeedlingTool.updateAllCardData(it, jsonData) }
                }

                else -> refreshSeedlingData()
            }
            DebugUtil.d(TAG, "curStatus:$curStatus ")
        }

        /**
         * 恢复权限
         * */
        private fun resetPermission(seedlingCard: SeedlingCard) {
            val jsonData = recorderViewModelApi?.getSeedlingData() ?: JSONObject()
            val hasAllPermission = PermissionUtils.hasReadAudioPermission() && PermissionUtils.hasRecordAudioPermission()

            if (!hasAllPermission) {
                jsonData.put(JsonConstants.NO_PERMISSION, !hasAllPermission)
                jsonData.put(JsonConstants.RECORD_STATE_TXT, JsonConstants.RECORD_STATE_DEFAULT)
                jsonData.put(JsonConstants.RECORD_STATE_ICON, JsonConstants.IMAGE_RECORD_BTN_DEFAULT)
                if (seedingApi?.hasReleased() == null) {
                    SeedlingTool.updateAllCardData(seedlingCard, jsonData)
                }
            }
        }
    }

    override fun attachInfo(context: Context?, info: ProviderInfo?) {
        super.attachInfo(context, info)
    }

    override fun onCardCreate(context: Context, card: SeedlingCard) {
        DebugUtil.i(TAG, "onCardCreate card = $card")
        smallCardMap[card.host] = card
    }

    override fun onCardObserve(context: Context, clientName: String, cards: List<SeedlingCard>) {
        cards.forEach { item ->
            DebugUtil.d(TAG, "card:${item.getSeedlingCardId()}")
            smallCardMap.putIfAbsent(item.host, SeedlingCard.build(item.getSeedlingCardId()))
            resetCardData(item)
        }
    }

    override fun onDestroy(context: Context, card: SeedlingCard) {
        DebugUtil.i(TAG, "onDestroy card = $card")
        smallCardMap.remove(card.host)
    }

    override fun onHide(context: Context, card: SeedlingCard) {
        DebugUtil.i(TAG, "onHide card = $card")
    }

    override fun onShow(context: Context, card: SeedlingCard) {
        DebugUtil.i(TAG, "onShow card = ${card.getSeedlingCardId()}")
        smallCardMap.putIfAbsent(card.host, SeedlingCard.build(card.getSeedlingCardId()))
    }

    override fun onSubscribed(context: Context, card: SeedlingCard) {
        DebugUtil.i(TAG, "onSubscribed card = $card")
    }

    override fun onUnSubscribed(context: Context, card: SeedlingCard) {
        DebugUtil.i(TAG, "onUnSubscribed card = $card")
    }


    override fun onSizeChanged(context: Context, card: SeedlingCard, oldSize: Int, newSize: Int) {
        DebugUtil.i(TAG, "onSizeChanged card = $card oldSize = $oldSize  newSize = $newSize")
    }

    override fun onUpdateData(context: Context, card: SeedlingCard, data: Bundle) {
        DebugUtil.i(TAG, "onUpdateData card = $card")
        /**
         * onUpdateData 必须强制刷新，不然可能导致卡片显示不出来
         */
        refreshSeedlingData(card)

        resetPermission(card)
        DebugUtil.d(TAG, "onUpdateData, card id = ${card.serviceId}", true)
    }
}