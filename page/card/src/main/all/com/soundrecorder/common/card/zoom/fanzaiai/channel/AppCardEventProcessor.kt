/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  AppCardEventProcessor.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/3/18
 * * Author      : W9067780
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.card.zoom.fanzaiai.channel

import android.content.Context
import com.google.gson.GsonBuilder
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.card.zoom.fanzaiai.channel.processor.AudioProgressSimulator
import com.soundrecorder.common.permission.PermissionUtils
import org.hapjs.features.channel.ChannelMessage

class AppCardEventProcessor {
    companion object {
        const val TAG = "AppCardEventProcessor"

        /**
         * 当点击卡片：转摘要
         */
        const val ON_CLICK_START_SUMMARY = 1001

        /**
         * 当点击卡片:转文本
         */
        const val ON_CLICK_START_TEXT = 1002

        /**
         * 当点击卡片:转摘要详情
         */
        const val CHECK_JUMP_SUMMARY_DETAIL = 1003

        /**
         * 当点击卡片:转文本详情
         */
        const val CHECK_JUMP_TEXT_DETAIL = 1007
    }

    /**
     * 处理消息
     */
    fun onMessage(context: Context, message: ChannelMessage) {
        val code: Int = message.code
        if (code == 0) {
            DebugUtil.w(TAG, "message code is 0 ignore.")
            return
        }
        val gson = GsonBuilder().disableHtmlEscaping().create()
        var audioFileInfo = gson.fromJson(message.data.toString(), AudioFileInfo::class.java)
        if (audioFileInfo == null) {
            audioFileInfo = AppCardAudioFileObserver.getAudioFileInfo()
            DebugUtil.w(TAG, "onMessage fileInfo is null. use local data")
        }
        DebugUtil.d(TAG, "onMessage code = $code, AudioFileInfo = ${audioFileInfo.title}")
        kotlin.runCatching {
            when (code) {
                ON_CLICK_START_SUMMARY -> {
                    if (checkPermission(context, code)) {
                        toRequestPermission(code)
                        return
                    }
                    AppCardStateProcessor.tryGenSummary(context, audioFileInfo)
                }
                ON_CLICK_START_TEXT -> {
                    if (checkPermission(context, code)) {
                        toRequestPermission(code)
                        return
                    }
                    AppCardStateProcessor.tryStartConvertText(context, audioFileInfo)
                }

                CHECK_JUMP_SUMMARY_DETAIL -> {
                    if (checkPermission(context, code)) {
                        toRequestPermission(code)
                        return
                    }
                    AppCardStateProcessor.tryJumpSummaryDetail(context, audioFileInfo)
                }

                CHECK_JUMP_TEXT_DETAIL -> {
                    if (audioFileInfo.mediaId <= 0) {
                        DebugUtil.d(TAG, "onMessage jumpTextDetail mediaId is invalid =${audioFileInfo.mediaId}")
                        return
                    }
                    if (checkPermission(context, code)) {
                        toRequestPermission(code)
                    } else {
                        val jumpUri = AppCardAudioFileObserver.genTextJumpUri(audioFileInfo)
                        AppCardMessageSender.sendCheckResult(true, jumpUri)
                    }
                }

                AppCardStateProcessor.ON_CARD_SHOW -> {
                    AppCardStateProcessor.onRecordDelete()
                    AppCardStateProcessor.cardState = code
                    tryStartSummaryProgress(code, audioFileInfo)
                    DebugUtil.d(TAG, "onMessage cardState=$code")
                }
                AppCardStateProcessor.ON_CARD_HIDE,
                AppCardStateProcessor.ON_CARD_DESTROY -> {
                    AppCardStateProcessor.cardState = code
                    DebugUtil.d(TAG, "onMessage cardState=$code")
                }

                else -> DebugUtil.d(TAG, "onMessage code=$code")
            }
        }.onFailure {
            DebugUtil.d(TAG, "onMessage error: ${it.message}")
        }
    }

    /**
     * 通过前端跳转到TransparentActivity去获取权限
     */
    private fun toRequestPermission(type: Int) {
        val jumpUri = AppCardAudioFileObserver.genTransparentActivityJumpUri(type)
        AppCardMessageSender.sendCheckResult(true, jumpUri)
        DebugUtil.d(TAG, "toRequestPermission jumpUri: $jumpUri")
    }

    private fun checkPermission(context: Context, type: Int): Boolean {
        val showUserNotice = PermissionUtils.getNextAction() == PermissionUtils.SHOULD_SHOW_USER_NOTICE
        val needHandlePermission = when (type) {
            ON_CLICK_START_SUMMARY -> showUserNotice || !PermissionUtils.hasReadAudioPermission()
            ON_CLICK_START_TEXT -> {
                showUserNotice || !PermissionUtils.isStatementConvertGranted(context)
                        || !PermissionUtils.hasReadAudioPermission()
            }

            CHECK_JUMP_SUMMARY_DETAIL,
            CHECK_JUMP_TEXT_DETAIL -> showUserNotice

            else -> showUserNotice
        }
        DebugUtil.i(TAG, "checkPermission needHandlePermission =$needHandlePermission")
        return needHandlePermission
    }

    private fun tryStartSummaryProgress(code: Int, audioFileInfo: AudioFileInfo) {
        val isPlaying = AudioProgressSimulator.isPlaying()
        DebugUtil.d(TAG, "tryStartSummaryProgress isPlaying=$isPlaying isProgress=${AppCardStateProcessor.checkIsGenSummary(audioFileInfo.mediaId)}")
        if (code == AppCardStateProcessor.ON_CARD_SHOW && AppCardStateProcessor.checkIsGenSummary(audioFileInfo.mediaId) && !isPlaying) {
            AppCardStateProcessor.startSendSummaryProgress(AudioProgressSimulator.MOCK_START_PROGRESS, audioFileInfo.mediaId)
        }
    }
}

