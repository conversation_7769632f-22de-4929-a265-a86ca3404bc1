/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: DataQueyUtil
 * Description:
 * Version: 1.0
 * Date: 2025/3/29
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/3/29 1.0 create
 */

package com.soundrecorder.common.card.zoom.fanzaiai.channel

import android.provider.MediaStore
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.card.zoom.fanzaiai.channel.AppCardAudioFileObserver.genNoteJumpUri
import com.soundrecorder.common.card.zoom.fanzaiai.channel.AppCardAudioFileObserver.genTextJumpUri
import com.soundrecorder.common.card.zoom.fanzaiai.channel.AppCardAudioFileObserver.getAISummaryJumpUri
import com.soundrecorder.common.card.zoom.fanzaiai.channel.processor.AudioProgressSimulator
import com.soundrecorder.common.db.CursorHelper
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.NoteDbUtils
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.summary.SummaryInterface
import com.soundrecorder.modulerouter.utils.Injector

object DataQueryUtil {

    private const val TAG = "DataQueryUtil"

    private val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    private val summaryApi by lazy {
        Injector.injectFactory<SummaryInterface>()
    }

    /**
     * 获取媒体库全部录音中最新一条数据
     * @param excludeRecording 不包含正在录音的文件
     * @return
     */
    @JvmStatic
    fun getNewestAudioInfo(excludeRecording: Boolean = true): AudioFileInfo? {
        var whereClause = CursorHelper.getAllRecordForFilterAndQueryWhereClause(BaseApplication.getAppContext())
        var selectionArg = CursorHelper.getsAcceptableAudioTypes()
        val orderClause = MediaStore.Audio.Media.DATE_MODIFIED + " DESC," + MediaStore.Audio.Media.TITLE + " DESC"
        if (excludeRecording) {
            // 当前正在录制的音频，过滤该条数据
            val fileBeingRecorded = recorderViewModelApi?.getFileBeingRecorded()
            if (!fileBeingRecorded.isNullOrBlank()) {
                whereClause += " and " + MediaStore.Audio.Media.DATA + " != ?"
                val argsList: MutableList<String> = selectionArg.toMutableList()
                argsList.add(fileBeingRecorded)
                selectionArg = argsList.toTypedArray()
            }
        }

        runCatching {
            BaseApplication.getAppContext().contentResolver.query(
                MediaDBUtils.BASE_URI, CursorHelper.getProjection(),
                whereClause, selectionArg, orderClause
            )?.use {
                if (it.count > 0 && it.moveToFirst()) {
                    val audioFileInfo = AudioFileInfo()
                    val columnIndexDisplayName = it.getColumnIndexOrThrow(MediaStore.Audio.Media.DISPLAY_NAME)
                    val columnIndexData = it.getColumnIndexOrThrow(MediaStore.Audio.Media.DATA)
                    val columnIndexRelativePath = it.getColumnIndexOrThrow(MediaStore.Audio.Media.RELATIVE_PATH)
                    val columnIndexDateModified = it.getColumnIndexOrThrow(MediaStore.Audio.Media.DATE_MODIFIED)
                    val columnIndexMediaId = it.getColumnIndexOrThrow(MediaStore.Audio.Media._ID)
                    val columnIndexDuration = it.getColumnIndexOrThrow(MediaStore.Audio.Media.DURATION)
                    val columnIndexSize = it.getColumnIndexOrThrow(MediaStore.Audio.Media.SIZE)
                    val columnIndexMimeType = it.getColumnIndexOrThrow(MediaStore.Audio.Media.MIME_TYPE)
                    val columnIndexDateAdd = it.getColumnIndexOrThrow(MediaStore.Audio.Media.DATE_ADDED)

                    audioFileInfo.displayName = it.getString(columnIndexDisplayName)
                    audioFileInfo.title = audioFileInfo.displayName
                    audioFileInfo.data = it.getString(columnIndexData)
                    audioFileInfo.relativePath = it.getString(columnIndexRelativePath)
                    audioFileInfo.dateModified = it.getLong(columnIndexDateModified)
                    audioFileInfo.dateAdd = it.getLong(columnIndexDateAdd)
                    audioFileInfo.mediaId = it.getLong(columnIndexMediaId)
                    audioFileInfo.mDuration = it.getLong(columnIndexDuration)
                    audioFileInfo.size = it.getLong(columnIndexSize)
                    audioFileInfo.mimeType = it.getString(columnIndexMimeType)
                    audioFileInfo.time = System.currentTimeMillis()
                    DebugUtil.d(TAG, "getNewestAudioInfo displayName ${audioFileInfo.displayName} mediaId=${audioFileInfo.mediaId}")
                    return audioFileInfo
                }
            }
        }.onFailure { DebugUtil.e(TAG, "getNewestAudioInfo error $it") }

        return null
    }

    @JvmStatic
    fun processSummaryOrConvertStatus(audioFileInfo: AudioFileInfo): AudioFileInfo {
        var supportGlobalSummary = false
        if (summaryApi?.getSupportRecordSummaryValue()?.value == null) {
            summaryApi?.initSupportSummary(true)
            supportGlobalSummary = summaryApi?.getRealTimeSupportRecordSummary() == true
        } else {
            supportGlobalSummary = summaryApi?.getSupportRecordSummaryValue()?.value == true
        }
        DebugUtil.d(TAG, "processSummaryOrConvertStatus supportGlobalSummary:$supportGlobalSummary")
        if (supportGlobalSummary) {
            if (AppCardStateProcessor.checkIsGenSummary(audioFileInfo.mediaId)) {
                audioFileInfo.status = STATUS_CONVERT_SUMMARY_IN_PROGRESS
                audioFileInfo.progress = AudioProgressSimulator.MOCK_START_PROGRESS
            } else {
                val noteData = NoteDbUtils.queryNoteByMediaId(audioFileInfo.mediaId.toString())
                audioFileInfo.progress = 0
                if (summaryApi?.getAISummaryIsExists(audioFileInfo.mediaId) == true) {
                    audioFileInfo.status = STATUS_LOOK_AI_SUMMARY
                    audioFileInfo.uri = getAISummaryJumpUri(audioFileInfo)
                } else if (noteData != null) {
                    audioFileInfo.status = STATUS_LOOK_NOTE_SUMMARY
                    audioFileInfo.uri = genNoteJumpUri(noteData.mediaId, noteData.recordUUID)
                } else {
                    audioFileInfo.status = STATUS_CONVERT_SUMMARY
                }
            }
        } else {
            if (AppCardStateProcessor.checkIsConvertText(audioFileInfo.mediaId)) {
                audioFileInfo.status = STATUS_CONVERT_TEXT_IN_PROGRESS
                audioFileInfo.progress = AudioProgressSimulator.MOCK_START_PROGRESS
            } else {
                audioFileInfo.progress = 0
                val textRecord = ConvertDbUtil.selectByRecordId(audioFileInfo.mediaId)
                if (textRecord?.completeStatus == ConvertDbUtil.CONVERT_COMP_STATUS_COMPLETE) {
                    audioFileInfo.status = STATUS_LOOK_TEXT
                    audioFileInfo.uri = genTextJumpUri(audioFileInfo)
                } else {
                    audioFileInfo.status = STATUS_CONVERT_TEXT
                }
            }
        }
        return audioFileInfo
    }
}