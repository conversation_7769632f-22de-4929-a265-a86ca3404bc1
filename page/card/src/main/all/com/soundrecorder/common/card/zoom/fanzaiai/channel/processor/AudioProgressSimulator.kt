/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  AudioProgressSimulator.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/3/18
 * * Author      : W9067780
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.card.zoom.fanzaiai.channel.processor

import com.soundrecorder.base.utils.DebugUtil
import java.util.Timer
import java.util.TimerTask

object AudioProgressSimulator {

    private const val TAG = "AudioProgressSimulator"

    private var timer: Timer? = null

    private const val STEP = 2000L

    private const val MIN_STEP_PROGRESS = 3

    private const val DEFAULT_DURING = 20 * 1000L

    private const val MAX_PROGRESS = 90

    private var isPlaying = false

    private const val DEFAULT_PROGRESS = 100

    private const val DELAY = 0L

    private const val UPDATE_INTERVAL = 1000L

    const val MOCK_START_PROGRESS = 40

    private var progress = 0

    fun startSendProgress(startProgress: Int = 0, during: Long, onProgressUpdate: (Int) -> Unit) {
        DebugUtil.d(TAG, "startSendProgress during=$during")
        var total = during
        if (total == 0L) {
            total = DEFAULT_DURING
        }
        isPlaying = true
        progress = startProgress
        timer = Timer()
        timer?.scheduleAtFixedRate(object : TimerTask() {
            override fun run() {
                progress += getStepProgress(total)
                DebugUtil.d(TAG, "startSendProgress progress=$progress")
                if (progress > MAX_PROGRESS) {
                    stopSendProgress()
                } else {
                    if (isPlaying) {
                        onProgressUpdate.invoke(progress)
                    } else {
                        DebugUtil.w(TAG, "ignore onProgressUpdate isPlaying is false")
                    }
                }
            }
        }, DELAY, UPDATE_INTERVAL)
    }

    private fun getStepProgress(total: Long): Int {
        var stepProgress = (STEP * DEFAULT_PROGRESS / total).toInt()
        if (stepProgress < MIN_STEP_PROGRESS) {
            stepProgress = MIN_STEP_PROGRESS
        }
        return stepProgress
    }

    fun stopSendProgress() {
        isPlaying = false
        timer?.cancel()
        timer = null
        DebugUtil.d(TAG, "stopSendProgress")
    }

    fun isPlaying(): Boolean {
        return isPlaying
    }
}