/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: EditWaveItemViewTest
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord.wave

import android.content.Context
import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.editrecord.views.wave.EditWaveItemView
import com.soundrecorder.editrecord.views.wave.EditWaveRecyclerView
import com.soundrecorder.editrecord.EditRecordActivity
import com.soundrecorder.editrecord.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class EditWaveItemViewTest {

    private var mContext: Context? = null
    private var mController: ActivityController<EditRecordActivity>? = null

    @Before
    fun setUp() {
        mContext = BaseApplication.getAppContext()
        mController = Robolectric.buildActivity(EditRecordActivity::class.java)
    }

    @After
    fun tearDown() {
        mController = null
        mContext = null
    }

    @Test
    fun should_notNull_when_init() {
        mController?.get()?.let {
            val itemView = EditWaveItemView(it)
            Assert.assertNotNull(Whitebox.getInternalState(itemView, "mHandler"))
        }
    }

    @Test
    fun should_notZero_when_initBasicInfo() {
        mController?.get()?.let {
            val itemView = EditWaveItemView(it)
            Whitebox.invokeMethod<Any>(itemView, "initBasicInfo", it)
            Assert.assertTrue(Whitebox.getInternalState<Int>(itemView, "mHandlerWidth") != 0)
        }
    }

    @Test
    fun should_notNull_when_initPaint() {
        mController?.get()?.let {
            val itemView = EditWaveItemView(it)
            Whitebox.invokeMethod<Any>(itemView, "initPaint", it)
            Assert.assertNotNull(Whitebox.getInternalState<Int>(itemView, "mLastLinePaint"))
        }
    }

    @Test
    fun should_equals_when_getXByTime() {
        mController?.get()?.let {
            val itemView = EditWaveItemView(it)
            Whitebox.setInternalState(itemView, "mViewIndex", 2)
            Whitebox.setInternalState(itemView, "mPxPerMs", 10)
            val time = 9000L
            val result = Whitebox.invokeMethod<Float>(itemView, "getXByTime", time)
            Assert.assertEquals(30000F, result)
        }
    }

    @Test
    fun should_equals_when_isTouchHandler() {
        mController?.get()?.let {
            val itemView = EditWaveItemView(it)
            Assert.assertFalse(itemView.isTouchHandler(1F))

            Whitebox.setInternalState(itemView, "mViewIndex", 1)
            Whitebox.setInternalState(itemView, "mPxPerMs", 10)
            val parent = EditWaveRecyclerView(it, null).apply {
                startRecord = 1000
                endRecord = 2000
            }
            Whitebox.setInternalState(itemView, "mParent", parent)
            Assert.assertFalse(itemView.isTouchHandler(100F))
        }
    }

    @Test
    fun should_NULL_when_onDetachedFromWindow() {
        mController?.get()?.let {
            val itemView = EditWaveItemView(it)
            itemView.onDetachedFromWindow()
            Assert.assertNull(Whitebox.getInternalState(itemView, "mHandler"))
        }
    }

    @Test
    fun should_equals_when_setCurViewIndex() {
        mController?.get()?.let {
            val itemView = EditWaveItemView(it)
            val viewIndex = 20
            itemView.setCurViewIndex(viewIndex)
            Assert.assertEquals(viewIndex, Whitebox.getInternalState<Int>(itemView, "mViewIndex"))
        }
    }


    @Test
    @Throws(Exception::class)
    fun verify_value_when_notifyStartTime() {
        mContext?.let {
            val waveItemView = EditWaveItemView(it)
            waveItemView.setCurViewIndex(1)
            waveItemView.notifyStartTime()
            waveItemView.notifyEndTime()
            Whitebox.invokeMethod<Any>(waveItemView, "notifyTime")
        }
    }

    @Test
    fun verify_value_when_notifyTimeChangeByX() {
        mContext?.let {
            val item = EditWaveItemView(it)
            val waveItemView = Mockito.spy(item)
            Mockito.`when`(waveItemView.width).thenReturn(540)
            waveItemView.setCurViewIndex(1)
            waveItemView.notifyTimeChangeByX(0F)
            waveItemView.notifyTimeChangeByX(1000F)
            waveItemView.notifyTimeChangeByX(400F)
            val waveRecyclerView = EditWaveRecyclerView(mContext, null)
            waveRecyclerView.totalTime = 1000
            Mockito.`when`(waveItemView.parent).thenReturn(waveRecyclerView)
            Assert.assertEquals(waveRecyclerView, waveItemView.parent)
            waveItemView.setCurViewIndex(-10)
            waveItemView.notifyTimeChangeByX(400F)
            waveItemView.setCurViewIndex(10)
            waveRecyclerView.totalTime = 1100
            waveRecyclerView.hitStatus = EditWaveItemView.LEFT
            waveRecyclerView.setCutEndTime(1100)
            waveItemView.notifyTimeChangeByX(400F)
            waveItemView.setCurViewIndex(10)
            waveRecyclerView.totalTime = 900
            waveRecyclerView.hitStatus = EditWaveItemView.LEFT
            waveRecyclerView.setCutEndTime(900)
            waveItemView.notifyTimeChangeByX(400F)
            waveRecyclerView.hitStatus = EditWaveItemView.RIGHT
            waveRecyclerView.setCutStartTime(900)
            waveItemView.notifyTimeChangeByX(400F)
        }
    }

    @Test
    @Throws(Exception::class)
    fun verify_value_when_getTimeByLocX() {
        mContext?.let {
            val waveItemView = EditWaveItemView(it)
            waveItemView.setCurViewIndex(1)
            Whitebox.setInternalState(waveItemView, "mPxPerMs", 1)
            Assert.assertEquals(
                0L,
                Whitebox.invokeMethod(waveItemView, "getTimeByLocX", -1000f)
            )
            Assert.assertEquals(
                1000L,
                Whitebox.invokeMethod(waveItemView, "getTimeByLocX", 1000f)
            )
        }
    }
}