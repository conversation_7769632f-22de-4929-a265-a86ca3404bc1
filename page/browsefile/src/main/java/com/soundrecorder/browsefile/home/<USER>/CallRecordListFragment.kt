/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CallRecordListFragment.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/1/15
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.home.call

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.animation.AlphaAnimation
import android.view.animation.Animation
import android.view.animation.PathInterpolator
import android.widget.ScrollView
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.annotation.StringRes
import androidx.appcompat.app.AlertDialog
import androidx.core.animation.addListener
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.children
import androidx.core.view.forEach
import androidx.core.view.isVisible
import androidx.core.view.size
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.commit
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.lifecycle.viewModelScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.bottomnavigation.COUINavigationView
import com.coui.appcompat.material.navigation.NavigationBarView
import com.coui.uikit.demo.navigationview.BottomMarginView
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.StorageManager
import com.soundrecorder.base.backpressed.OnBackPressedListener
import com.soundrecorder.base.ext.dismissWhenShowing
import com.soundrecorder.base.ext.getStringExtraSecure
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.splitwindow.BaseFragment
import com.soundrecorder.base.splitwindow.SplitWindowUtil
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.DensityUtil.px2dp
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.base.utils.NumberConstant.NUMBER_L100
import com.soundrecorder.base.utils.NumberConstant.NUMBER_L300
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.base.view.DeDuplicateInsetsCallback
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.databinding.FragmentCallRecordListBinding
import com.soundrecorder.browsefile.home.BrowseFragment
import com.soundrecorder.browsefile.home.ReceiverUtils
import com.soundrecorder.browsefile.home.RecordItemDragDelegate
import com.soundrecorder.browsefile.home.dialog.navigation.NavigationViewManager
import com.soundrecorder.browsefile.home.item.BrowseAdapter
import com.soundrecorder.browsefile.home.item.BrowseAdapter.Companion.TYPE_HEADER_CALL_GROUP_MORE
import com.soundrecorder.browsefile.home.item.FastPlayHelper
import com.soundrecorder.browsefile.home.item.IBrowseViewHolderListener
import com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel
import com.soundrecorder.browsefile.home.load.BrowseViewModel
import com.soundrecorder.browsefile.home.load.ConvertingInfo
import com.soundrecorder.browsefile.home.load.ViewStatus
import com.soundrecorder.browsefile.home.view.group.CallGroupMoreHeaderView
import com.soundrecorder.browsefile.home.view.group.util.GroupViewModel
import com.soundrecorder.browsefile.home.view.group.view.GroupChooseFragment
import com.soundrecorder.browsefile.parentchild.BrowseFileActivityViewModel
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.buryingpoint.SummaryStaticUtil
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.databean.GroupInfo
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.databean.StartPlayModel
import com.soundrecorder.common.db.GroupInfoDbUtil.genDefaultCallGroupInfo
import com.soundrecorder.common.dialog.LoadingDialog
import com.soundrecorder.common.fileoperator.CheckOperate
import com.soundrecorder.common.fileoperator.delete.DeleteFileDialogUtil
import com.soundrecorder.common.fileoperator.recover.RecoverFileDialogUtil
import com.soundrecorder.common.flexible.FollowDialogRestoreUtils
import com.soundrecorder.common.permission.PermissionActivity
import com.soundrecorder.common.permission.PermissionDialogUtils
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.share.ShareTypeLink
import com.soundrecorder.common.share.ShareTypeNote
import com.soundrecorder.common.task.ActivityTaskUtils
import com.soundrecorder.common.utils.FileDealUtil
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.common.utils.LandScapeUtil
import com.soundrecorder.common.utils.RecordFileChangeNotify
import com.soundrecorder.common.utils.SendSetUtil
import com.soundrecorder.common.utils.TipUtil
import com.soundrecorder.common.utils.VibrateUtils
import com.soundrecorder.common.utils.ViewUtils.getUnDisplayViewHeight
import com.soundrecorder.common.utils.ViewUtils.onRelease
import com.soundrecorder.common.utils.taskbar.TaskBarUtil
import com.soundrecorder.common.utils.visible
import com.soundrecorder.common.widget.OSImageView
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_CONVERT_STATUS
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_RECORD_ID
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_SMART_NAME_CONVERT_COMPLETE
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_SMART_NAME_NAME_TEXT
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_SMART_NAME_STATUS
import com.soundrecorder.modulerouter.playback.NOTIFY_CONVERT_STATUS_UPDATE
import com.soundrecorder.modulerouter.playback.NOTIFY_SMART_NAME_STATUS_UPDATE
import com.soundrecorder.modulerouter.playback.PlayBackInterface
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.PAGE_FROM_CALL_RECORD
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant
import com.soundrecorder.modulerouter.share.ShareAction
import com.soundrecorder.modulerouter.smartname.ISmartNameManager
import com.soundrecorder.modulerouter.summary.ACTION_SUMMARY_STATE_CHANGED
import com.soundrecorder.modulerouter.summary.BUNDLE_CALL_ID
import com.soundrecorder.modulerouter.summary.BUNDLE_FROM_WHERE
import com.soundrecorder.modulerouter.summary.BUNDLE_MEDIA_ID
import com.soundrecorder.modulerouter.summary.BUNDLE_NOTE_ID
import com.soundrecorder.modulerouter.summary.SummaryInterface
import com.soundrecorder.modulerouter.utils.Injector
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference

// the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
private const val ARG_GROUP_INFO = "groupInfo"
private const val ARG_CALLER_NAME = "callerName"
private const val ARG_CIRCLE_COLOR = "circleColor"

/**
 * A simple [Fragment] subclass.
 * Use the [CallRecordListFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class CallRecordListFragment : BaseFragment<FragmentCallRecordListBinding>(),
    OnBackPressedListener,
    ReceiverUtils.IReceiverOnReceive {

    private var callerName: String? = null
    private var circleColor: String? = null
    private var groupInfo: GroupInfo? = null
    private var nagivationHeight: Int = 0
    private var taskId: Int = 0
    private lateinit var mBrowseViewModel: BrowseViewModel
    private val mBrowseFileActivityViewModel: BrowseFileActivityViewModel by activityViewModels()
    private var mAdapter: BrowseAdapter? = null
    private var dragJob: Job? = null
    private var itemDragDelegate: RecordItemDragDelegate? = null
    private var dragViewReference: WeakReference<View>? = null

    private var isDeviceSecureDelete = false
    private var mOptionsMenu: Menu? = null
    private var mNavigationViewManager: NavigationViewManager? = null
    private var mNavigationLayout: View? = null
    private var mBottomNavigationView: COUINavigationView? = null
    private var mNavigationAnim: ObjectAnimator? = null
    private var mBottomMarginView: BottomMarginView? = null

    //loading动画
    private var mLoadingViewLayout: View? = null
    private var mLoadingView: View? = null

    //无权限动画
    private var mPermissionScrollview: ScrollView? = null
    private var mBtnPermissionOperate: TextView? = null

    //空页面动画
    private var mEmptyScrollview: ScrollView? = null
    private var mEmptyOSImageView: OSImageView? = null

    private var disableDialog: AlertDialog? = null
    private var clickedItemSummaryNoteId: String? = null

    private var mCallGroupMoreHeaderView: CallGroupMoreHeaderView? = null
    private var mReceiverUtils: ReceiverUtils? = null
    private val mGroupViewModel: GroupViewModel by activityViewModels()
    private var isLoadFinished = false
    private var mSmartNameMangerImpl: ISmartNameManager? = null
    private var mFilePermissionDialog: AlertDialog? = null

    private var isNeedSmartName = false
    private var needSmartNameMediaList = mutableListOf<Long>()

    private val refreshRunnable = {
        refreshData()
    }

    //分享txt，转文本文件超过50M的时候，显示“请稍后...”dialog
    private var shareWaitingDialog: LoadingDialog? = null

    private val summaryApi by lazy {
        Injector.injectFactory<SummaryInterface>()
    }

    private val playbackAction by lazy {
        Injector.injectFactory<PlayBackInterface>()
    }

    private val shareAction by lazy {
        Injector.injectFactory<ShareAction>()
    }

    private val recordOptionCompletedListener =
        object : NavigationViewManager.OnOptionCompletedListener {
            override fun onOptionCompleted(
                option: Int,
                selectedMediaIdList: ArrayList<String>?,
                deleteRecordList: ArrayList<Record>?,
                isDeleteAll: Boolean
            ): Long {
                DebugUtil.i(logTag, "onOptionCompleted > $option")
                var delayTime = 0L
                mBrowseViewModel.exitEditMode()
                if (option == CheckOperate.OPERATE_DELETE || option == CheckOperate.OPERATE_RECOVER) {
                    if (mAdapter?.isContentNotEmpty() == true) {
                        if (deleteRecordList?.size == mBrowseViewModel.getShowRecordCount()) {
                            startAlphaAnimation()
                        } else {
                            delayTime = mAdapter?.deleteItems(selectedMediaIdList, refreshRunnable) ?: 0L
                        }
                    } else {
                        refreshRunnable.invoke()
                    }
                    activity?.let {
                        VibrateUtils.vibrate(it)
                    }
                    whetherClearCurrentPlayRecord(deleteRecordList)
                    mBrowseFileActivityViewModel.isCallingRecordEditRefresh.postValueSafe(true)
                } else {
                    refreshRunnable.invoke()
                    mBrowseFileActivityViewModel.isCallingRecordEditRefresh.postValueSafe(true)
                }
                //isDeviceSecureDelete = false
                return delayTime
            }

            override fun onOptionSmartName(option: Int, selectedMediaIdList: MutableList<Long>?) {
                if (selectedMediaIdList.isNullOrEmpty()) {
                    return
                }
                DebugUtil.i(logTag, "onOptionSmartName > $option")
                mBrowseViewModel.exitEditMode()
                mNavigationViewManager?.resetContinueOperator()
                if (!PermissionUtils.hasAllFilePermission()) {
                    showPermissionAllFileDialog(true, selectedMediaIdList)
                    return
                }
                startConvertAndSmartName(selectedMediaIdList)
            }
        }

    private fun clearNeedSmartNameMedias() {
        DebugUtil.d(logTag, "clearNeedSmartNameMedias")
        isNeedSmartName = false
        needSmartNameMediaList.clear()
    }

    private fun addNeedSmartNameMedias(selectedMediaIdList: MutableList<Long>?) {
        if (selectedMediaIdList.isNullOrEmpty()) {
            return
        }
        DebugUtil.d(logTag, "addNeedSmartNameMedias")
        isNeedSmartName = true
        needSmartNameMediaList.clear()
        needSmartNameMediaList.addAll(selectedMediaIdList)
    }

    private fun startConvertAndSmartName(selectedMediaIdList: MutableList<Long>?) {
        if (PermissionUtils.hasAllFilePermission()) {
            initSmartNameManagerImpl()
            mSmartNameMangerImpl?.convertStartSmartNameClickHandle(activity, selectedMediaIdList, PAGE_FROM_CALL_RECORD)
            clearNeedSmartNameMedias()
        }
    }

    private fun showPermissionAllFileDialog(needSmartName: Boolean = false, selectedMediaIdList: MutableList<Long>? = null) {
        if (ClickUtils.isFastDoubleClick()) {
            return
        }
        activity?.let {
            mFilePermissionDialog = PermissionDialogUtils.showPermissionAllFileAccessDialog(
                it,
                object : PermissionDialogUtils.PermissionDialogListener {
                    override fun onClick(alertType: Int, isOk: Boolean, permissions: ArrayList<String>?) {
                        DebugUtil.d(TAG, "showPermissionAllFileDialog, isOk:$isOk, permissions:$permissions")
                        if (isOk) {
                            if (needSmartName) {
                                addNeedSmartNameMedias(selectedMediaIdList)
                            }
                            PermissionUtils.goToAppAllFileAccessConfigurePermissions(it)
                        }
                    }
                })
        }
    }

    override fun onResume() {
        super.onResume()
        checkRestoreSmartName()
    }

    private fun checkRestoreSmartName(): Boolean {
        DebugUtil.d(
            TAG, "checkRestoreSmartName, isNeedSmartName:$isNeedSmartName, " +
                    "needSmartNameMediaList:${needSmartNameMediaList.size}"
        )
        if (isNeedSmartName && needSmartNameMediaList.isNotEmpty()) {
            startConvertAndSmartName(needSmartNameMediaList)
            return true
        }
        return false
    }

    private val onMoveGroupListener =
        object : NavigationViewManager.OnMoveGroupListener {
            override fun onMoveGroup(selectedRecordList: ArrayList<Record>) {
                mGroupViewModel.mutableSelectRecordings.value = selectedRecordList
                showGroupChooseFragment()
                mGroupViewModel.onMoveGroupCallBack.value =
                    object : GroupChooseFragment.OnMoveGroupCallBack {
                        override fun onPreMoveGroup() {}

                        override fun onMovedGroup(showWaitingDialog: Boolean) {
                            DebugUtil.d(TAG, "onMovedGroup, selectedRecordList:${selectedRecordList.size}")
                            mBinding.toolbar.post {
                                mBrowseViewModel.exitEditMode()
                                //退出编辑模式，状态重置，否则可能导致新增记录列表无法刷新
                                mNavigationViewManager?.resetContinueOperator()
                            }
                            refreshData()
                            mBrowseFileActivityViewModel.isCallingRecordEditRefresh.postValueSafe(true)
                            if (selectedRecordList.size == mBrowseViewModel.getShowRecordCount()) {
                                //全部移出，退出页面
                                mBrowseFileActivityViewModel.clearCallGroupMoreData()
                            }
                        }
                    }
            }
        }

    /**
     * 如果删除或恢复时当前录音正在播放，清除当前播放录音
     */
    private fun whetherClearCurrentPlayRecord(deleteRecordList: ArrayList<Record>?) {
        if (deleteRecordList?.isNotEmpty() == true
            && mBrowseFileActivityViewModel.mCurrentPlayRecordData.value != null
        ) {
            deleteRecordList.forEach { record ->
                if (isCurrentPlayRecord(record)) {
                    mBrowseFileActivityViewModel.clearPlayRecordData()
                    return
                }
            }
        }
    }

    private fun isCurrentPlayRecord(record: Record): Boolean {
        return mBrowseFileActivityViewModel.mCurrentPlayRecordData.value?.mediaId == record.id
    }

    private fun startAlphaAnimation() {
        val alphaAnimation = AlphaAnimation(NumberConstant.NUM_F1_0, NumberConstant.NUM_F0_0).apply {
            duration = NUMBER_L300
            interpolator = PathInterpolator(NumberConstant.NUM_F0_3, NumberConstant.NUM_F0_0, NumberConstant.NUM_F0_1, NumberConstant.NUM_F1_0)
            setAnimationListener(object : Animation.AnimationListener {
                override fun onAnimationStart(animation: Animation?) {}

                override fun onAnimationEnd(animation: Animation?) {
                    mBinding.rvCallRecordList.isVisible = false
                    refreshRunnable.invoke()
                    mBrowseFileActivityViewModel.clearCallGroupMoreData()
                }

                override fun onAnimationRepeat(animation: Animation?) {}
            })
        }
        mBinding.rvCallRecordList.startAnimation(alphaAnimation)
    }

    override fun layoutId(): Int {
        return R.layout.fragment_call_record_list
    }

    override fun onViewCreated(savedInstanceState: Bundle?) {
        setBrowseViewModel()
        initiateWindowInsets()
        configToolbar()
        setListViewAdapter()
        initDragDelegate()
        observe()
        registerReceiver()
        initSmartNameManagerImpl()
    }

    private fun initSmartNameManagerImpl() {
        if (mSmartNameMangerImpl == null) {
            mSmartNameMangerImpl = playbackAction?.getSmartNameManager()
        }
    }

    private fun registerReceiver() {
        if (mReceiverUtils == null) {
            mReceiverUtils = activity?.let { ReceiverUtils(it.applicationContext, mBrowseFileActivityViewModel.isFromOtherApp) }
            mReceiverUtils?.registerAllBrowseFileReceiver()
            mReceiverUtils?.setOnReceive(this)
        }
    }

    private fun unregisterReceiver() {
        mReceiverUtils?.unRegisterAllBrowseFileReceiver()
        mReceiverUtils = null
    }

    private fun setBrowseViewModel() {
        createViewModel()
        taskId = activity?.taskId ?: 0
        DebugUtil.i(TAG, "taskId : $taskId")
        mBrowseViewModel.taskId = taskId
        ensureLiveDataParams()
    }

    private fun createViewModel() {
        mBrowseViewModel = ViewModelProvider(this)[BrowseViewModel::class.java]
        mBrowseViewModel.initLifecycle(this)
        mBrowseViewModel.isShowCallMorePage = true
    }

    private fun ensureLiveDataParams() {
        if (ItemBrowseRecordViewModel.liveEditMode[taskId] == null) {
            ItemBrowseRecordViewModel.liveEditMode[taskId] = MutableLiveData<Boolean>()
        }
        if (ItemBrowseRecordViewModel.liveDragMode[taskId] == null) {
            ItemBrowseRecordViewModel.liveDragMode[taskId] = MutableLiveData<Boolean>()
        }
        if (ItemBrowseRecordViewModel.liveSelectedMap[taskId] == null) {
            ItemBrowseRecordViewModel.liveSelectedMap[taskId] = MutableLiveData()
        }
        if (ItemBrowseRecordViewModel.liveAddFooter[taskId] == null) {
            ItemBrowseRecordViewModel.liveAddFooter[taskId] = MutableLiveData<Int>()
        }
        if (ItemBrowseRecordViewModel.liveConvertStatus[taskId] == null) {
            ItemBrowseRecordViewModel.liveConvertStatus[taskId] = MutableLiveData()
        }
    }

    private fun initDragDelegate() {
        if (itemDragDelegate == null) {
            itemDragDelegate = RecordItemDragDelegate(context, taskId)
        }
        itemDragDelegate?.setReceiveDragListener(activity?.window?.decorView)
    }

    private fun observe() {
        if (groupInfo != null) {
            mBrowseViewModel.currentGroup.value = groupInfo
        }
        ItemBrowseRecordViewModel.liveSelectedMap[taskId]?.observe(
            viewLifecycleOwner
        ) {
            updateSelectMap()
        }

        ItemBrowseRecordViewModel.liveAddFooter[taskId]?.observe(viewLifecycleOwner) {
            mBinding.rvCallRecordList.removeCallbacks(updateFooterRunnable)
            //虽然addFooter后不用notifyDataChange, 还是采用了postDelay的方式，因为冷启动后直接进编辑模式，navigationView的measureHeight拿不到导致最后一条被遮住一部分
            mBinding.rvCallRecordList.postDelayed(updateFooterRunnable, it.toLong())
        }

        mBrowseViewModel.currentGroup.observe(viewLifecycleOwner) {
            refreshData()
        }

        mBrowseViewModel.liveDataList.observe(viewLifecycleOwner) {
            onDataChanged(it)
        }
        mBrowseViewModel.browseModel.liveViewStatus.observe(
            viewLifecycleOwner
        ) { viewStatus: ViewStatus? ->
            updateViewStatus(viewStatus)
        }
        ItemBrowseRecordViewModel.liveEditMode[taskId]?.observe(
            viewLifecycleOwner
        ) { isEditMode: Boolean? ->
            updateEditModeChange(isEditMode)
            setNavigationColor()
        }
        initBrowseFileActivityObservers()
        mBrowseViewModel.needShareWaitingDialog.observe(viewLifecycleOwner) {
            if (it.first) {
                when (it.second) {
                    is ShareTypeNote -> showShareWaitingDialog(com.soundrecorder.common.R.string.is_saving)
                    is ShareTypeLink -> showShareWaitingDialog(com.soundrecorder.common.R.string.generating)
                    else -> showShareWaitingDialog()
                }
            } else {
                dismissShareWaitingDialog()
            }
        }

        mBrowseViewModel.showShareLinkPanel.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "observe showShareLinkPanel $it ${mBrowseViewModel.currentLifecycleState}")
            if (it?.isNotEmpty() == true && mBrowseViewModel.currentLifecycleState == Lifecycle.Event.ON_RESUME) {
                activity?.let { activity ->
                    shareAction?.showShareLinkPanel(activity, it, null)
                    mBrowseViewModel.showShareLinkPanel.value = null
                }
            }
        }

        mBrowseViewModel.showShareToast.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "observe showShareToast $it ${mBrowseViewModel.currentLifecycleState}")
            if (it != null && mBrowseViewModel.currentLifecycleState == Lifecycle.Event.ON_RESUME) {
                ToastManager.showShortToast(context, it)
                mBrowseViewModel.showShareToast.value = null
            }
        }
    }

    private fun refreshData() {
        DebugUtil.d(TAG, "refreshData")
        mBrowseViewModel.refreshCallByGroup(groupInfo ?: genDefaultCallGroupInfo(), callerName)
    }

    private fun initBrowseFileActivityObservers() {
        mBrowseFileActivityViewModel.windowType.observe(viewLifecycleOwner) {
            onWindowTypeChanged(it)
        }
        mBrowseFileActivityViewModel.beDeleteSummaryNoteId.observe(viewLifecycleOwner) {
            mBrowseViewModel.updateDataLiveDataByClearNoteId(it)
        }
        mBrowseFileActivityViewModel.isNeedRefresh.observe(viewLifecycleOwner) {
            if (it) {
                refreshData()
            }
        }
    }

    private fun onWindowTypeChanged(windowType: WindowType) {
        when (windowType) {
            WindowType.SMALL -> {
                // 小屏，若有播放内容，则退出编辑模式，取消各种弹窗
                exitEditModeAndDialogInSmallWindow(windowType)
            }

            WindowType.MIDDLE, WindowType.LARGE -> {
                // 小屏切中大屏，快捷播放接续
                mBrowseViewModel.fastPlayHelper.itemBrowseRecordViewModel?.let {
                    if (it.isNeedShowSeekBarArea(mBrowseViewModel.fastPlayHelper.getPlayerState())) {
                        mBrowseFileActivityViewModel.mCurrentPlayRecordData.value = it.toStartPlayModel(
                            mBrowseFileActivityViewModel.isFromOtherApp,
                            mBrowseViewModel.fastPlayHelper.getCurrentPlayerTime(),
                            mBrowseViewModel.fastPlayHelper.mediaPlayerManager.isWholePlaying(),
                            false
                        )
                        mBrowseViewModel.releasePlayer()
                    }
                }
            }
        }
    }

    private fun exitEditModeAndDialogInSmallWindow(windowType: WindowType?) {
        val needClearDialog = (mBrowseFileActivityViewModel.hasPlayPageData()) && (windowType == WindowType.SMALL)
        val exitEditMode = needClearDialog && (ItemBrowseRecordViewModel.liveEditMode[taskId]?.value == true)
        activity?.let {
            if (ActivityTaskUtils.isTaskEmptyExceptActivity(taskId, it)) {
                if (needClearDialog) {
                    //全部录音弹窗
                    mBrowseViewModel.isSingleDialogShowing = false
                }

                if (exitEditMode) {
                    // 销毁设为铃声、分享三方activity
                    activity?.finishActivity(SendSetUtil.REQUEST_CODE_SET_RING)
                    activity?.finishActivity(FileDealUtil.REQUEST_CODE_SHARE)
                    //重命名弹窗
                    mNavigationViewManager?.releaseRenameDialog()
                    mBrowseViewModel.isRenameDialogShowing = false
                    // 删除弹窗
                    mNavigationViewManager?.dismissDeleteDialog()
                    mBrowseViewModel.isDeleteDialogShowing = false
                    mNavigationViewManager?.dismissRecoverDialog()
                    mBrowseViewModel.isRecoverDialogShowing = false
                    mBrowseViewModel.isRecycleDialogAllShowing = false
                    //分享弹窗
                    mNavigationViewManager?.dismissShareDialog()
                    mBrowseViewModel.isShareDialogShowing = false
                    //文本分享弹窗
                    mNavigationViewManager?.dismissShareTextDialog()
                    mBrowseViewModel.isShareTextDialogShowing = false
                    disableDialog.dismissWhenShowing()
                    // 退出编辑模式
                    mBrowseViewModel.exitEditMode()
                }
            }
        }
    }

    private val updateFooterRunnable = Runnable {
        addBlankFooter()
    }

    private fun addBlankFooter() {
        mAdapter?.setFooter(calBlankFooterHeight())
    }

    private fun calBlankFooterHeight(): Int {
        val editMode = ItemBrowseRecordViewModel.liveEditMode[taskId]?.value
        return when {
            editMode == true -> {
                (mBottomNavigationView?.measuredHeight
                    ?: getDimension(com.soundrecorder.common.R.dimen.dp56).toInt()) + getDimension(com.soundrecorder.common.R.dimen.dp30).toInt()
            }

            groupInfo?.isCallingGroup() == true -> getDimension(com.soundrecorder.common.R.dimen.dp30).toInt()
            else -> getDimension(R.dimen.recorder_height).toInt() + getDimension(com.soundrecorder.common.R.dimen.dp30).toInt()
        }
    }

    private fun getDimension(id: Int): Float {
        return BaseApplication.getAppContext().resources.getDimension(id)
    }

    private val updateToolBarMenuRunnable = Runnable {
        setMenuSelectAllVisible()
        mBinding.toolbar.isTitleCenterStyle =
            ItemBrowseRecordViewModel.liveEditMode[taskId]?.value == true
        setMenuNaviBackIcon()
    }

    private fun setMenuNaviBackIcon() {
        val isEditMode = ItemBrowseRecordViewModel.liveEditMode[taskId]?.value ?: false
        if (isEditMode) {
            mBinding.toolbar.setNavigationIcon(null)
            mBinding.toolbar.setNavigationOnClickListener(null)
        } else {
            mBinding.toolbar.setNavigationIcon(com.support.appcompat.R.drawable.coui_back_arrow)
            mBinding.toolbar.setNavigationContentDescription(com.soundrecorder.common.R.string.back)
            mBinding.toolbar.setNavigationOnClickListener {
                mBrowseFileActivityViewModel.clearCallGroupMoreData()
            }
        }
    }

    /**
     * 标题菜单栏-选中全部
     */
    private fun setMenuSelectAllVisible() {
        val isEditMode = ItemBrowseRecordViewModel.liveEditMode[taskId]?.value ?: false
        val height = mBinding.toolbar.measuredHeight
        //进入编辑模式时menu显示为“取消”、“全选”纯文本时Toolbar高度会比之前低，会导致“全部录音”文案向上跳动
        mBinding.toolbar.minimumHeight = if (isEditMode && height > 0) {
            height
        } else {
            BaseApplication.getAppContext().resources.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.recorder_toolbar_height)
        }
        mOptionsMenu?.findItem(R.id.item_select_all)?.isVisible = isEditMode
        mOptionsMenu?.findItem(R.id.item_cancel)?.isVisible = isEditMode
    }

    private fun updateToolBarMenu(delayTime: Long = 0L) {
        mBinding.toolbar.removeCallbacks(updateToolBarMenuRunnable)
        if (delayTime <= 0) {
            updateToolBarMenuRunnable.run()
        } else { //延迟消失，防止点击menu进入编辑模式后，menu未消失前item为空
            mBinding.toolbar.postDelayed(updateToolBarMenuRunnable, delayTime)
        }
        updateSelectMap()
    }

    private fun updateSelectMap() {
        if (ItemBrowseRecordViewModel.liveEditMode[taskId]?.value == true) {
            val selectedCount = ItemBrowseRecordViewModel.liveSelectedMap[taskId]?.value?.size ?: 0
            updateNavigationItemEnable(selectedCount)
            updateSelectAllMenu(selectedCount)
        }
    }

    private fun updateSelectAllMenu(selectedCount: Int) {
        val totalCount = mBrowseViewModel.getShowRecordCount()
        DebugUtil.i(TAG, "updateSelectAllMenu totalCount = $totalCount,selectedCount=$selectedCount")
        val isSelectAll = if (isLoadFinished) {
            selectedCount >= totalCount
        } else {
            false
        }
        val title = if (selectedCount == 0 || !isLoadFinished) {
            getString(com.soundrecorder.common.R.string.choose_item)
        } else if (isSelectAll) {
            getString(com.soundrecorder.common.R.string.selected_all_item)
        } else {
            getString(com.soundrecorder.common.R.string.item_select, selectedCount)
        }
        //mBinding.toolbarTitle.text = title
        mBinding.toolbar.isTitleCenterStyle = true
        mBinding.toolbar.setTitle(title)

        val selectAllMenu = mOptionsMenu?.findItem(R.id.item_select_all)
        selectAllMenu?.let {
            it.isEnabled = totalCount > 0
            if (isSelectAll) {
                it.setTitle(com.soundrecorder.common.R.string.record_delete_all_cancel)
            } else {
                it.setTitle(com.soundrecorder.common.R.string.select_all)
            }
        }
    }

    private fun updateNavigationItemEnable(selectedCount: Int) {
        var enable: Boolean
        mBottomNavigationView?.menu?.forEach {
            when (it.itemId) {
                R.id.item_delete,
                R.id.item_move,
                R.id.item_send -> {
                    enable = (selectedCount > 0)
                    if (enable != it.isEnabled) {
                        it.isEnabled = enable
                    }
                }

                R.id.set_as,
                R.id.item_rename -> {
                    enable = (selectedCount == 1)
                    if (enable != it.isEnabled) {
                        it.isEnabled = enable
                    }
                }
            }
        }
    }

    private fun updateEditModeChange(isEditMode: Boolean?) {
        DebugUtil.i(logTag, "updateEditModeChange >> $isEditMode")
        val isEdit = isEditMode ?: false
        updateToolBarMenu(if (isEditMode == true) NUMBER_L300 else 0)
        updateTitleEditModeChange(isEdit)
        ensureNavigationView()

        switchToNavigationMenu(isEditMode)
    }

    private fun switchToNavigationMenu(isEditMode: Boolean?) {
        //mBottomNavigationView?.switchToMenu(R.menu.bottom_navigation_menu)
        if (isEditMode == true) {
            mBottomMarginView?.setDisplay(true)
        } else {
            mBottomMarginView?.setDisplay(false)
        }

        val selectedCount = ItemBrowseRecordViewModel.liveSelectedMap[taskId]?.value?.size ?: 0
        DebugUtil.d(TAG, "switchToNavigationMenu, selectedCount:$selectedCount")
        updateNavigationItemEnable(selectedCount)
    }

    private fun ensureNavigationView() {
        if ((mNavigationLayout == null) && (!mBinding.mViewStub.isInflated)) {
            mNavigationLayout = mBinding.mViewStub.viewStub?.inflate()
            mBottomNavigationView = mNavigationLayout?.findViewById(R.id.navi_menu_tool)
            //setBottomNavigationHeight()
            mNavigationLayout?.apply {
                visibility = View.GONE
            }
            mNavigationLayout?.updatePadding(bottom = nagivationHeight)
            fixTalkBackFocus()

            mBottomNavigationView?.setItemLayoutType(COUINavigationView.VERTICAL_ITEM_LAYOUT_TYPE)
            mBottomNavigationView?.switchToMenu(R.menu.bottom_navigation_menu, false)
            mBottomNavigationView?.setOnAnimatorListener(object :
                COUINavigationView.OnNavigationEnterExitListener {
                override fun onAnimationEnterEnd() {
                }

                override fun onAnimationExitEnd() {
                    DebugUtil.d(TAG, "switchToNavigationMenu, onAnimationExitEnd:")
                    adjustItemNumber()
                }
            })
            mBottomMarginView = mNavigationLayout?.let { BottomMarginView().addView(it) }

            mBottomNavigationView?.setOnItemSelectedListener(
                NavigationBarView.OnItemSelectedListener OnItemSelectedListener@{ item ->
                    if (ClickUtils.isFastDoubleClick()) {
                        DebugUtil.i(logTag, "isFastDoubleClick return")
                        return@OnItemSelectedListener true
                    }
                    DebugUtil.i(TAG, "item >> $item")
                    onItemSelected(item)
                    true
                })
            val selectedCount = ItemBrowseRecordViewModel.liveSelectedMap[taskId]?.value?.size ?: 0
            updateNavigationItemEnable(selectedCount)
        }
    }

    private fun adjustItemNumber() {
        //val maxCount = mBinding.naviMenuTool.getMaxItemCount(mContainerWidth)
        val menuSize = mBottomNavigationView?.menu?.size ?: 0
        val selectedCount = ItemBrowseRecordViewModel.liveSelectedMap[taskId]?.value?.size ?: 0
        DebugUtil.d(TAG, "adjustItemNumber, menuSize:$menuSize, selectedCount:$selectedCount")
        updateNavigationItemEnable(selectedCount)
    }

    private fun ensureNavigationViewManager() {
        if (mNavigationViewManager == null) {
            mNavigationViewManager = NavigationViewManager(activity, mBottomNavigationView)
            mNavigationViewManager?.supportSmartName = mBrowseFileActivityViewModel.isSupportSmartName()
            mNavigationViewManager?.showRecordCount = mBrowseViewModel.getShowRecordCount()
            mNavigationViewManager?.setGroupInfo(mBrowseViewModel.getCurrentGroup())
            mNavigationViewManager?.onOptionCompletedListener = recordOptionCompletedListener
        }
    }

    private fun onItemSelected(item: MenuItem?) {
        if (mNavigationViewManager == null) {
            mNavigationViewManager = NavigationViewManager(activity, mBottomNavigationView)
        }
        if (mBrowseViewModel.isRenameDialogShowing) {
            mNavigationViewManager?.editDisplayName = mBrowseViewModel.renameContent
            mBrowseViewModel.renameContent = null
            mBrowseViewModel.isRenameDialogShowing = false
        }
        mNavigationViewManager?.supportSmartName = mBrowseFileActivityViewModel.isSupportSmartName()
        mNavigationViewManager?.showRecordCount = mBrowseViewModel.getShowRecordCount()
        mNavigationViewManager?.setGroupInfo(mBrowseViewModel.getCurrentGroup())
        mNavigationViewManager?.onMoveGroupListener = this.onMoveGroupListener
        mNavigationViewManager?.click(item, mBrowseViewModel.viewModelScope, mBrowseViewModel, mBrowseViewModel)
        mNavigationViewManager?.onOptionCompletedListener = recordOptionCompletedListener
    }

    private fun fixTalkBackFocus() {
        var firstItem: View? = null
        if ((mBottomNavigationView?.childCount ?: 0) > 0) {
            firstItem = mBottomNavigationView?.getChildAt(0)
        }
        firstItem?.contentDescription = getString(com.soundrecorder.common.R.string.send)
    }

    private fun updateTitleEditModeChange(isEditMode: Boolean?) {
        DebugUtil.i(logTag, "updateTitleEditModeChange >> $isEditMode")
        mBinding.toolbar.isTitleCenterStyle = isEditMode ?: false
        if (isEditMode == true) {
            mBinding.toolbar.setTitle(resources.getString(com.soundrecorder.common.R.string.choose_item))
        } else {
            mBinding.toolbar.setTitle("")
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.item_select_all -> mBrowseViewModel.selectAllOrNone()
            R.id.item_cancel -> {
                mBinding.toolbar.postDelayed({
                    mBrowseViewModel.exitEditMode()
                    //退出编辑模式，状态重置，否则可能导致新增记录列表无法刷新
                    mNavigationViewManager?.resetContinueOperator()
                }, MENU_PRESS_DELAY_TIME)
            }
        }
        return true
    }

    private fun setNavigationColor() {
        TaskBarUtil.setNavigationColorOnSupportTaskBar(
            navigationHeight = nagivationHeight,
            activity = activity,
            defaultNoTaskBarColor = navigationBarColor(),
            taskBarColor()
        )
    }

    private fun navigationBarColor(): Int? {
        return if (ItemBrowseRecordViewModel.liveEditMode[taskId]?.value == true) {
            // 编辑模式工具栏颜色同背景色不一致，所以需要单独设置
            com.support.appcompat.R.color.coui_color_bottom_bar
        } else {
            (activity as? BaseActivity)?.navigationBarColor()
        }
    }

    /**
     * taskbar颜色设置，编辑模式下，为默认taskbar颜色，非编辑模式下，同录制面板颜色一致
     */
    private fun taskBarColor(): Int {
        return if (ItemBrowseRecordViewModel.liveEditMode[taskId]?.value == true) {
            // 编辑模式下，同底部navigationView颜色一致
            com.support.appcompat.R.color.coui_color_bottom_bar
        } else {
            com.soundrecorder.base.R.color.common_background_color
        }
    }

    private fun updateViewStatus(viewStatus: ViewStatus?) {
        when (viewStatus) {
            ViewStatus.NO_PERMISSION -> showNoPermissionView()
            ViewStatus.QUERYING -> showQueryingView()
            ViewStatus.EMPTY -> {
                DebugUtil.d(TAG, "updateViewStatus, EMPTY")
                /*if data are all clear,exit edit mode in editMode*/
                mBrowseViewModel.exitEditMode()
                /*show empty view*/
                showEmptyView()
            }

            ViewStatus.SHOW_CONTENT -> showContentView()
            else -> {}
        }
    }

    private fun showContentView() {
        DebugUtil.i(logTag, "showContentView")
        mBinding.rvCallRecordList.visible()

        mPermissionScrollview?.visibility = View.GONE
        this.mEmptyScrollview?.visibility = View.GONE
        mLoadingViewLayout?.visibility = View.GONE
    }

    private fun ensureEmptyLayout() {
        DebugUtil.i(TAG, "ensureEmptyLayout--------")
        if ((this.mEmptyScrollview == null) && (!mBinding.mEmptyViewStub.isInflated)) {
            this.mEmptyScrollview = mBinding.mEmptyViewStub.viewStub?.inflate() as? ScrollView
            mEmptyOSImageView = this.mEmptyScrollview?.findViewById(R.id.mEmptyImage)
        }
    }

    private fun showEmptyView() {
        DebugUtil.i(logTag, "showEmptyView")
        ensureEmptyLayout()
        mEmptyScrollview?.visibility = View.VISIBLE
        mBottomMarginView?.setDisplay(false)

        mPermissionScrollview?.visibility = View.GONE
        mBinding.rvCallRecordList.visibility = View.GONE
        mLoadingViewLayout?.visibility = View.GONE

        mEmptyOSImageView?.initImageResource()
        //showEmptyView在layoutChange之后调用，所以需要再次更新图片大小
        setOtherViewBottomMargin()
    }

    private fun showQueryingView() {
        DebugUtil.i(logTag, "showQueryingView")
        if ((mAdapter?.isContentNotEmpty() == true) || (mEmptyScrollview?.isVisible == true)) {
            DebugUtil.i(TAG, "if list view content is not empty or empty visible, don't show loading view.")
            return
        }
        ensureLoadingView()
        mLoadingViewLayout?.visibility = View.VISIBLE
        mPermissionScrollview?.visibility = View.GONE
        this.mEmptyScrollview?.visibility = View.GONE
        mBinding.rvCallRecordList.visibility = View.GONE
    }

    private fun ensureLoadingView() {
        if ((mLoadingViewLayout == null) && (!mBinding.mLoadingStub.isInflated)) {
            mLoadingViewLayout = mBinding.mLoadingStub.viewStub?.inflate()
            mLoadingView = mLoadingViewLayout?.findViewById(com.soundrecorder.common.R.id.loadingView)
        }
    }

    private fun showNoPermissionView() {
        DebugUtil.i(logTag, "showNoPermissionView")
        ensurePermissionDeniedStub()
        mPermissionScrollview?.visibility = View.VISIBLE
        mBinding.rvCallRecordList.visibility = View.GONE
        mLoadingViewLayout?.visibility = View.GONE
        mEmptyScrollview?.visibility = View.GONE
        //setRefreshEnable(false)
        setOtherViewBottomMargin()
    }

    /**
     * 设置emptyView/无权限到底部的间距
     * 分屏模式且通话录音的模式下，设置bottomMargin = 10dp
     * 其他情况设置为底部面板的高度
     */
    private fun setOtherViewBottomMargin() {
        if (this.mEmptyScrollview?.isVisible == true) {
            DebugUtil.i(TAG, "setEmptyViewBottomMargin ")
            updateTipsViewTopMargin(this.mEmptyScrollview, mEmptyOSImageView)
        }
        if (this.mPermissionScrollview?.isVisible == true) {
            DebugUtil.i(TAG, "setOtherViewBottomMargin,mPermissionScrollview ")
            updateTipsViewTopMargin(this.mPermissionScrollview, null)
        }
    }

    private fun updateTipsViewTopMargin(rootView: ScrollView?, osImageView: OSImageView?) {
        DebugUtil.i(TAG, "setTipsViewBottomMargin ")
        if (rootView == null) {
            return
        }
        activity?.let {
            rootView.postDelayed({
                val topMargin = if ((FunctionOption.PHONE_NEED_HORIZONTAL) && (LandScapeUtil.spitWindowHeightLessThanForPlay450(it))) {
                    mBinding.toolbar.height
                } else {
                    mBinding.appBarLayout.height
                }
                DebugUtil.d(TAG, "updateTipsViewTopMargin, topMargin:$topMargin")
                val bottomMargin = it.resources.getDimension(R.dimen.recorder_height).toInt()

                (rootView.layoutParams as? ViewGroup.MarginLayoutParams)?.let { params ->
                    if (params.topMargin != topMargin || params.bottomMargin != bottomMargin) {
                        params.topMargin = topMargin
                        params.bottomMargin = bottomMargin
                        rootView.layoutParams = params
                    }
                }
                osImageView?.run {
                    val windowWith = it.px2dp(rootView.width).toInt()
                    val windowHeight = SplitWindowUtil.getCurrentSplitWindowParameter(it).windowHeight
                    this.setScaleByEmptySize(windowWith, windowHeight - it.px2dp(topMargin + bottomMargin).toInt(), "BrowseFragment")
                }

                rootView.fullScroll(ScrollView.FOCUS_DOWN)
            }, NUMBER_L100)
        }
    }

    private fun ensurePermissionDeniedStub() {
        if ((mPermissionScrollview == null) && (!mBinding.mPermissionDeniedStub.isInflated)) {
            mPermissionScrollview = mBinding.mPermissionDeniedStub.viewStub?.inflate() as ScrollView
            mBtnPermissionOperate = mPermissionScrollview?.findViewById(R.id.btn_permission_operate)
            (mPermissionScrollview?.findViewById(R.id.tips_permission_denied) as? TextView)?.setText(
                if (BaseUtil.isAndroidTOrLater) {
                    com.soundrecorder.common.R.string.permission_open_read_audio_dialog_title_v2
                } else {
                    com.soundrecorder.common.R.string.storage_permission_denied
                }
            )
            (mPermissionScrollview?.findViewById(R.id.tips_hint_permission_denied) as? TextView)?.setText(
                if (BaseUtil.isAndroidTOrLater) {
                    com.soundrecorder.common.R.string.permission_open_read_audio_empty_desc_v2
                } else {
                    com.soundrecorder.common.R.string.storage_permission_describe_v2
                }
            )
        }

        activity?.let { act: FragmentActivity ->
            mBtnPermissionOperate?.run {
                if (PermissionUtils.hasReadAudioPermissionRationale(act)) {
                    text = BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.permission_open_dialog)
                    setOnClickListener {
                        DebugUtil.e(
                            TAG,
                            "onPermissionsDeniedWithTick BROWSE_FILE_SCENES, click PERMISSION_STUB_OPEN"
                        )
                        PermissionUtils.requestReadAudioPermissionForBrowseFile(act)
                    }
                } else {
                    text = BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.app_name_settings)
                    setOnClickListener {
                        DebugUtil.e(
                            TAG,
                            "onPermissionsDeniedWithTick BROWSE_FILE_SCENES, click PERMISSION_STUB_SETTING"
                        )
                        PermissionUtils.goToAppSettingConfigurePermissions(act, arrayListOf(PermissionUtils.READ_AUDIO_PERMISSION()))
                    }
                }
            }
        }
    }

    private fun onDataChanged(data: List<ItemBrowseRecordViewModel>) {
        if (DebugUtil.isLogOpen()) {
            DebugUtil.i(TAG, "onDataChanged ${data.size}")
        }
        mAdapter?.setData(data.toMutableList())
        isLoadFinished = true
        val headerEmpty = mAdapter?.isHeaderEmpty() ?: false
        mCallGroupMoreHeaderView = mAdapter?.getHeadeView(TYPE_HEADER_CALL_GROUP_MORE) as? CallGroupMoreHeaderView
        if (headerEmpty && mCallGroupMoreHeaderView == null) {
            mCallGroupMoreHeaderView = activity?.let { CallGroupMoreHeaderView(it) }
            mAdapter?.setHeader(TYPE_HEADER_CALL_GROUP_MORE, mCallGroupMoreHeaderView)
        }
        mCallGroupMoreHeaderView?.let {
            val showCount = mBrowseViewModel.getShowRecordCount()
            it.setCallerName(callerName, circleColor)
            it.setCount(
                resources.getQuantityString(
                    com.soundrecorder.common.R.plurals.count_record,
                    showCount,
                    showCount
                )
            )
        }
        updateSelectMap()
    }

    private fun configToolbar() {
        mBinding.toolbar.isTitleCenterStyle = false
        mBinding.toolbar.setTitleTextColor(Color.argb(0, 0, 0, 0))
        mBinding.toolbar.inflateMenu(R.menu.call_more_list_option_browse)
        mOptionsMenu = mBinding.toolbar.menu
        mBinding.toolbar.setTitle("")

        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.RESUMED) {
                mOptionsMenu?.apply {
                    //onCreateOptionsMenuSpeaker(this)
                    updateToolBarMenu()
                    this.children.forEach {
                        it.setOnMenuItemClickListener { menuItem ->
                            onOptionsItemSelected(menuItem)
                        }
                    }
                }
            }
        }
    }

    private fun initiateWindowInsets() {
        activity?.let {
            WindowCompat.setDecorFitsSystemWindows(it.window, false)

            val callback = object : DeDuplicateInsetsCallback() {
                override fun onApplyInsets(v: View, insets: WindowInsetsCompat) {
                    //使用statusBars的高度    systemBars的高度不对
                    val stableStatusBarInsets =
                        insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.systemBars())
                    //底部面板bottom在onLayoutChange中动态设置高度nagivationHeight
                    nagivationHeight = stableStatusBarInsets.bottom
                    // view 顶部statusBar高度
                    mBinding.coordinator.updatePadding(
                        top = stableStatusBarInsets.top,
                        left = stableStatusBarInsets.left, right = stableStatusBarInsets.right
                    )
                    // view 底部navigationBar高度
                    mBinding.rvCallRecordList.updatePadding(bottom = nagivationHeight)

                    mAdapter?.setFooter(calBlankFooterHeight())
                    mNavigationLayout?.updatePadding(bottom = nagivationHeight)
                    setNavigationColor()
                }
            }
            ViewCompat.setOnApplyWindowInsetsListener(mBinding.root, callback)
        }
    }

    private fun setListViewAdapter() {
        context?.let {
            mAdapter = BrowseAdapter(viewLifecycleOwner, it, object : IBrowseViewHolderListener {
                override fun showSummaryTip(anchorView: View?) {
                    DebugUtil.i(TAG, "showSummaryTip")
                    TipUtil.checkShow({ anchorView }, TipUtil.TYPE_SUMMARY_TIPS, null, lifecycle, false, {
                        ItemBrowseRecordViewModel.summaryTipMediaId[taskId] = null
                    })
                }

                override fun getPlayerController(): FastPlayHelper {
                    return mBrowseViewModel.fastPlayHelper
                }

                override fun onClickItem(data: ItemBrowseRecordViewModel) {
                    onRecordItemClick(data, isCurrentPlay(data))
                }

                override fun onLongClickItem(view: View, data: ItemBrowseRecordViewModel) {
                    onItemLongClick(view, data)
                }

                override fun isCurrentPlay(data: ItemBrowseRecordViewModel): Boolean {
                    return getPlayerController().itemBrowseRecordViewModel?.mediaId == data.mediaId
                }

                override fun getWindowType(): MutableLiveData<WindowType> {
                    return mBrowseFileActivityViewModel.windowType
                }

                override fun getPlayLiveData(): MutableLiveData<StartPlayModel?> {
                    return mBrowseFileActivityViewModel.mCurrentPlayRecordData
                }

                override fun canShowAddAnimator(data: ItemBrowseRecordViewModel): Boolean {
                    //父子级录制新的音频回来右侧加载对应的详情，规则同原来展开逻辑一致（新录制且pos=0）
                    if (getWindowType().value != WindowType.SMALL) {
                        mBrowseFileActivityViewModel.mCurrentPlayRecordData.value =
                            data.toStartPlayModel(mBrowseFileActivityViewModel.isFromOtherApp, isRecycle = false)
                        ItemBrowseRecordViewModel.addAnimatorDisplayName = null
                        return false
                    }
                    return true
                }

                override fun onClickSummaryIcon(data: ItemBrowseRecordViewModel) {
                    clickSummaryIcon(data.recordUUID, data.noteId)
                }
            })
            mAdapter?.setPlaceHolder(mBinding.appBarLayout.getUnDisplayViewHeight())
            mAdapter?.setPlaceHolder(mBinding.toolbar.height)
            mAdapter?.stateRestorationPolicy = RecyclerView.Adapter.StateRestorationPolicy.PREVENT
            mBinding.rvCallRecordList.apply {
                isForceDarkAllowed = false
                adapter = mAdapter
                layoutManager = LinearLayoutManager(activity, LinearLayoutManager.VERTICAL, false)
                isVerticalFadingEdgeEnabled = false
                addOnItemTouchListener(object : RecyclerView.SimpleOnItemTouchListener() {
                    override fun onInterceptTouchEvent(rv: RecyclerView, e: MotionEvent): Boolean {
                        when (e.action) {
                            MotionEvent.ACTION_CANCEL, MotionEvent.ACTION_OUTSIDE, MotionEvent.ACTION_UP -> {
                                dragJob?.let { job ->
                                    if (!job.isCompleted) {
                                        DebugUtil.i(logTag, "item touch cancel drag. action=${e.action}")
                                        job.cancel()
                                    }
                                }
                            }
                        }
                        return false
                    }
                })
            }
        }
    }


    /**
     * 点击摘要图标跳转到便签
     */
    private fun clickSummaryIcon(summaryCallId: String?, summaryNoteId: String?) {
        val act = activity ?: kotlin.run {
            DebugUtil.e(TAG, "clickSummaryIcon activity is null!")
            return
        }
        val callId = summaryCallId ?: return
        val noteId = summaryNoteId ?: return
        clickedItemSummaryNoteId = noteId
        summaryApi?.toNotesSummaryActivity(
            act, callId, noteId, BrowseFragment.REQUEST_CODE_START_NOTE
        ) { clearSummary, disableDialog ->
            if (clearSummary) {
                mBrowseViewModel.clearAllSummary()
            } else {
                this.disableDialog = disableDialog
            }
        }
        SummaryStaticUtil.addClickViewSummaryEvent(SummaryStaticUtil.EVENT_FROM_MAIN)
    }

    private fun onItemLongClick(view: View, data: ItemBrowseRecordViewModel) {
        mBrowseViewModel.releasePlayer()
        val isEditMode = ItemBrowseRecordViewModel.liveEditMode[taskId]?.value
        if (isEditMode == true) {
            val delayTime = if (data.checkSelectItemOnLongClick()) {
                RecordItemDragDelegate.DURATION_CHECKBOX_ANIM
            } else {
                RecordItemDragDelegate.DURATION_DRAG_DELAY_DEFAULT
            }
            tryItemStartDrag(view, false, delayTime)
            return
        }
        data.onLongClick()
        // 等动效执行完成，view绘制好了，再触发拖拽，这样拖拽view的UI才显示正常
        tryItemStartDrag(view, true, RecordItemDragDelegate.DURATION_ENTER_EDIT_MODE_ANIM)
    }

    private fun tryItemStartDrag(view: View, vibrate: Boolean, delayTime: Long = 0) {
        DebugUtil.d(logTag, "tryItemStartDrag delayTime=$delayTime")
        dragViewReference = WeakReference(view)
        dragJob?.cancel()
        dragJob = lifecycleScope.launch {
            delay(delayTime)
            if (isActive) {
                withContext(Dispatchers.Main) {
                    itemDragDelegate?.tryStartDrag(dragViewReference?.get(), vibrate) {
                        if (it) {
                            refreshData()
                        }
                    }
                }
            }
        }
    }

    private fun onRecordItemClick(data: ItemBrowseRecordViewModel, isCurrentPlay: Boolean) {
        DebugUtil.d(TAG, "onClickItem, data:${data.mediaId}")
        if (ItemBrowseRecordViewModel.liveEditMode[taskId]?.value == true) {
            DebugUtil.d(TAG, "onClickItem, onClickCheckBox")
            data.onClickCheckBox()
            BuryingPoint.addClickBrowseRecordFile(RecorderUserAction.VALUE_SELECT_RECORD_FILE)
            return
        }
        if (data.isRecycle && !PermissionUtils.hasAllFilePermission()) {
            DebugUtil.d(TAG, "onClickItem, data:${data.mediaId}, no permission!")
            //回收站如果没有文管权限，点击需要授权
            activity?.let { it1 ->
                PermissionDialogUtils.showPermissionAllFileAccessDialog(
                    it1,
                    object : PermissionDialogUtils.PermissionDialogListener {
                        override fun onClick(
                            alertType: Int,
                            isOk: Boolean,
                            permissions: ArrayList<String>?
                        ) {
                            if (isOk) {
                                PermissionUtils.goToAppAllFileAccessConfigurePermissions(it1)
                            }
                        }
                    })
            }
            return
        }

        val currentPlayId = mBrowseFileActivityViewModel.mCurrentPlayRecordData.value?.mediaId
        val playerState = mBrowseViewModel.fastPlayHelper.getPlayerState()
        if (data.mediaId != currentPlayId && data.fileIsExists()) {
            val currentIsPlay = isCurrentPlay && data.isNeedShowSeekBarArea(playerState)
            mBrowseFileActivityViewModel.mCurrentPlayRecordData.value =
                data.toStartPlayModel(
                    mBrowseFileActivityViewModel.isFromOtherApp,
                    isRecycle = false
                ).apply {
                    DebugUtil.d(TAG, "onClickItem, mediaId:$mediaId, currentIsPlay:$currentIsPlay")
                    if (currentIsPlay) {
                        seekToMill = mBrowseViewModel.fastPlayHelper.getCurrentPlayerTime()
                    }
                    autoPlay = if (mBrowseFileActivityViewModel.isSmallWindow()) {
                        true // 小屏同原有逻辑，自动开始播放
                    } else if (currentIsPlay) {
                        mBrowseViewModel.fastPlayHelper.mediaPlayerManager.isPlaying()
                    } else {
                        false
                    }
                }
            mBrowseViewModel.releasePlayer()
            BuryingPoint.addPlayFromAndDuration(
                RecorderUserAction.VALUE_PLAY_FROM_PLAYBACK,
                data.recordType().toString(),
                data.mDuration
            )
            BuryingPoint.addClickBrowseRecordFile(RecorderUserAction.VALUE_NORMAL_RECORD_FILE)
        }
    }

    override fun onBackPressed(): Boolean {
        DebugUtil.i(
            TAG,
            "onBackPressed, liveEditMode.value > ${ItemBrowseRecordViewModel.liveEditMode[taskId]?.value}"
        )
        if (ItemBrowseRecordViewModel.liveEditMode[taskId]?.value == true) {
            mBrowseViewModel.exitEditMode()
            mBrowseViewModel.isRecycleDialogAllShowing = false

            //退出编辑模式，状态重置，否则可能导致新增记录列表无法刷新
            mNavigationViewManager?.resetContinueOperator()
            return true
        }
        if (mBrowseFileActivityViewModel.isSmallWindow()
            && mBrowseFileActivityViewModel.isCallGroupMoreShowing()
        ) {
            mBrowseFileActivityViewModel.clearCallGroupMoreData()
            return true
        }
        return false
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        DebugUtil.i(logTag, "requestCode = $requestCode resultCode = $resultCode")
        when (requestCode) {
            DeleteFileDialogUtil.REQUEST_LOCK_SCREEN_RESULT_SUCCESS -> responseDelete(resultCode, false)
            DeleteFileDialogUtil.REQUEST_LOCK_SCREEN_RECYCLE_DELETE_ALL_NONE_EDIT -> responseDelete(resultCode, true)
            RecoverFileDialogUtil.REQUEST_LOCK_SCREEN_RESULT_SUCCESS -> responseRecover(resultCode)
            FileDealUtil.REQUEST_CODE_SHARE -> responseShare(resultCode)
            BrowseFragment.REQUEST_CODE_SYS_RENAME_AUTH -> responseRename(resultCode)
            BrowseFragment.REQUEST_CODE_SYS_DELETE_AUTH -> responseDeleteBatch(resultCode)
            BrowseFragment.REQUEST_CODE_SYS_DELETE_ALL_AUTH -> responseDeleteBatch(resultCode, true)
            BrowseFragment.REQUEST_CODE_SYS_RECOVER_AUTH -> responseRecoverBatch(resultCode)
            BrowseFragment.REQUEST_CODE_START_NOTE -> responseStartNote(resultCode, data)
            else -> super.onActivityResult(requestCode, resultCode, data)
        }
        if (resultCode == PermissionUtils.RESULT_NOTIFICATION_PERMISSION_DENIED) {
            //首次使用APP，在录制界面拒绝了权限导致不能进行录制，需要回到首页，同时也拒绝了通知权限，则回到首页展示受阻弹窗。
            PermissionActivity.showRequestNotificationPermissionSnackBar(activity)
        }
    }

    private fun responseRecoverBatch(resultCode: Int) {
        DebugUtil.i(logTag, "<<< responseRecoverBatch resultCode > $resultCode")
        if (resultCode == Activity.RESULT_OK) {
            ensureNavigationViewManager()
            mNavigationViewManager?.recover()
        }
    }

    private fun responseDeleteBatch(resultCode: Int, isDeleteAll: Boolean = false) {
        DebugUtil.i(logTag, "<<< responseDeleteBatch resultCode > $resultCode")
        if (resultCode == Activity.RESULT_OK) {
            ensureNavigationViewManager()
            mNavigationViewManager?.delete(isDeleteAll)
        }
    }

    private fun responseRename(resultCode: Int) {
        if (resultCode == Activity.RESULT_OK) {
            val renameContent = mNavigationViewManager?.getRenameContent() ?: mBrowseViewModel.renameContent
            ensureNavigationViewManager()
            val record = mNavigationViewManager?.selectedRecordList?.get(0)
            val renameResult = FileDealUtil.renameAgain(record, renameContent)
            if (renameResult) {
                mBrowseViewModel.exitEditMode()
            }
            CloudStaticsUtil.addCloudLog(TAG, "responseRename,${record?.displayName} renameTo $renameContent,result=$renameResult")
        } else {
            mBrowseViewModel.exitEditMode()
        }
    }

    private fun responseRecover(resultCode: Int) {
        if (resultCode == Activity.RESULT_OK) {
            DebugUtil.i(logTag, "<<< responseRecover")
            isDeviceSecureDelete = true
            mNavigationViewManager?.recoverHasPermission()
        }
    }

    private fun responseShare(resultCode: Int) {
        if (resultCode == 0) {
            mBrowseViewModel.isCanShareRecordFiles = true
        }
    }

    /**
     * @param fromRecycleNoneEditDeleteAll 回收站非编辑模式下底部删除全部 R.id.item_delete_all
     */
    private fun responseDelete(resultCode: Int, fromRecycleNoneEditDeleteAll: Boolean) {
        if (resultCode == Activity.RESULT_OK) {
            DebugUtil.i(logTag, "<<< responseDelete fromRecycleNoneEditDeleteAll=$fromRecycleNoneEditDeleteAll")
            isDeviceSecureDelete = true
            mNavigationViewManager?.deleteHasPermission(fromRecycleNoneEditDeleteAll)
        }
    }

    private fun responseStartNote(resultCode: Int, data: Intent?) {
        if (resultCode == Constants.NOTES_RESULT_CODE_ERROR) {
            val uuid = data?.getStringExtra("speech_log_id")
            val noteId = data?.getStringExtra("note_id")
            DebugUtil.e(logTag, "<<< responseStartNote callId=$uuid, noteId=$noteId, clickedItemSummaryNoteId=$clickedItemSummaryNoteId")

            lifecycleScope.launch(Dispatchers.IO) {
                /*兼容老版本便签不支持noteId跳转的，所以也不会回传noteId*/
                val summaryNoteId = noteId ?: clickedItemSummaryNoteId
                if (!summaryNoteId.isNullOrEmpty()) {
                    mBrowseViewModel.clearSummaryByNoteId(summaryNoteId)
                    mBrowseFileActivityViewModel.beDeleteSummaryNoteId.postValueSafe(summaryNoteId)
                }
            }
            ToastManager.showShortToast(BaseApplication.getAppContext(), com.soundrecorder.common.R.string.tip_summary_be_deleted)
        } else {
            DebugUtil.i(logTag, "<<< responseStartNote resultCode > $resultCode")
        }
    }

    private fun BottomMarginView.setDisplay(isDisplay: Boolean) {
        if (isDisplay && mNavigationLayout?.visibility == View.VISIBLE) {
            DebugUtil.d(TAG, "BottomMarginView, setDisplay is visible")
            return
        }
        DebugUtil.d(TAG, "BottomMarginView.setDisplay:$isDisplay")
        mNavigationAnim?.end()
        val alphaAnimation = ObjectAnimator.ofFloat(
            mNavigationLayout, "alpha",
            if (isDisplay) 0f else 1f, if (isDisplay) 1f else 0f
        ).apply {
            duration = if (isDisplay) TOOL_NAV_SHOW_ANIM_DURATION else TOOL_NAV_DISMISS_ANIM_DURATION
            interpolator = if (isDisplay) toolNaShowAnimInterpolator else toolNavDismissAnimInterpolator
        }

        val marginBottomValue = -1 * resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.tool_navigation_translation)
        DebugUtil.d(TAG, "setDisplay, marginBottomValue:$marginBottomValue")
        mNavigationAnim = ObjectAnimator.ofInt(
            this,
            NAV_ANIM_PROPERTY_NAME,
            if (isDisplay) marginBottomValue else 0,
            if (isDisplay) 0 else marginBottomValue
        ).apply {
            duration = if (isDisplay) TOOL_NAV_SHOW_ANIM_DURATION else TOOL_NAV_DISMISS_ANIM_DURATION
            interpolator = if (isDisplay) toolNaShowAnimInterpolator else toolNavDismissAnimInterpolator
        }

        AnimatorSet().apply {
            playTogether(mNavigationAnim, alphaAnimation)
            addListener(
                onEnd = {
                    if (!isDisplay) mNavigationLayout?.visibility = View.GONE
                }, onStart = {
                    if (isDisplay) mNavigationLayout?.visibility = View.VISIBLE
                })
            start()
        }
    }

    override fun onDestroyView() {
        mBottomNavigationView?.menu?.clear()
        mBottomNavigationView?.setOnAnimatorListener(null)
        mBinding.toolbar.removeCallbacks(updateToolBarMenuRunnable)
        mBinding.rvCallRecordList.removeCallbacks(updateFooterRunnable)
        mBinding.rvCallRecordList.removeCallbacks(refreshRunnable)

        mEmptyOSImageView?.onRelease()
        mBinding.rvCallRecordList.animation?.let {
            it.setAnimationListener(null)
            it.cancel()
        }

        FollowDialogRestoreUtils.releaseFollowDialogRunnable(activity?.window?.decorView)
        disableDialog.dismissWhenShowing()
        mFilePermissionDialog.dismissWhenShowing()
        super.onDestroyView()
        itemDragDelegate = null
        dragViewReference = null
        disableDialog = null
    }

    override fun onDestroy() {
        super.onDestroy()
        release()
        unregisterReceiver()
        dismissShareWaitingDialog()
        mBrowseViewModel.clearLifecycle(this)
        mSmartNameMangerImpl?.release()
        mSmartNameMangerImpl = null
        clearNeedSmartNameMedias()
        (activity as? BaseActivity)?.unRegisterBackPressed()
    }

    private fun release() {
        if (activity?.isFinishing == true) {
            ItemBrowseRecordViewModel.run {
                liveEditMode[taskId]?.value = false
                liveDragMode[taskId]?.value = false
                liveSelectedMap[taskId]?.value?.clear()
                liveSelectedMap[taskId]?.value = null
                liveSelectedMap.remove(taskId)
                liveConvertStatus[taskId]?.value?.clear()
                liveConvertStatus.remove(taskId)
                liveEditMode.remove(taskId)
                liveDragMode.remove(taskId)
                liveAddFooter.remove(taskId)
            }
            if (::mBrowseViewModel.isInitialized) {
                mBrowseViewModel.isShowCallMorePage = false
                mBrowseViewModel.releasePlayer()
            }
        }
        mNavigationViewManager?.release()
        mAdapter?.release()
        isLoadFinished = false
    }

    override var logTag: String = TAG

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            callerName = it.getString(ARG_CALLER_NAME)
            circleColor = it.getString(ARG_CIRCLE_COLOR)
            groupInfo = it.getParcelable(ARG_GROUP_INFO, GroupInfo::class.java)
        }
        (activity as? BaseActivity)?.registerBackPressed()
    }

    companion object {
        fun show(containerId: Int, fm: FragmentManager, stackName: String?, groupInfo: GroupInfo?, callerName: String?, circleColor: String?) {
            fm.commit {
                setCustomAnimations(R.anim.detail_fragment_alpha_in, R.anim.detail_fragment_alpha_out)
                add(containerId, newInstance(groupInfo, callerName, circleColor), TAG)
            }
        }

        private const val TAG = "CallRecordListFragment"

        private const val MENU_PRESS_DELAY_TIME: Long = 150
        private const val NAV_ANIM_DURATION = 250L
        private const val NAV_ANIM_PROPERTY_NAME = "bottomMargin"
        private const val TOOL_NAV_DISMISS_ANIM_DURATION = 230L
        private const val TOOL_NAV_SHOW_ANIM_DURATION = 300L

        private const val DELAY_TIME_100 = 100L
        private const val DELAY_TIME_300 = 300L

        private val toolNavDismissAnimInterpolator = PathInterpolator(0.33f, 0f, 0.83f, 0.83f)
        private val toolNaShowAnimInterpolator = PathInterpolator(0.17f, 0.17f, 0.67f, 1f)

        /**
         * Use this factory method to create a new instance of
         * this fragment using the provided parameters.
         *
         * @param param1 Parameter 1.
         * @param param2 Parameter 2.
         * @return A new instance of fragment CallRecordListFragment.
         */
        @JvmStatic
        fun newInstance(groupInfo: GroupInfo?, callerName: String?, circleColor: String?): CallRecordListFragment =
            CallRecordListFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_CALLER_NAME, callerName)
                    putString(ARG_CIRCLE_COLOR, circleColor)
                    putParcelable(ARG_GROUP_INFO, groupInfo)
                }
            }
    }

    override fun onReceive(intent: Intent?) {
        DebugUtil.i(TAG, "onReceive action:" + intent?.action)
        when (intent?.action) {
            Intent.ACTION_MEDIA_EJECT,
            Intent.ACTION_MEDIA_UNMOUNTED,
            Intent.ACTION_MEDIA_REMOVED,
            Intent.ACTION_MEDIA_MOUNTED -> whenMediaChangeRelease()

            NOTIFY_CONVERT_STATUS_UPDATE -> notifyConvertStatusUpdate(intent)
            NOTIFY_SMART_NAME_STATUS_UPDATE -> notifySmartNameStatusUpdate(intent)
            RecordFileChangeNotify.FILE_UPDATE_ACTION,
            RecordFileChangeNotify.FILE_CUT_NEW_RECORD_ACTION -> {
                val isRefresh = intent.getBooleanExtra(Constants.FRESH_FLAG, false)
                if (isRefresh) {
                    //云同步时，如果处于回收站，不刷新数据
                    refreshData()
                }
            }

            ACTION_SUMMARY_STATE_CHANGED -> notifySummaryStateChanged(intent)
            RecordFileChangeNotify.CUBE_CLEAR_PLAY_RECORD_DATA -> {
                if (mBrowseFileActivityViewModel.isSmallWindow() != true) {
                    mBrowseFileActivityViewModel.clearCallGroupMoreData()
                }
                mBrowseFileActivityViewModel.clearPlayRecordData()
            }

            RecorderDataConstant.SMART_NAME_FILE_PERMISSION_ACTION -> {
                val mediaId = intent.getLongExtra(RecorderDataConstant.RECORD_MEDIA_ID, -1L)
                val mediaIdList = mutableListOf<Long>().apply {
                    add(mediaId)
                }
                showPermissionAllFileDialog(true, mediaIdList)
            }
        }
    }

    private fun notifySmartNameStatusUpdate(intent: Intent?) {
        mBrowseViewModel.onConvertStatusChanged(intent)
        val mediaId = intent?.getLongExtra(KEY_NOTIFY_RECORD_ID, -1L) ?: 0
        val display = intent?.getBooleanExtra(KEY_NOTIFY_SMART_NAME_STATUS, false) ?: false
        val resultName = intent?.getStringExtra(KEY_NOTIFY_SMART_NAME_NAME_TEXT)
        val convertComplete = intent?.getBooleanExtra(KEY_NOTIFY_SMART_NAME_CONVERT_COMPLETE, false) ?: false
        ConvertingInfo.onSmartNameStatusChanged(mediaId, display)
        DebugUtil.d(TAG, "notifySmartNameStatusUpdate, display:$display, resultname:$resultName")
        if (!resultName.isNullOrBlank()) {
            ConvertingInfo.addSmartNameResult(mediaId, resultName)
        }
        kotlin.runCatching {
            mAdapter?.let {
                it.getData()?.forEachIndexed { index, itemBrowseRecord ->
                    if (mediaId == itemBrowseRecord.mediaId) {
                        itemBrowseRecord.converting = false
                        itemBrowseRecord.convertCompleted = convertComplete
                        itemBrowseRecord.smartNaming = display
                        itemBrowseRecord.showSmartTextAnim = !display
                        it.notifyItemChanged(index + 1 + it.getHeaderSize())
                        return@forEachIndexed
                    }
                }
            }
        }.onFailure {
            DebugUtil.e(TAG, "notifySmartNameStatusUpdate, error:$it")
        }
    }

    private fun notifySummaryStateChanged(intent: Intent?) {
        val from = intent?.getStringExtraSecure(BUNDLE_FROM_WHERE) ?: return
        val defaultMediaId = -1L
        val mediaId = intent.getLongExtra(BUNDLE_MEDIA_ID, defaultMediaId)
        val noteId = intent.getStringExtraSecure(BUNDLE_NOTE_ID)
        val callId = intent.getStringExtraSecure(BUNDLE_CALL_ID)
        DebugUtil.i(TAG, "notifySummaryStateChanged from=$from, mediaId=$mediaId,noteId=$noteId,callId=$callId")
        if (mediaId != defaultMediaId && !noteId.isNullOrEmpty() && !callId.isNullOrEmpty()) {
            runCatching {
                mAdapter?.getData()?.forEachIndexed { index, itemModel ->
                    if (itemModel.mediaId == mediaId) {
                        itemModel.noteId = noteId
                        itemModel.recordUUID = callId
                        if (SummaryStaticUtil.EVENT_FROM_RECORD == from && !TipUtil.hasShowTip(TipUtil.TYPE_SUMMARY_TIPS)) {
                            // 录制入口的生成摘要且未显示过摘要tip才显示新手引导
                            ItemBrowseRecordViewModel.summaryTipMediaId[taskId] = mediaId
                        }
                        mAdapter?.notifyItemChanged(index + 1 + (mAdapter?.getHeaderSize() ?: 0))
                        return@forEachIndexed
                    }
                }
            }.onFailure {
                DebugUtil.e(TAG, "notifySummaryStateChanged error$it")
            }
        }
    }

    private fun notifyConvertStatusUpdate(intent: Intent?) {
        mBrowseViewModel.onConvertStatusChanged(intent)
    }

    private fun whenMediaChangeRelease() {
        val path = StorageManager.getInstance(context).getExternalPath(context)
        DebugUtil.d(TAG, "whenMediaChangeRelease externalPath:$path")
        path?.let {
            if (mBrowseViewModel.fastPlayHelper.itemBrowseRecordViewModel?.data?.contains(it) == true) {
                mBrowseViewModel.releasePlayer()
            }
        }
        mNavigationViewManager?.dismissDeleteDialog()
        mNavigationViewManager?.dismissRecoverDialog()
        mNavigationViewManager?.dismissDeleteAllDialog()
    }

    private fun showGroupChooseFragment() {
        val fm = activity?.supportFragmentManager
        fm?.let {
            val selectGroupInfo = mBrowseViewModel.currentGroup.value
            mGroupViewModel.showChooseGroupFragment(it)
        }
    }

    /**
     * 显示 “请稍后。。。” dialog
     * 当转文本的文件大于  Constants.SHOW_WAITING_DIALOG__THRESHOLD 的时候，保存到本地和分享需要弹窗
     * 弹窗显示不足1s的时候，需要延迟到1s之后再关闭
     */
    private fun showShareWaitingDialog(@StringRes msgResId: Int = com.soundrecorder.common.R.string.waiting) {
        val activity = activity ?: return
        if (shareWaitingDialog == null) {
            shareWaitingDialog = LoadingDialog(activity)
        }
        //修复loading dialog只显示一次的问题
        if (shareWaitingDialog?.isActivityNull() == true) {
            shareWaitingDialog?.resetActivity(activity)
        }
        shareWaitingDialog?.show(msgResId)
    }

    /**
     * 关闭waitingDialog并且显示分享面板
     */
    private fun dismissShareWaitingDialog() {
        if (shareWaitingDialog?.isShowing() == true) {
            shareWaitingDialog?.dismiss()
        }
    }

    fun onPrivacyPolicySuccess(type: Int, pageFrom: Int?) {
        if (type == PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT) {
            initSmartNameManagerImpl()
            mSmartNameMangerImpl?.doClickPermissionConvertOK(activity, needSmartNameMediaList, pageFrom)
            clearNeedSmartNameMedias()
        }
    }
}