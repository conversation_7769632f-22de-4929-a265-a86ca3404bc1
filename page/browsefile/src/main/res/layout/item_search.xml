<?xml version="1.0" encoding="utf-8"?><!-- oppoListItem -->
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="itemRecord"
            type="com.soundrecorder.browsefile.search.load.ItemSearchViewModel" />
    </data>

    <com.soundrecorder.common.widget.ClickScaleCardView
        android:id="@+id/click_scale_card_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/item_round_rect_bg"
        android:descendantFocusability="blocksDescendants"
        android:paddingHorizontal="@dimen/dp16"
        android:forceDarkAllowed="false"
        android:elevation="0dp"
        app:exclude_view_tags="bg_play_area,item_play_info,summary_button,item_play_area,play_button_area,play_button,record_duration">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <include
                android:id="@+id/item_info"
                layout="@layout/item_record_info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp14"
                app:itemRecord="@{itemRecord}"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@id/item_summary_and_play"
                app:layout_constraintTop_toTopOf="parent" />
            <include
                android:id="@+id/include_text_info"
                layout="@layout/include_search_result_text_info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                app:layout_constrainedWidth="true"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:itemRecord="@{itemRecord}"
                app:layout_constraintEnd_toEndOf="@id/item_summary_and_play"
                app:layout_constraintStart_toStartOf="@id/item_info"
                app:layout_constraintTop_toBottomOf="@id/item_play_info"
                />


            <include
                android:id="@+id/item_summary_and_play"
                layout="@layout/item_summary_and_play"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:itemRecord="@{itemRecord}"
                app:layout_constraintBottom_toBottomOf="@id/item_info"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/item_info" />

            <com.soundrecorder.browsefile.home.view.ItemBrowsePlayInfoLayout
                android:id="@+id/item_play_info"
                android:layout_width="0dp"
                android:layout_height="@dimen/seekbar_layout_height"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/item_info"
                android:layout_marginTop="@dimen/dp12"
                android:tag="item_play_info"/>

            <Space
                android:id="@+id/play_space"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp14"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/include_text_info" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.soundrecorder.common.widget.ClickScaleCardView>

</layout>