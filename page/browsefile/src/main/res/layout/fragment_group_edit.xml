<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.coui.appcompat.grid.COUIPercentWidthLinearLayout xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingTop="12dp"
        android:paddingBottom="24dp"
        app:gridNumber="@integer/grid_guide_column_preference"
        app:paddingSize="small"
        app:paddingType="cardListType"
        app:percentMode="paddingMode"
        tools:background="@color/coui_color_background_elevatedWithCard">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp16"
            android:background="@drawable/bg_edit_group_input">

            <ImageView
                android:id="@+id/input_icon"
                android:layout_width="@dimen/dp24"
                android:layout_height="@dimen/dp24"
                android:layout_marginStart="@dimen/dp16"
                android:scaleType="fitXY"
                app:layout_constraintBottom_toBottomOf="@+id/inputLayout"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/inputLayout" />

            <LinearLayout
                android:id="@+id/inputLayout"
                android:layout_width="@dimen/dp0"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingEnd="@dimen/dp16"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/input_icon"
                app:layout_constraintTop_toTopOf="parent">

                <com.coui.appcompat.edittext.COUICardSingleInputView
                    android:id="@+id/group_edit_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_minus_16"
                    android:layout_marginEnd="@dimen/dp_minus_32"
                    android:gravity="start|top"
                    android:textAlignment="viewStart"
                    android:textDirection="locale"
                    app:couiHint="@string/recording_group_edit_tips" />

                <View
                    android:id="@+id/red_line"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp1"
                    android:layout_marginStart="@dimen/dp16"
                    android:layout_marginTop="@dimen/dp_minus_4"
                    android:background="@color/coui_color_error"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tv_group_rename_prompts"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp16"
                    android:layout_marginTop="@dimen/dp4"
                    android:layout_marginBottom="@dimen/dp10"
                    android:fontFamily="OPlusSans 4.0"
                    android:gravity="start|center_vertical"
                    android:textColor="@color/couiRedTintControlNormal"
                    android:textSize="@dimen/record_group_create_or_update_name_text_size"
                    android:visibility="gone"
                    tools:text="@string/error_none_filename" />
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>


        <com.coui.appcompat.cardlist.COUICardListSelectedItemLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="vertical"
            android:paddingStart="16dp"
            android:paddingTop="16dp"
            android:paddingEnd="16dp"
            android:paddingBottom="16dp">

            <TextView
                style="@style/couiTextDescription"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:text="@string/recording_group_edit_select_color"
                android:textColor="?attr/couiColorSecondNeutral" />

            <com.coui.appcompat.grid.COUIGridLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:paddingStart="12dp"
                android:paddingEnd="12dp"
                app:childHeight="32dp"
                app:childWidth="32dp"
                app:maxHorizontalGap="13.33dp"
                app:minHorizontalGap="10.5dp"
                app:specificType="specificSizeMode">

                <View
                    android:id="@+id/group_edit_cover_red"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/group_edit_cover"
                    android:backgroundTint="@color/group_cover_color_red"
                    android:contentDescription="@string/color_red" />

                <View
                    android:id="@+id/group_edit_cover_yellow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/group_edit_cover"
                    android:backgroundTint="@color/group_cover_color_yellow"
                    android:contentDescription="@string/color_yellow" />

                <View
                    android:id="@+id/group_edit_cover_orange"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/group_edit_cover"
                    android:backgroundTint="@color/group_cover_color_orange"
                    android:contentDescription="@string/color_orange" />

                <View
                    android:id="@+id/group_edit_cover_green"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/group_edit_cover"
                    android:backgroundTint="@color/group_cover_color_green"
                    android:contentDescription="@string/color_green" />

                <View
                    android:id="@+id/group_edit_cover_azure"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/group_edit_cover"
                    android:backgroundTint="@color/group_cover_color_azure"
                    android:contentDescription="@string/color_blue" />

                <View
                    android:id="@+id/group_edit_cover_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/group_edit_cover"
                    android:backgroundTint="@color/group_cover_color_grey"
                    android:contentDescription="@string/color_gray" />

                <View
                    android:id="@+id/group_edit_cover_brown"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/group_edit_cover"
                    android:backgroundTint="@color/group_cover_color_brown"
                    android:contentDescription="@string/color_brown" />
            </com.coui.appcompat.grid.COUIGridLayout>

        </com.coui.appcompat.cardlist.COUICardListSelectedItemLayout>

    </com.coui.appcompat.grid.COUIPercentWidthLinearLayout>
</ScrollView>
