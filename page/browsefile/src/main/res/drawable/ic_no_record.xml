<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="280dp"
    android:height="200dp"
    android:viewportWidth="280"
    android:viewportHeight="200">
  <path
      android:pathData="M116.32,32.42C116.32,31.22 115.35,30.24 114.15,30.24C112.94,30.24 111.97,31.22 111.97,32.42V78.42C111.97,79.62 112.94,80.6 114.15,80.6C115.35,80.6 116.32,79.62 116.32,78.42V32.42Z"
      android:fillColor="#D98B84"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M75.8,40.15C77,40.15 77.97,41.12 77.97,42.32V68.59C77.97,69.79 77,70.77 75.8,70.77C74.6,70.77 73.62,69.79 73.62,68.59V42.32C73.62,41.12 74.6,40.15 75.8,40.15Z"
      android:fillColor="#D98B84"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M68.14,43.22C69.34,43.22 70.32,44.2 70.32,45.4V65.51C70.32,66.71 69.34,67.69 68.14,67.69C66.94,67.69 65.97,66.71 65.97,65.51V45.4C65.97,44.2 66.94,43.22 68.14,43.22Z"
      android:fillColor="#BF5C56"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M85.63,48.85C85.63,47.65 84.65,46.68 83.45,46.68C82.25,46.68 81.28,47.65 81.28,48.85V61.99C81.28,63.19 82.25,64.16 83.45,64.16C84.65,64.16 85.63,63.19 85.63,61.99V48.85Z"
      android:fillColor="#BF5C56"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M91.11,50.05C92.31,50.05 93.28,51.03 93.28,52.23V58.76C93.28,59.96 92.31,60.94 91.11,60.94C89.91,60.94 88.93,59.96 88.93,58.76V52.15C89.01,50.95 89.91,50.05 91.11,50.05Z"
      android:fillColor="#D98B84"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M100.94,48.85C100.94,47.65 99.96,46.68 98.76,46.68C97.56,46.68 96.58,47.65 96.58,48.85V61.99C96.58,63.19 97.56,64.16 98.76,64.16C99.96,64.16 100.94,63.19 100.94,61.99V48.85Z"
      android:fillColor="#D98B84"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M106.49,43.45C107.69,43.45 108.67,44.43 108.67,45.63V65.36C108.67,66.56 107.69,67.54 106.49,67.54C105.29,67.54 104.31,66.56 104.31,65.36V45.63C104.31,44.43 105.29,43.45 106.49,43.45Z"
      android:fillColor="#BF5C56"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M121.8,40.15C123,40.15 123.98,41.12 123.98,42.32V68.59C123.98,69.79 123,70.77 121.8,70.77C120.6,70.77 119.62,69.79 119.62,68.59V42.32C119.7,41.12 120.6,40.15 121.8,40.15Z"
      android:fillColor="#D98B84"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M131.63,45.63C131.63,44.43 130.65,43.45 129.46,43.45C128.25,43.45 127.28,44.43 127.28,45.63V65.36C127.28,66.56 128.25,67.54 129.46,67.54C130.65,67.54 131.63,66.56 131.63,65.36V45.63Z"
      android:fillColor="#BF5C56"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M180.64,104.46C181.28,105.94 181.92,107.39 182.56,108.85"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M185.22,117.97C183.94,136.13 177.04,152.12 183.04,167.65H139.66C145.59,152.04 138.69,136.06 137.49,117.97C136.51,107.01 144.39,105.74 153.4,105.96L148.52,96.51C148.44,96.28 148.52,96.06 148.67,95.98L153.17,93.88C153.47,93.73 153.85,93.81 154.07,94.03L160.3,100.64L166.53,94.03C166.76,93.81 167.13,93.73 167.43,93.88L171.93,95.98C172.16,96.06 172.23,96.28 172.08,96.51L167.13,106.04C177.04,105.59 186.27,106.19 185.22,117.97Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M182.06,163.6L188.67,187.76H133.51L140.86,163.52C141.24,162.47 142.06,161.8 143.11,161.8H179.81C180.79,161.8 181.76,162.55 182.06,163.6Z"
      android:fillColor="#CC8A52"/>
  <path
      android:pathData="M160.3,100.71L164.5,105.59"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M169.61,80.22C168.86,80.15 168.1,79.77 167.58,79.25"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M170.21,84.05C169.76,80.45 166.15,82.17 164.28,83.97C163.83,85.25 166.15,86 167.88,85.93C169.83,85.85 170.13,84.73 170.21,84.05ZM168.7,83.07C168.1,82.77 164.8,84.2 164.8,84.2H169.46C169.46,84.2 169.3,83.45 168.7,83.07Z"
      android:fillColor="#808080"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M174.18,74.14C174.18,74.89 173.73,75.49 173.13,75.49C172.61,75.49 172.16,74.89 172.16,74.14C172.16,73.39 172.61,72.79 173.21,72.79C173.73,72.79 174.18,73.39 174.18,74.14Z"
      android:fillColor="#323739"/>
  <path
      android:pathData="M163.98,74.37C163.98,75.12 163.45,75.79 162.93,75.79C162.33,75.79 161.88,75.12 161.88,74.37C161.88,73.62 162.4,72.94 162.93,72.94C163.53,73.02 164.05,73.62 163.98,74.37Z"
      android:fillColor="#323739"/>
  <path
      android:pathData="M175.46,72.04C175.38,71.89 174.56,71.07 173.36,71.29C172.31,71.44 171.78,72.27 171.63,72.42"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M159.7,72.57C159.85,72.42 160.68,71.52 162.1,71.59C163.3,71.67 163.98,72.42 164.13,72.57"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M164.8,84.2C164.8,84.2 168.1,82.77 168.7,83.07C169.3,83.45 169.46,84.2 169.46,84.2H164.8Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M170.81,67.24C171.33,67.61 172.31,68.06 173.58,68.29C174.33,68.36 175.01,68.36 175.54,68.21"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M158.13,69.19C158.43,68.89 159.63,67.54 161.65,67.31C163.6,67.09 165.03,67.99 165.4,68.21"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M145.44,36.85C145.21,37.37 143.34,42.47 146.34,47.28C148.59,50.8 152.65,52.83 156.92,52.61"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#595050"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M146.19,44.2C147.17,51.03 153.17,54.48 159.77,54.63C166.45,54.93 172.38,51.48 173.36,44.12C174.33,36.85 168.26,31.37 159.77,31.82C151.37,31.89 145.22,37.37 146.19,44.2Z"
      android:fillColor="#595050"/>
  <path
      android:pathData="M104.84,111.85L104.73,111.68C104.68,111.72 104.64,111.78 104.64,111.85H104.84ZM107.09,118.87L107.08,119.07H107.09V118.87ZM116.25,118.87V119.07L116.26,119.07L116.25,118.87ZM116.55,104.09L116.59,103.89C116.57,103.89 116.56,103.89 116.55,103.89V104.09ZM108.6,104.09V103.89C108.53,103.89 108.48,103.92 108.44,103.97L108.6,104.09ZM106.64,106.64L106.48,106.52C106.42,106.6 106.43,106.71 106.51,106.79C106.58,106.86 106.7,106.86 106.78,106.79L106.64,106.64ZM107.32,106.04L107.45,106.19L107.46,106.18L107.32,106.04ZM107.39,105.96L107.53,106.11L107.53,106.11L107.39,105.96ZM107.92,105.74V105.94C107.93,105.94 107.94,105.94 107.95,105.94L107.92,105.74ZM111.6,105.21L111.62,105.41L111.6,105.21ZM112.72,106.04L112.52,106.07V106.07L112.72,106.04ZM111.97,107.16L112.02,107.36C112.02,107.36 112.03,107.36 112.03,107.35L111.97,107.16ZM108.74,107.92L108.7,107.72C108.66,107.73 108.63,107.75 108.6,107.78L108.74,107.92ZM105.07,111.67L105.21,111.81L105.21,111.81L105.07,111.67ZM104.84,116.54H105.04V111.85H104.84H104.64V116.54H104.84ZM107.09,118.87L107.1,118.67C105.93,118.6 105.04,117.71 105.04,116.54H104.84H104.64C104.64,117.93 105.71,118.99 107.08,119.07L107.09,118.87ZM116.25,118.87V118.67H107.09V118.87V119.07H116.25V118.87ZM118.5,116.54H118.3C118.3,117.71 117.41,118.6 116.24,118.67L116.25,118.87L116.26,119.07C117.63,118.99 118.7,117.93 118.7,116.54H118.5ZM118.5,106.41H118.3V116.54H118.5H118.7V106.41H118.5ZM116.55,104.09L116.51,104.28C117.54,104.49 118.3,105.39 118.3,106.41H118.5H118.7C118.7,105.19 117.8,104.13 116.59,103.89L116.55,104.09ZM116.17,104.09V104.29H116.55V104.09V103.89H116.17V104.09ZM108.6,104.09V104.29H116.17V104.09V103.89H108.6V104.09ZM108.6,104.09L108.44,103.97L106.48,106.52L106.64,106.64L106.8,106.76L108.75,104.21L108.6,104.09ZM106.64,106.64L106.78,106.79L107.45,106.19L107.32,106.04L107.18,105.89L106.51,106.49L106.64,106.64ZM107.32,106.04L107.46,106.18L107.53,106.11L107.39,105.96L107.25,105.82L107.18,105.9L107.32,106.04ZM107.39,105.96L107.53,106.11C107.6,106.04 107.65,106 107.71,105.98C107.76,105.95 107.83,105.94 107.92,105.94V105.74V105.54C107.78,105.54 107.66,105.56 107.55,105.61C107.43,105.66 107.34,105.74 107.25,105.82L107.39,105.96ZM107.92,105.74L107.95,105.94L111.62,105.41L111.6,105.21L111.57,105.01L107.89,105.54L107.92,105.74ZM111.6,105.21L111.62,105.41C112.04,105.35 112.46,105.66 112.52,106.07L112.72,106.04L112.92,106.01C112.83,105.37 112.2,104.93 111.57,105.01L111.6,105.21ZM112.72,106.04L112.52,106.07C112.58,106.47 112.29,106.85 111.91,106.97L111.97,107.16L112.03,107.35C112.55,107.18 113.01,106.65 112.92,106.01L112.72,106.04ZM111.97,107.16L111.93,106.97L108.7,107.72L108.74,107.92L108.79,108.11L112.02,107.36L111.97,107.16ZM108.74,107.92L108.6,107.78L104.92,111.53L105.07,111.67L105.21,111.81L108.89,108.06L108.74,107.92ZM105.07,111.67L104.93,111.53C104.87,111.58 104.8,111.64 104.73,111.68L104.84,111.85L104.95,112.02C105.04,111.96 105.13,111.89 105.21,111.81L105.07,111.67Z"
      android:fillColor="#808080"/>
  <path
      android:pathData="M110.62,115.27H112.65C114.82,115.27 116.55,113.54 116.55,111.37V95.16C116.55,92.98 114.75,91.25 112.65,91.25H110.62C108.85,91.25 107.38,92.44 106.89,94.02L108.52,92.68C108.97,92.31 109.64,92.38 109.94,92.83C110.32,93.21 110.24,93.81 109.87,94.18L106.72,97.54V99.34L114.82,97.26C115.42,97.11 116.02,97.48 116.17,98.08C116.32,98.68 116.02,99.21 115.42,99.43L114.67,99.73H114.82C115.42,99.58 115.95,99.96 116.02,100.49C116.1,101.01 115.72,101.46 115.27,101.61L109.34,103.11L106.72,106.54V106.57L107.32,106.04L107.39,105.96C107.54,105.81 107.69,105.74 107.92,105.74L111.6,105.21C112.12,105.14 112.65,105.51 112.72,106.04C112.8,106.56 112.42,107.01 111.97,107.16L108.74,107.92L106.72,109.98V111.37C106.72,113.54 108.52,115.27 110.62,115.27Z"
      android:fillColor="#BF5C56"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M177.37,77.62C177.29,78.22 177.22,78.67 177.22,78.9C176.02,86.33 170.96,91.85 165.78,91.33C161.88,90.95 158.2,88.7 155.42,85.55"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M153.51,83.15C153.54,83.67 153.57,84.19 153.59,84.71C152.13,83.94 150.77,82.96 149.54,81.81C149.57,81.81 149.61,81.8 149.64,81.8C149.76,81.76 149.89,81.72 150.02,81.69C150.15,81.65 150.28,81.61 150.39,81.57C151,81.32 151.54,80.9 151.98,80.34C151.99,80.37 152.01,80.4 152.02,80.43C151.6,80.95 151.1,81.34 150.54,81.57C150.69,81.62 150.88,81.61 151.06,81.59C151.14,81.58 151.22,81.57 151.29,81.57C151.68,81.51 152.05,81.39 152.39,81.2C152.73,81.87 153.1,82.52 153.51,83.15ZM157.34,60.72C158.02,60.27 158.75,59.87 159.49,59.51C159.44,59.53 159.38,59.56 159.32,59.58C158.62,59.91 157.96,60.29 157.34,60.72ZM176.79,63.72C175.39,55.98 168.56,50.99 160.6,51.4C159.77,51.48 158.87,51.48 158.05,51.48H158.05C149.27,51.78 142.51,58.31 142.89,67.39C142.97,69.23 143.3,71.03 143.84,72.75C144.57,71.23 145.82,70.05 147.09,69.57C147.24,68.89 147.39,68.21 147.62,67.61C147.84,66.94 148.14,66.26 148.44,65.74C148.52,65.6 148.6,65.46 148.68,65.31C148.9,64.9 149.14,64.47 149.42,64.09C150.09,63.04 150.92,61.99 151.82,61.09C153.62,59.21 155.8,57.78 158.2,56.88C160.6,55.98 163.15,55.61 165.63,55.76C166.9,55.83 168.1,56.06 169.3,56.36C169.9,56.51 170.51,56.66 171.03,56.88C171.18,56.96 171.33,57.01 171.48,57.07C171.63,57.13 171.78,57.18 171.93,57.26C172.23,57.41 172.53,57.56 172.83,57.78C173.96,58.53 174.78,59.51 175.46,60.64C176.08,61.59 176.44,62.68 176.79,63.72Z"
      android:fillColor="#595050"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M153.55,83.07C153.7,86.3 153.77,90.58 153.7,93.81"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M177.34,77.82C177.79,78.57 179.81,79.55 179.81,79.55C181.16,82.62 187.77,94.11 189.42,96.88C189.34,96.88 188.74,96.81 188.67,96.73C186.12,96.28 183.79,95.38 182.07,95.31C178.09,95.08 176.44,96.51 175.54,96.43L171.86,95.98"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M175.99,61.09C175.99,61.09 180.64,63.71 182.74,67.01C183.87,68.67 185.22,71.74 186.19,73.99C186.27,74.07 205.18,94.56 206.08,95.53C207.65,97.26 217.49,107.76 210.66,114.22C207.58,117.07 202.63,116.25 200,116.02C198.77,115.88 188.58,113.77 185.11,112.75"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M189.42,96.88C189.42,96.88 191.97,101.24 193.02,103.11C194.15,105.14 196.62,107.76 196.62,107.76"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M169.01,58.59C168.34,61.66 166.53,63.79 163.68,65.21C161.73,66.19 159.48,66.56 157.3,67.01C155.42,67.46 153.25,67.91 151.74,68.89C151.74,68.89 151.45,68.74 151.22,68.67C152.03,66.28 155.22,60.38 162.26,58.59C165.81,57.68 165.77,58.07 169.01,58.59Z"
      android:fillColor="#595050"/>
  <path
      android:pathData="M177.86,71.07C177.86,73.47 177.79,75.79 177.41,78.12H177.34C177.56,75.79 177.41,73.47 177.26,71.14C177.04,68.82 176.59,66.56 175.84,64.39C175.08,62.29 173.81,60.26 172.01,59.28C171.78,59.13 171.56,59.06 171.33,58.98C171.11,58.91 170.88,58.83 170.58,58.76C170.06,58.61 169.53,58.53 168.93,58.38C167.8,58.23 166.75,58.16 165.63,58.16C163.45,58.23 161.28,58.68 159.32,59.58C157.37,60.49 155.72,61.84 154.37,63.49C153.7,64.31 153.1,65.21 152.57,66.11C152.35,66.56 152.04,67.09 151.9,67.54C151.67,68.07 151.52,68.44 151.37,68.89C151.29,69.04 151.29,69.27 151.22,69.42C151.14,69.79 151.07,70.24 151.07,70.62C152.12,71.59 152.95,73.09 153.25,74.89C153.62,77.97 152.42,80.75 150.39,81.57C150.17,81.65 149.87,81.72 149.64,81.8C147.24,82.17 143.79,79.92 143.26,76.4C142.81,73.24 144.92,70.39 147.09,69.57C147.24,68.89 147.39,68.21 147.62,67.61C147.84,66.94 148.14,66.26 148.44,65.74C148.74,65.21 149.04,64.61 149.42,64.09C150.09,63.04 150.92,61.99 151.82,61.09C153.62,59.21 155.8,57.78 158.2,56.88C160.6,55.98 163.15,55.61 165.63,55.76C166.9,55.83 168.1,56.06 169.31,56.36C169.91,56.51 170.51,56.66 171.03,56.88C171.33,57.03 171.63,57.11 171.93,57.26C172.23,57.41 172.53,57.56 172.83,57.78C173.96,58.53 174.78,59.51 175.46,60.64C176.13,61.69 176.51,62.89 176.89,64.01C177.56,66.34 177.79,68.67 177.86,71.07Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M177.2,77.89C177.11,77.96 177.11,78.09 177.18,78.17C177.26,78.25 177.38,78.26 177.47,78.18L177.2,77.89ZM177.59,78.06C177.67,77.99 177.67,77.86 177.59,77.78C177.52,77.7 177.39,77.7 177.31,77.78L177.59,78.06ZM177.4,77.68C177.33,77.76 177.33,77.89 177.41,77.97C177.49,78.04 177.61,78.04 177.69,77.97L177.4,77.68ZM179.44,73.01L179.64,73L179.64,73L179.44,73.01ZM178.06,69.15C177.99,69.06 177.87,69.05 177.78,69.12C177.7,69.19 177.69,69.32 177.76,69.4L178.06,69.15ZM177.33,78.03L177.47,78.18C177.51,78.14 177.55,78.1 177.59,78.06L177.45,77.92L177.31,77.78C177.27,77.81 177.23,77.85 177.2,77.89L177.33,78.03ZM177.55,77.82L177.69,77.97C178.86,76.77 179.72,74.99 179.64,73L179.44,73.01L179.24,73.02C179.32,74.88 178.51,76.56 177.4,77.68L177.55,77.82ZM179.44,73.01L179.64,73C179.54,71.49 178.92,70.16 178.06,69.15L177.91,69.27L177.76,69.4C178.57,70.36 179.15,71.61 179.24,73.02L179.44,73.01Z"
      android:fillColor="#808080"/>
  <path
      android:pathData="M151.29,81.57C151.07,81.57 150.77,81.65 150.54,81.57C152.49,80.75 153.77,77.97 153.4,74.89C153.1,73.17 152.27,71.59 151.22,70.62C151.22,70.17 151.29,69.79 151.37,69.42C153.1,70.17 154.52,72.12 154.9,74.67C155.35,78.12 153.77,81.2 151.29,81.57Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M148.89,75.64C149.04,76.92 148.29,78.05 147.24,78.12C146.19,78.2 145.29,77.22 145.14,75.94C144.99,74.67 145.74,73.54 146.79,73.47C147.84,73.39 148.82,74.37 148.89,75.64Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M158.2,88.33C158.2,88.33 160.98,92.08 163.38,92.83C165.85,93.58 166.68,93.21 166.68,93.21V91.4C166.68,91.4 165.78,91.7 163.3,90.8C160.83,89.9 158.2,88.33 158.2,88.33Z"
      android:fillColor="#808080"/>
  <path
      android:pathData="M166.76,91.33L166.83,93.81"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M123.9,129.9C123.9,129.9 126.23,133.58 130.36,139.21C133.13,142.96 133.88,148.51 133.88,148.51"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114.9,99.66L107.47,101.31L103.72,105.66"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M106.61,106.5L105.5,107.79"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M139.21,130.73C138.84,133.66 136.96,144.54 136.81,145.66C135.46,155.2 131.56,159.47 127.36,159.47C123.3,159.47 119.93,156.7 119.03,152.94C117.07,147.91 113.02,137.11 112.87,136.81C111.97,134.63 111,132.38 110.09,130.35C109.19,128.33 108.29,126.45 107.54,124.8C106.19,121.87 105.22,119.85 105.07,119.47C104.77,119.02 104.47,118.57 104.32,118.05L100.04,105.51C100.04,105.51 99.29,104.01 99.51,103.11C99.59,103.04 102.82,97.48 102.82,97.48C102.89,97.41 102.96,97.33 103.04,97.26L108.52,92.76C108.97,92.38 109.64,92.46 109.94,92.91C110.32,93.28 110.25,93.88 109.87,94.26L105.29,99.13L104.32,101.46L105.82,99.81C105.97,99.66 106.19,99.51 106.42,99.43L114.9,97.26C115.5,97.11 116.1,97.48 116.25,98.08C116.4,98.68 116.1,99.21 115.5,99.43L114.75,99.74H114.9C115.42,99.66 115.95,100.04 116.02,100.64C116.1,101.16 115.72,101.61 115.27,101.76L109.18,103.29L106.42,106.76L107.39,106.11C107.54,105.96 107.69,105.89 107.92,105.89L111.6,105.36C112.12,105.29 112.65,105.66 112.72,106.19C112.8,106.71 112.42,107.17 111.97,107.32L108.74,108.07L104.92,112.04"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M116.85,118.87C117.82,120.37 119.32,122.85 120.53,124.72C121.73,126.6 123,128.55 124.35,130.5C124.5,129.45 124.65,128.48 124.8,127.43C125.25,124.5 129.38,107.46 129.83,106.19C131.48,101.46 135.38,98.08 141.91,97.26C143.19,97.11 148.29,96.21 148.29,96.21"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M116.65,101.26C117.01,101.16 117.07,101.39 117.15,101.46C117.22,101.61 117.22,102.81 117.22,104.31"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M104.32,101.46L102.51,103.64"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M111.22,129.68C101.69,153.69 115.57,148.82 119.32,159.62C121.5,165.7 117.07,169.9 114.82,172.01C112.5,174.11 110.69,175.98 108.97,178.61C107.32,181.24 106.42,184.61 107.62,187.46"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114.45,135.53H108.97C107.77,135.53 106.87,134.56 106.87,133.43V132.98H116.62V133.43C116.55,134.56 115.57,135.53 114.45,135.53Z"
      android:fillColor="#595050"/>
  <path
      android:pathData="M116.25,118.87V120.97C116.25,121.87 115.5,122.7 114.6,122.7H114.52V126.9C114.52,128.25 114,129.3 113.17,129.83V187.84H110.1V129.83C109.27,129.3 108.74,128.33 108.82,126.9C108.82,125.48 108.82,124.13 108.82,122.7H108.74C107.84,122.7 107.09,121.95 107.09,120.97V118.87H116.25Z"
      android:fillColor="#595050"/>
</vector>
