<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="ConvertLoadingView">
        <item name="minWidth">48dip</item>
        <item name="maxWidth">48dip</item>
        <item name="minHeight">48dip</item>
        <item name="maxHeight">48dip</item>
    </style>

    <style name="ConvertLoadingView.Large">
        <item name="minWidth">76dip</item>
        <item name="maxWidth">76dip</item>
        <item name="minHeight">76dip</item>
        <item name="maxHeight">76dip</item>
    </style>

    <style name="ConvertLoadingView.Small">
        <item name="minWidth">24dip</item>
        <item name="maxWidth">24dip</item>
        <item name="minHeight">24dip</item>
        <item name="maxHeight">24dip</item>
    </style>

    <style name="Browse_Title_OS14" parent="TitleStyle"></style>

    <style name="Browse_Title_Semibold" parent="textAppearanceSecondTitle">
        <item name="android:fontFamily">sans-serif-semibold</item>
    </style>

    <style name="Browse_Title_OS13_2" parent="TitleStyle">
        <item name="android:textSize">@dimen/dp40</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>
</resources>