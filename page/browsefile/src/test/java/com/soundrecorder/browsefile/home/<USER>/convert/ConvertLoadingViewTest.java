package com.soundrecorder.browsefile.home.view.convert;

import android.content.Context;
import android.os.Build;
import android.view.View;
import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.browsefile.shadows.ShadowFeatureOption;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class ConvertLoadingViewTest {
    private ConvertLoadingView convertLoadingView;
    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
        convertLoadingView = new ConvertLoadingView(mContext);
    }

    @After
    public void tearDown() {
        mContext = null;
        convertLoadingView = null;
    }

    @Test
    public void should_visiable_when_smoothToShow() {
        convertLoadingView.smoothToShow();
        Assert.assertEquals(convertLoadingView.getVisibility(), View.VISIBLE);
    }

    @Test
    public void should_gone_when_smoothToGone() {
        convertLoadingView.smoothToGone();
        Assert.assertEquals(convertLoadingView.getVisibility(), View.GONE);
    }

    @Test
    public void should_gone_when_gone() {
        convertLoadingView.smoothToShow();
        convertLoadingView.gone();
        Assert.assertEquals(convertLoadingView.getVisibility(), View.GONE);
    }

    @Test
    public void should_false_when_show() {
        convertLoadingView.gone();
        convertLoadingView.show();
        boolean mDismissed = Whitebox.getInternalState(convertLoadingView, "mDismissed");
        Assert.assertFalse(mDismissed);
    }
}
