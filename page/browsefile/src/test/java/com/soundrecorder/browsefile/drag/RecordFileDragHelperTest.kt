/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RecordFileDragHelperTest
 * Description:
 * Version: 1.0
 * Date: 2024/2/21
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/2/21 1.0 create
 */

package com.soundrecorder.browsefile.drag

import android.app.Activity
import android.content.Context
import android.os.Build
import android.view.View
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.common.databean.Record
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S], shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class RecordFileDragHelperTest {
    private var context: Context? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
    }

    @After
    fun tearDown() {
        context = null
    }

    @Test
    fun should_correct_when_startDragAndDrop() {
        val context = context ?: return
        val view = View(context)
        RecordFileDragHelper.startDragAndDrop(context, view, mutableListOf()) {
            // do nothing
        }
        val activity = Mockito.mock(Activity::class.java)
        val record = Record()
        RecordFileDragHelper.startDragAndDrop(activity, view, mutableListOf(record)) {
            // do nothing
        }
    }

    @Test
    fun should_correct_when_checkCanDragAndDrop() {
        val context = context ?: return
        Assert.assertFalse(RecordFileDragHelper.checkCanDragAndDrop(context, null))
        val recordList = mutableListOf<Record>()
        val record = Record()
        recordList.add(record)
        Assert.assertTrue(RecordFileDragHelper.checkCanDragAndDrop(context, recordList))
        for (i in 0..99) {
            recordList.add(record)
        }
        Assert.assertFalse(RecordFileDragHelper.checkCanDragAndDrop(context, recordList))
    }

    @Test
    fun should_correct_when_createClipData() {
        val recordList = mutableListOf<Record>()
        Assert.assertNull(RecordFileDragHelper.createClipData(recordList))
        val record = Record()
        recordList.add(record)
        Assert.assertNotNull(RecordFileDragHelper.createClipData(recordList))
    }

    @Test
    fun should_notNull_when_createDragShadow() {
        val view = View(context)
        Assert.assertNotNull(RecordFileDragHelper.createDragShadow(view, 1, true))
    }
}