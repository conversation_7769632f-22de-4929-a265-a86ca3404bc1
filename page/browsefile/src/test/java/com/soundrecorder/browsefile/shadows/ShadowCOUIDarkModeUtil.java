/************************************************************
 * Copyright 2010-2019 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * FileName       : ShadowColorDarkModeUtil.java
 * Version Number : 1.0
 * Description    : Replace some methods of the COUIDarkModeUtil class for testing
 * Author         : LI Kun
 * Date           : 2019-07-15
 * History        :( ID,     Date,         Author, Description)
 * v1.0, 2019-09-15, LI Kun, create
 ************************************************************/
package com.soundrecorder.browsefile.shadows;

import android.content.Context;
import android.view.View;

import com.coui.appcompat.darkmode.COUIDarkModeUtil;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(COUIDarkModeUtil.class)
public class ShadowCOUIDarkModeUtil {

    @Implementation
    public static void setForceDarkAllow(View view, boolean allow) {
    }

    @Implementation
    public static boolean isNightMode(Context mContext) {
        return false;
    }


}
