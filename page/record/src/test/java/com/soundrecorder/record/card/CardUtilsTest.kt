/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: CardUtilsTest
 * Description:
 * Version: 1.0
 * Date: 2023/11/3
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/11/3 1.0 create
 */

package com.soundrecorder.record.card

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.utils.AppUtil
import com.soundrecorder.record.shadows.ShadowClearDataUtils
import com.soundrecorder.record.shadows.ShadowFeatureOption
import com.soundrecorder.record.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.record.shadows.ShadowOplusUsbEnvironment
import oplus.multimedia.soundrecorder.card.CardUtils
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.anyString
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOplusUsbEnvironment::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class, ShadowClearDataUtils::class]
)
class CardUtilsTest {
    private var mockStaticAppUtil: MockedStatic<AppUtil>? = null

    @Before
    fun setup() {
        mockStaticAppUtil = Mockito.mockStatic(AppUtil::class.java)
    }

    @After
    fun release() {
        mockStaticAppUtil?.close()
        mockStaticAppUtil = null
    }

    @Test
    fun should_correct_when_getSmallCardVersionCode() {
        mockStaticAppUtil?.`when`<Int> { AppUtil.metaDataInt(anyString(), anyString()) }
            ?.thenReturn(1, -1)

        Assert.assertEquals(1, CardUtils.getSmallCardVersionCode())
        Assert.assertEquals(-1, CardUtils.getSmallCardVersionCode())
    }

    @Test
    fun should_correct_when_isSmallVersionCode3OrLater() {
        mockStaticAppUtil?.`when`<Int> { AppUtil.metaDataInt(anyString(), anyString()) }
            ?.thenReturn(1, 3)

        Assert.assertFalse(CardUtils.isSmallVersionCode3OrLater())
        Assert.assertTrue(CardUtils.isSmallVersionCode3OrLater())
    }

    @Test
    fun should_correct_when_isSmallVersionCode2OrLater() {
        mockStaticAppUtil?.`when`<Int> { AppUtil.metaDataInt(anyString(), anyString()) }
            ?.thenReturn(1, 2)

        Assert.assertFalse(CardUtils.isSmallVersionCode2OrLater())
        Assert.assertTrue(CardUtils.isSmallVersionCode2OrLater())
    }

    @Test
    fun should_correct_when_isDragonFlyVersionCode2OrLater() {
        mockStaticAppUtil?.`when`<Int> { AppUtil.metaDataInt(anyString(), anyString()) }
            ?.thenReturn(1, 2)

        Assert.assertFalse(CardUtils.isDragonFlyVersionCode2OrLater())
        Assert.assertTrue(CardUtils.isDragonFlyVersionCode2OrLater())
    }
}