/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  RecorderWaveRecyclerView
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.record.views.wave

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.ViewGroup
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.record.R
import com.soundrecorder.wavemark.wave.WaveViewUtil
import com.soundrecorder.wavemark.wave.load.WaveSafeLinkedList
import com.soundrecorder.wavemark.wave.view.WaveAdapter
import com.soundrecorder.wavemark.wave.view.WaveRecyclerView
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.math.abs

class RecorderWaveRecyclerView(context: Context, attrs: AttributeSet? = null) :
    WaveRecyclerView<RecorderWaveItemView>(context, attrs) {

    companion object {
        const val TAG = "RecorderWaveRecyclerView"
    }

    private val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    var mIsRecording: Boolean = false

    //是否正在执行录制页进入动画
    private var mIsDrawStartAnimation = false

    //开始执行动效的时间
    private var mStartEnterAnimationTimeMillis: Long = 0
    private var mIsFromSlider = recorderViewModelApi?.isFromSlidBar()

    private var mMeterBlockGap = 0

    private var isEnhance = false //是否触发定向录音按钮

    init {
        mMeterBlockGap = context.resources.getDimensionPixelSize(com.soundrecorder.wavemark.R.dimen.vumeter_gap)
        mAmplitudeValue = WaveSafeLinkedList()
        setIsCanScrollTimeRuler(false)
    }

    override fun onInterceptTouchEvent(e: MotionEvent?): Boolean {
        if (mCanScrollHorizontally) {
            super.onInterceptTouchEvent(e)
        }
        return true
    }

    override fun onTouchEvent(e: MotionEvent): Boolean {
        checkMarkRegion(e)
        if (mCanScrollHorizontally) {
            when (e.action) {
                MotionEvent.ACTION_DOWN -> mTouchDownX = e.x
                MotionEvent.ACTION_UP -> {
                    val touchDeltaX = e.x - mTouchDownX
                    if (abs(touchDeltaX) > mSlop) {
                        DebugUtil.i(
                            TAG,
                            "onDragged invoked , mTouchDeltaX: $touchDeltaX, mSlop: $mSlop"
                        )
                        if (mDragListener != null) {
                            mDragListener.onDragged()
                        }
                    }
                }
            }
            return super.onTouchEvent(e)
        }
        return true
    }
    @SuppressLint("NotifyDataSetChanged")
    fun setEnhanceRecording(isEnhance: Boolean) {
        if (isEnhance != this.isEnhance) {
            this.isEnhance = isEnhance
            mWaveAdapter.notifyDataSetChanged()
        }
    }

    override fun createNewItemView(context: Context, parent: ViewGroup): RecorderWaveItemView {
        return LayoutInflater.from(context)
            .inflate(R.layout.item_ruler, parent, false) as RecorderWaveItemView
    }

    override fun fixItemCount(totalCount: Int): Int {
        if (mIsRecording && totalCount < WaveAdapter.MAX_COUNT) {
            return WaveAdapter.MAX_COUNT
        }

        return totalCount
    }

    override fun onItemViewCreated(parent: ViewGroup, rulerView: RecorderWaveItemView) {
        //do nothing
    }

    override fun onBindItemView(rulerView: RecorderWaveItemView, position: Int) {
        rulerView.amplitudes = mAmplitudeValue
        rulerView.mIsRecording = mIsRecording
        rulerView.mIsDrawStartAnimation = mIsDrawStartAnimation
        rulerView.setLineEnhanceRecording(isEnhance)
        rulerView.doEnterAnimationTimeMillis = mStartEnterAnimationTimeMillis
        //设置最近一次开启定向录音的时间
        rulerView.lastDirectOnTime = lastDirectOnTime
        //设置已有的定向录音时间段
        rulerView.directTime = directTime
    }

    fun startRecordEnterAnimation(currentTimeMillis: Long) {
        DebugUtil.e(TAG, "startEnterWaveAnimation... ")
        mIsDrawStartAnimation = true
        mStartEnterAnimationTimeMillis = currentTimeMillis
        notifyDataSetChanged()
    }

    fun stopEnterWaveAnimation() {
        DebugUtil.e(TAG, "stopEnterWaveAnimation... ")
        mIsDrawStartAnimation = false
        //录制的时候也会刷新波形view，所以这里不需要再次调用，否则会出现闪烁。
        notifyDataSetChanged()
    }

    /**
     * start wave timer
     */
    fun startRecordMove() {
        mIsRecording = true
    }

    /**
     * stop wave timer
     */
    fun stopRecorderMove() {
        if (mWaveAdapter != null && !mIsRecording) {
            DebugUtil.w(TAG, "stopRecorderMove, the state is not recording")
            return
        }
        DebugUtil.d(TAG, "stopRecorderMove")
        if (mAmplitudeValue != null) {
            fixAmplitude(false)
            CoroutineScope(Dispatchers.IO).launch {
                val amplitudeCurrentTime = recorderViewModelApi?.getAmplitudeCurrentTime() ?: 0
                withContext(Dispatchers.Main) {
                    setSelectTime(amplitudeCurrentTime)
                    mIsRecording = false
                    notifyDataSetChanged()
                }
            }
        }
    }

    fun fixAmplitudeWhenPause() {
        if (!mIsRecording) {
            DebugUtil.d(TAG, "fixAmplitudeWhenPause")
            fixAmplitude()
        }
    }

    private fun fixAmplitude(needNotify: Boolean = true) {
        val amplitudeList = mAmplitudeValue ?: return

        /*
         * mAmplitudeValue可能直接引用的[RecordAmplitudeModel.mAmplitudeList]
         * 此处为主线程，在此修改可能与[WaveSampleWorker]中的修改产生并发冲突，
         * 需要考虑后续从整体架构逻辑上进行优化
         */
        synchronized(amplitudeList) {
            val time = recorderViewModelApi?.getAmplitudeCurrentTime() ?: 0
            val amplitudeListSize = recorderViewModelApi?.getAmplitudeList()?.size ?: 0
            val oneWaveLineTime = WaveViewUtil.getOneWaveLineTimeByWaveType(BaseApplication.getAppContext(), mWaveType)
            val calculate = time - amplitudeListSize * oneWaveLineTime
            if (calculate >= 0) {
                DebugUtil.d(TAG, "fixAmplitude: $calculate")
                amplitudeList.add(recorderViewModelApi?.getLatestAmplitude() ?: 0)
                if (needNotify) {
                    notifyDataSetChanged()
                }
            }
        }
    }

        /**
     * update wave
     */
    @Suppress("TooGenericExceptionCaught")
    fun recorderIntervalUpdate() {
        if (mAmplitudeValue == null) {
            DebugUtil.e(TAG, "recorderIntervalUpdate, mAmplitudeValue is null")
            return
        }
        if (mMaxAmplitudeSource == null) {
            DebugUtil.e(
                TAG,
                "recorderIntervalUpdate, mMaxAmplitudeSource is null"
            )
            return
        }
        if (mIsDrawStartAnimation) {
            DebugUtil.d(
                TAG,
                "recorderIntervalUpdate, is doing enterAnimation, so return!"
            )
            return
        }

        try {
            // 此处设为true是由于侧边栏录音过程中进入录制页面，点击保存，stopRecorderMove会被拦截 不执行，导致波形显示异常
            mIsRecording = true
            mAmplitudeValue = recorderViewModelApi?.getAmplitudeList()
            startSmoothScroll(recorderViewModelApi?.getAmplitudeCurrentTime() ?: 0)
        } catch (e: Exception) {
            DebugUtil.e(TAG, e.message)
        }
    }

    override fun getDeltaScrollLength(currentTimeMillis: Long, scrolledLength: Int): Int {
        var delta = super.getDeltaScrollLength(currentTimeMillis, scrolledLength)
        if (delta >= mMeterBlockGap * WaveViewUtil.NUM_4) {
            if (false == mIsFromSlider) {
                /* why use this? because when dark mode change, first the view inflate so mFirstVisibleItem is null, should fix the dx*/
                DebugUtil.d(
                    TAG,
                    "In this case, is record mode normal, the dx may be wrong, use default value."
                )
                delta = mMeterBlockGap * WaveViewUtil.NUM_4
            } else {
                /* when from slider, the dx should fix like this*/
                DebugUtil.d(
                    TAG,
                    "In this case, is record mode slider, the dx may be wrong, use default value."
                )
                delta = mMeterBlockGap
                mIsFromSlider = false
            }
        }
        return delta
    }
}