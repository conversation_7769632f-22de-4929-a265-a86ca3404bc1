<svg width="126" height="126" viewBox="0 0 126 126" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_572_46635)">
        <g filter="url(#filter0_d_572_46635)">
            <rect width="126" height="126" rx="63" fill="url(#paint0_linear_572_46635)" shape-rendering="crispEdges"/>
            <rect x="44.1" y="44.1016" width="37.8" height="37.8" rx="6.3" fill="white"/>
        </g>
    </g>
    <defs>
        <filter id="filter0_d_572_46635" x="-33.6" y="-10.5" width="193.2" height="193.2" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="23.1"/>
            <feGaussianBlur stdDeviation="16.8"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.0941176 0 0 0 0 0.0941176 0 0 0 0.17 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_572_46635"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_572_46635" result="shape"/>
        </filter>
        <linearGradient id="paint0_linear_572_46635" x1="63" y1="-10.8281" x2="63" y2="136.828" gradientUnits="userSpaceOnUse">
            <stop stop-color="#FC695F"/>
            <stop offset="1" stop-color="#EB3B2F"/>
        </linearGradient>
        <clipPath id="clip0_572_46635">
            <rect width="126" height="126" fill="white"/>
        </clipPath>
    </defs>
</svg>
