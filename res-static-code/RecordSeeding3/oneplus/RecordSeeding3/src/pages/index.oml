<template>
    <card-template entry="notification" category="modular" onclick="cardClick">
        <compact>
            <leading>
                <lottie level="A*" src="{{$r('images.lottie_logo')}}" autoplay="{{recordInProgress}}" loop="true" voice-label="{{logoDescription}}"></lottie>
                <text level="B*"></text>
            </leading>
            <trailing>
                <text level="B0" transform-effect="none" voice-label="{{timeDescription}}">
                    <span font-family="monospace">{{ dayText }}</span>
                    <span>{{ daySeparator }}</span>
                    <span font-family="monospace">{{ hourText }}</span>
                    <span>{{ hourSeparator }}</span>
                    <span font-family="monospace">{{ minuteText }}</span>
                    <span>{{ minuteSeparator }}</span>
                    <span font-family="monospace">{{ secondText }}</span>
                </text>
            </trailing>
        </compact>

        <expanded level="F1*" bg-color="{{ cardBg }}" border-color="#40959595">
            <center category="graphic-highlight">
                <lottie level="D1*" src="{{$r('images.lottie_logo_big')}}" autoplay="{{recordInProgress}}" loop="true" voice-label="{{logoDescription}}" ></lottie>

                <text level="B1*" transform-effect="none" voice-label="{{timeDescription}}">
                    <span font-family="monospace">{{ dayText }}</span>
                    <span>{{ daySeparator }}</span>
                    <span font-family="monospace">{{ hourText }}</span>
                    <span>{{ hourSeparator }}</span>
                    <span font-family="monospace">{{ minuteText }}</span>
                    <span>{{ minuteSeparator }}</span>
                    <span font-family="monospace">{{ secondText }}</span>
                </text>
            
                <button level="E2" type="circle"
                    onclick= "recordBtnClick"
                    disabled ="{{recordDisable}}"
                    bg-color="{{recordBtnBgColor}}"
                    voice-label="{{recordBtnDescription}}"
                    icon="{{recordStateIcon}}">
                </button> 
                <button level="E1" type="circle" 
                    onclick="saveClick"
                    disabled ="{{labelDisable}}"
                    icon="{{$r('images.img_recorder_save_btn')}}"
                    voice-label="{{labelBtnDescription}}"
                    if="{{showG1}}">
                </button>
            </center>
        </expanded>
        <voice>{{ voiceLabel }}</voice>
        <image level="A1*" src="{{$r('images.logo')}}"></image>
    </card-template>
</template>
<style>
</style>
<data>
    {
        "uiData": {
            "timeDescription": "",
            "labelText": "",
            "labelDescription":"",
            "labelBtnDescription":"",
            "recordBtnDescription":"",
            "recordDisable":false,
            "recordImageOn":"",
            "recordImageOff":"",
            "transitionOn":"$r('images.lottie_pause')",
            "transitionOff":"$r('images.lottie_start')",
            "labelDisable": false,
            "labelImage": "",
            "voiceLabel": true,
            "recordInProgress": true,
            "logoDescription":"",
            "recordBtnBgColor":"",
            "markBtnIconColor":"",
            "showG1":true,
            "saveBtn": "",
            "saveTitle":"",
            "saveContent":"",
            "saveLottie":"$r('images.lottie_saving')",
            "cardClickType":"",
            "fileName":"",
            "dayText":"",
            "hourText":"",
            "minuteText":"",
            "secondText":"",
            "daySeparator":"",
            "hourSeparator":"",
            "minuteSeparator":"",
            "pageId":"",
            "cardBg":"$r('images.card_bg_color')",
            "recordStateIcon":"$r('images.paused')"
        },
        "uiEvent": {
            "recordBtnClick": {
                "type":'message',
                "uri":'com.oneplus.soundrecorder.card.event.provider',
                "method" :'pauseOrResume'
            },        
            "saveClick": {
                "type":'message',
                "uri":'com.oneplus.soundrecorder.card.event.provider',
                "method" :'saveAudio'
            },
            "cardClick": {
                "type": "{{cardClickType}}",
                "uri": 'nativeapp://oplus.intent.action.com.soundrecorder.SEEDLING_CARD',
                "params": {
                    "should_auto_find_file_name": "{{fileName}}"
                }
            } 
        }
    }
</data>
